package task

import (
	"slam/model"
	"slam/utils"

	"github.com/stretchr/testify/mock"
)

// Mock结构体定义
type MockXflowModel struct {
	mock.Mock
}

type MockIntegrationModel struct {
	mock.Mock
}

type MockIntegrationBugModel struct {
	mock.Mock
}

type MockIntegrationCveModel struct {
	mock.Mock
}

type MockIntegrationCheckModel struct {
	mock.Mock
}

type MockFuncsModel struct {
	mock.Mock
}

type MockIntegrationRpmModel struct {
	mock.Mock
}

// Mock方法实现
func (m *MockXflowModel) GetStockBugCveList(bq *model.StockListQuery, igId int) (
	[]*model.StockBugCveList, []*model.IntegrationBug,
	[]*model.IntegrationCve, error,
) {
	args := m.Called(bq, igId)
	return args.Get(0).([]*model.StockBugCveList),
		args.Get(1).([]*model.IntegrationBug),
		args.Get(2).([]*model.IntegrationCve),
		args.Error(3)
}

func (m *MockXflowModel) GetBuildInfosByXflowIds(xflowIds []int) ([]*model.BuildMapping, error) {
	args := m.Called(xflowIds)
	return args.Get(0).([]*model.BuildMapping), args.Error(1)
}
func (m *MockXflowModel) GetArchsByIds(archIds []int) map[int]string {
	args := m.Called(archIds)
	return args.Get(0).(map[int]string)
}
func (m *MockXflowModel) GetStockCenterPacksByIds(ids []int) []*model.StockCenterPackList {
	args := m.Called(ids)
	return args.Get(0).([]*model.StockCenterPackList)
}

func (m *MockXflowModel) GetEvaluatorsByXflowIds(xflowId []int) map[int][]*model.Evaluator {
	args := m.Called(xflowId)
	return args.Get(0).(map[int][]*model.Evaluator)
}

func (m *MockIntegrationModel) CreateIntegration(integration *model.Integration) (int, error) {
	args := m.Called(integration)
	return args.Int(0), args.Error(1)
}

func (m *MockIntegrationModel) GetIntegrationList(pager utils.Pager) (*model.Data_List, error) {
	args := m.Called(pager)
	return args.Get(0).(*model.Data_List), args.Error(1)
}

func (m *MockIntegrationModel) GetIntegrationById(id int) (*model.Integration, error) {
	args := m.Called(id)
	return args.Get(0).(*model.Integration), args.Error(1)
}

func (m *MockIntegrationModel) GetNotExistBuildRpmIds() ([]int, error) {
	args := m.Called()
	return args.Get(0).([]int), args.Error(1)
}

func (m *MockIntegrationBugModel) BatchInsertIntegrationBug(bugs []*model.IntegrationBug) error {
	args := m.Called(bugs)
	return args.Error(0)
}

func (m *MockIntegrationBugModel) GetIntegrationBugList(igId int, srcName string, pager utils.Pager) ([]*model.IntegrationBugWithRpms, int64, error) {
	args := m.Called(igId, srcName, pager)
	return args.Get(0).([]*model.IntegrationBugWithRpms), args.Get(1).(int64), args.Error(2)
}
func (m *MockIntegrationBugModel) GetXflowIdsByIgid(igid int) ([]int, error) {
	args := m.Called(igid)
	return args.Get(0).([]int), args.Error(1)
}

func (m *MockIntegrationCveModel) BatchInsertIntegrationCve(cves []*model.IntegrationCve) error {
	args := m.Called(cves)
	return args.Error(0)
}

func (m *MockIntegrationCveModel) GetIntegrationCveList(igId int, srcName string, pager utils.Pager) ([]*model.IntegrationCveWithRpms, int64, error) {
	args := m.Called(igId, srcName, pager)
	return args.Get(0).([]*model.IntegrationCveWithRpms), args.Get(1).(int64), args.Error(2)
}

func (m *MockIntegrationCveModel) GetXflowIdsByIgid(igid int) ([]int, error) {
	args := m.Called(igid)
	return args.Get(0).([]int), args.Error(1)
}

func (m *MockIntegrationCheckModel) GetIntegrationCheckList(checkType int, igId int) ([]*model.IntegrationCheck, error) {
	args := m.Called(checkType, igId)
	return args.Get(0).([]*model.IntegrationCheck), args.Error(1)
}

func (m *MockIntegrationCheckModel) CreateIntegrationCheck(check []*model.IntegrationCheck) error {
	args := m.Called(check)
	return args.Error(0)
}

func (m *MockFuncsModel) ProductList(ids []int) []model.ProductList {
	args := m.Called(ids)
	return args.Get(0).([]model.ProductList)
}

func (m *MockFuncsModel) GetEvaluators(ids int) []model.User {
	args := m.Called(ids)
	return args.Get(0).([]model.User)
}

func (m *MockIntegrationRpmModel) BatchInsertIntegrationBuild(rpmInfos []*model.IntegrationRpmInfo) error {
	args := m.Called(rpmInfos)
	return args.Error(0)
}

// // 测试用例
// func TestCreateIntegration(t *testing.T) {
// 	// 初始化mock对象
// 	mockXflow := new(MockXflowModel)
// 	mockIg := new(MockIntegrationModel)
// 	mockBug := new(MockIntegrationBugModel)
// 	mockCve := new(MockIntegrationCveModel)
// 	mockCheck := new(MockIntegrationCheckModel)
// 	mockFuncs := new(MockFuncsModel)

// 	// 创建Integration实例
// 	i := &Integration{
// 		xflowModel:   mockXflow,
// 		igModel:      mockIg,
// 		igBugModel:   mockBug,
// 		igCveModel:   mockCve,
// 		igCheckModel: mockCheck,
// 		funcsModel:   mockFuncs,
// 	}

// 	rows := []*model.StockBugCveList{
// 		{
// 			XflowId:  1,
// 			UserId:   1,
// 			UserName: "admin",
// 			Type:     "test",
// 			Packs:    []int{1},
// 		},
// 	}
// 	ptrid := 1
// 	// 测试用例1：正常创建
// 	t.Run("success case", func(t *testing.T) {
// 		// 准备测试数据
// 		req := &CreateIgReq{
// 			StartDate:  "2025-04-01 00:00:00",
// 			EndDate:    "2025-05-01 00:00:00",
// 			ProductIds: []int{1, 2},
// 		}

// 		// 设置mock期望
// 		mockIg.On("CreateIntegration", mock.Anything).Return(1, nil)
// 		mockXflow.On("GetStockBugCveList", mock.Anything, 1).Return(rows, []*model.IntegrationBug{{IntegrationId: 1}}, []*model.IntegrationCve{{IntegrationId: 1}}, nil)
// 		// 添加 getPacksCve 中心仓入库的逻辑
// 		mockXflow.On("GetStockCenterPacksByIds", []int{1}).Return([]*model.StockCenterPackList{{Id: &ptrid, ProdId: 1}})
// 		mockFuncs.On("ProductList", []int{1}).Return([]model.ProductList{{Id: &ptrid, ProductArchs: []int{1}}})
// 		mockXflow.On("GetArchsByIds", []int{1}).Return(map[int]string{1: "1"})
// 		mockBug.On("BatchInsertIntegrationBug", mock.Anything).Return(nil)
// 		mockCve.On("BatchInsertIntegrationCve", mock.Anything).Return(nil)

// 		// 执行测试
// 		err := i.CreateIntegration(getContextWithTestUserInfo(), req)

// 		// 验证结果
// 		assert.Nil(t, err)
// 		mockIg.AssertExpectations(t)
// 		mockXflow.AssertExpectations(t)
// 		mockBug.AssertExpectations(t)
// 		mockCve.AssertExpectations(t)
// 	})
// }

// func getContextWithTestUserInfo() *gin.Context {
// 	c := &gin.Context{}
// 	userId := 1
// 	user := model.User{Id: &userId, Nickname: "张三"}
// 	c.Set("user_info", user)
// 	return c
// }

// func TestGetIntegrationList(t *testing.T) {
// 	// 初始化mock对象和Integration实例
// 	mockIg := new(MockIntegrationModel)
// 	i := &Integration{igModel: mockIg}

// 	// 测试用例
// 	t.Run("success case", func(t *testing.T) {
// 		// 准备测试数据
// 		pager := utils.Pager{Index: 1, Size: 10}
// 		expectedResult := &model.Data_List{
// 			List: []*model.Integration{},
// 			Page: model.PageRes{Page_index: 1, Page_size: 10, Total: 0},
// 		}

// 		// 设置mock期望
// 		mockIg.On("GetIntegrationList", pager).Return(expectedResult, nil)

// 		// 执行测试
// 		result, err := i.GetIntegrationList(pager)

// 		// 验证结果
// 		assert.Nil(t, err)
// 		assert.Equal(t, expectedResult, result)
// 		mockIg.AssertExpectations(t)
// 	})
// }

// func TestGetIntegrationBugList(t *testing.T) {
// 	// 初始化mock对象
// 	mockBug := new(MockIntegrationBugModel)
// 	mockCheck := new(MockIntegrationCheckModel)
// 	mockFuncs := new(MockFuncsModel)

// 	// 创建Integration实例
// 	i := &Integration{
// 		igBugModel:   mockBug,
// 		igCheckModel: mockCheck,
// 		funcsModel:   mockFuncs,
// 	}

// 	// 测试用例1：正常获取列表
// 	t.Run("success case", func(t *testing.T) {
// 		// 准备测试数据
// 		igid := 1
// 		pager := utils.Pager{Index: 1, Size: 10}
// 		c := getContextWithTestUserInfo()
// 		uid := 1

// 		// 模拟数据
// 		bugs := []*model.IntegrationBugWithRpms{
// 			{
// 				IntegrationBug: model.IntegrationBug{
// 					Id:        1,
// 					ProductId: 1,
// 					ZentaoNo:  "001",
// 				},
// 			},
// 		}
// 		checks := []*model.IntegrationCheck{
// 			{
// 				Identifier:  1,
// 				CheckResult: 1,
// 			},
// 		}
// 		products := []model.ProductList{
// 			{
// 				Id:                  &uid,
// 				TechnicalManager:    1,
// 				ArchitectureManager: 1,
// 			},
// 		}

// 		// 设置mock期望
// 		mockBug.On("GetIntegrationBugList", igid, "", pager).Return(bugs, int64(1), nil)
// 		mockCheck.On("GetIntegrationCheckList", model.IntegrationCheckTypeBug, igid).Return(checks, nil)
// 		mockFuncs.On("ProductList", []int{1}).Return(products)

// 		// 执行测试
// 		result, err := i.GetIntegrationBugList(c, igid, "", pager)

// 		// 验证结果
// 		assert.Nil(t, err)
// 		assert.NotNil(t, result)
// 		assert.Equal(t, 1, len(result.List.([]*BugList)))
// 		mockBug.AssertExpectations(t)
// 		mockCheck.AssertExpectations(t)
// 		mockFuncs.AssertExpectations(t)
// 	})
// }

// func TestGetIntegrationCveList(t *testing.T) {
// 	// 初始化mock对象
// 	mockCve := new(MockIntegrationCveModel)
// 	mockCheck := new(MockIntegrationCheckModel)
// 	mockFuncs := new(MockFuncsModel)

// 	// 创建Integration实例
// 	i := &Integration{
// 		igCveModel:   mockCve,
// 		igCheckModel: mockCheck,
// 		funcsModel:   mockFuncs,
// 	}

// 	// 测试用例1：正常获取列表
// 	t.Run("success case", func(t *testing.T) {
// 		// 准备测试数据
// 		igid := 1
// 		pager := utils.Pager{Index: 1, Size: 10}
// 		c := getContextWithTestUserInfo()
// 		uid := 1

// 		// 模拟数据
// 		cves := []*model.IntegrationCveWithRpms{
// 			{
// 				IntegrationCve: model.IntegrationCve{
// 					Id:        1,
// 					ProductId: 1,
// 					CveId:     "CVE-2025-001",
// 				},
// 			},
// 		}
// 		checks := []*model.IntegrationCheck{
// 			{
// 				Identifier:  1,
// 				CheckResult: 1,
// 			},
// 		}
// 		products := []model.ProductList{
// 			{
// 				Id:                  &uid,
// 				TechnicalManager:    1,
// 				ArchitectureManager: 1,
// 			},
// 		}

// 		// 设置mock期望
// 		mockCve.On("GetIntegrationCveList", igid, "", pager).Return(cves, int64(1), nil)
// 		mockCheck.On("GetIntegrationCheckList", model.IntegrationCheckTypeCve, igid).Return(checks, nil)
// 		mockFuncs.On("ProductList", []int{1}).Return(products)

// 		// 执行测试
// 		result, err := i.GetIntegrationCveList(c, igid, "", pager)

// 		// 验证结果
// 		assert.Nil(t, err)
// 		assert.NotNil(t, result)
// 		assert.Equal(t, 1, len(result.List.([]*CveList)))
// 		mockCve.AssertExpectations(t)
// 		mockCheck.AssertExpectations(t)
// 		mockFuncs.AssertExpectations(t)
// 	})
// }

// func TestGetIntegrationById(t *testing.T) {
// 	// 初始化mock对象
// 	mockIg := new(MockIntegrationModel)

// 	// 创建Integration实例
// 	i := &Integration{igModel: mockIg}

// 	// 测试用例1：正常获取详情
// 	t.Run("success case", func(t *testing.T) {
// 		// 准备测试数据
// 		igid := 1
// 		expectedResult := &model.Integration{
// 			Id: igid,
// 		}

// 		// 设置mock期望
// 		mockIg.On("GetIntegrationById", igid).Return(expectedResult, nil)

// 		// 执行测试
// 		result, err := i.GetIntegrationById(igid)

// 		// 验证结果
// 		assert.Nil(t, err)
// 		assert.Equal(t, expectedResult, result)
// 		mockIg.AssertExpectations(t)
// 	})
// }

// func TestCheckIntegrationFirst(t *testing.T) {
// 	// 初始化mock对象
// 	mockCheck := new(MockIntegrationCheckModel)

// 	// 创建Integration实例
// 	i := &Integration{igCheckModel: mockCheck}

// 	// 测试用例
// 	t.Run("success case", func(t *testing.T) {
// 		// 准备测试数据
// 		c := getContextWithTestUserInfo()
// 		req := &CheckReq{
// 			Ids:         []int{1},
// 			IgId:        1,
// 			CheckResult: 1,
// 			CheckAdvice: "通过",
// 			CheckType:   1,
// 		}

// 		// 设置mock期望
// 		mockCheck.On("CreateIntegrationCheck", mock.Anything).Return(nil)

// 		// 执行测试
// 		i.CheckIntegrationFirst(c, req)

// 		// 验证结果
// 		mockCheck.AssertExpectations(t)
// 	})
// }

// func TestCheckIntegrationSecond(t *testing.T) {
// 	// 初始化mock对象
// 	mockCheck := new(MockIntegrationCheckModel)

// 	// 创建Integration实例
// 	i := &Integration{igCheckModel: mockCheck}

// 	// 测试用例
// 	t.Run("success case", func(t *testing.T) {
// 		// 准备测试数据
// 		c := getContextWithTestUserInfo()
// 		req := &CheckReq{
// 			Ids:         []int{1},
// 			IgId:        1,
// 			CheckResult: 1,
// 			CheckAdvice: "通过",
// 			CheckType:   1,
// 		}

// 		// 设置mock期望
// 		mockCheck.On("CreateIntegrationCheck", mock.Anything).Return(nil)

// 		// 执行测试
// 		i.CheckIntegrationSecond(c, req)

// 		// 验证结果
// 		mockCheck.AssertExpectations(t)
// 	})
// }

// func TestGetNotExistBuildRpmIds(t *testing.T) {
// 	NewIntegration()
// 	mockIg := new(MockIntegrationModel)

// 	// 创建Integration实例
// 	i := &Integration{igModel: mockIg}

// 	mockIg.On("GetNotExistBuildRpmIds").Return([]int{}, nil)
// 	i.GetNotExistBuildRpmIds()
// 	mockIg.AssertExpectations(t)
// }

// func TestFlushBuildInfo(t *testing.T) {
// 	// 初始化mock对象
// 	mockBug := new(MockIntegrationBugModel)
// 	mockCve := new(MockIntegrationCveModel)
// 	mockXflow := new(MockXflowModel)
// 	mockRpm := new(MockIntegrationRpmModel)

// 	// 创建Integration实例
// 	i := &Integration{
// 		igBugModel: mockBug,
// 		igCveModel: mockCve,
// 		xflowModel: mockXflow,
// 		igRpmModel: mockRpm,
// 	}

// 	// 测试用例1：正常场景
// 	t.Run("success case", func(t *testing.T) {
// 		// 准备测试数据
// 		igid := 1
// 		xflowIdsForBug := []int{21037}
// 		xflowIdsForCve := []int{}
// 		buildMappings := []*model.BuildMapping{{
// 			XflowId: 21037, SrcName: "php-8.0.30-9.ky10h.src.rpm",
// 			IpAddress: "http://************:8238",
// 		}}

// 		// 设置mock期望
// 		mockBug.On("GetXflowIdsByIgid", igid).Return(xflowIdsForBug, nil)
// 		mockCve.On("GetXflowIdsByIgid", igid).Return(xflowIdsForCve, nil)
// 		mockXflow.On("GetBuildInfosByXflowIds", mock.Anything).Return(buildMappings, nil)
// 		mockRpm.On("BatchInsertIntegrationBuild", mock.Anything).Return(nil)

// 		// 执行测试
// 		i.FlushBuildInfo(igid)

// 		// 验证结果
// 		mockBug.AssertExpectations(t)
// 		mockCve.AssertExpectations(t)
// 		mockXflow.AssertExpectations(t)
// 		mockRpm.AssertExpectations(t)
// 	})

// 	// 测试用例2：空列表场景
// 	t.Run("empty list case", func(t *testing.T) {
// 		// 准备测试数据
// 		igid := 1

// 		// 设置mock期望
// 		mockBug.On("GetXflowIdsByIgid", igid).Return([]int{}, nil)
// 		mockCve.On("GetXflowIdsByIgid", igid).Return([]int{}, nil)

// 		// 执行测试
// 		i.FlushBuildInfo(igid)

// 		// 验证结果
// 		mockBug.AssertExpectations(t)
// 		mockCve.AssertExpectations(t)
// 	})
// }

// func TestOrderFuncs(t *testing.T) {
// 	i := NewIntegration()
// 	data := &model.Data_List{
// 		List: []*BugList{},
// 		Page: model.PageRes{
// 			Page_index: 1,
// 			Page_size:  1,
// 			Total:      1,
// 		},
// 	}
// 	i.getBugSheetConfig(data)
// 	i.getCveSheetConfig(data)
// }

// func TestProcessRpmAndEvaluatorsInfo(t *testing.T) {
// 	// 测试用例1：RPM和评估人信息都有
// 	t.Run("success case", func(t *testing.T) {
// 		// 创建IntegrationExtends实例
// 		iExtends := &IntegrationExtends{}
// 		// 准备测试数据
// 		item := model.IntegrationBugWithRpms{
// 			Rpms: []*model.Rpm{
// 				{SrcName: "package1", Sha256: "sha256-1"},
// 				{SrcName: "package2", Sha256: "sha256-2"},
// 			},
// 			IntegrationBug: model.IntegrationBug{
// 				XflowId: 1,
// 			},
// 		}
// 		evaluatorsMap := map[int][]*model.Evaluator{
// 			1: {
// 				{Nickname: "张三", Email: "<EMAIL>"},
// 				{Nickname: "李四", Email: "<EMAIL>"},
// 			},
// 		}

// 		// 执行测试
// 		processRpmAndEvaluatorsInfo(iExtends, item, evaluatorsMap)
// 		// 验证结果
// 		expectedRpmString := "软件包：package1[sha256:sha256-1]\n软件包：package2[sha256:sha256-2]"
// 		expectedEvaluatorString := "评估人：张三[<EMAIL>]\n评估人：李四[<EMAIL>]"
// 		assert.Equal(t, expectedRpmString, iExtends.RmpsToString)
// 		assert.Equal(t, expectedEvaluatorString, iExtends.EvaluatorsToString)
// 	})

// 	// 测试用例2：无RPM和评估人信息
// 	t.Run("success case2", func(t *testing.T) {
// 		// 创建IntegrationExtends实例
// 		iExtends := &IntegrationExtends{}
// 		// 准备测试数据
// 		item := model.IntegrationBugWithRpms{}

// 		// 执行测试
// 		processRpmAndEvaluatorsInfo(iExtends, item, map[int][]*model.Evaluator{})

// 		// 验证结果
// 		assert.Equal(t, "", iExtends.RmpsToString)
// 		assert.Equal(t, "", iExtends.EvaluatorsToString)
// 	})
// }

// func TestDownload(t *testing.T) {
// 	mockBug := new(MockIntegrationBugModel)
// 	mockCve := new(MockIntegrationCveModel)
// 	mockXflow := new(MockXflowModel)
// 	mockRpm := new(MockIntegrationRpmModel)

// 	// 创建Integration实例
// 	i := &Integration{
// 		igBugModel: mockBug,
// 		igCveModel: mockCve,
// 		xflowModel: mockXflow,
// 		igRpmModel: mockRpm,
// 	}
// 	uid := 1
// 	user := model.User{
// 		Id:       &uid,
// 		Nickname: "张三",
// 	}
// 	// 执行测试
// 	c := &gin.Context{}
// 	c.Set("user_info", user)

// 	i.ExportIntegration(c, 0)
// }

// func TestProcessCheckInfo(t *testing.T) {
// 	//processRpmAndEvaluatorsInfo
// 	bug := model.IntegrationBug{}
// 	bugWithRpm := &model.IntegrationBugWithRpms{
// 		IntegrationBug: model.IntegrationBug{Id: 1, XflowId: 1},
// 		Rpms:           []*model.Rpm{{SrcName: "srcname", Sha256: "sha256"}},
// 	}
// 	bug.Id = 1
// 	checks := []*model.IntegrationCheck{
// 		{
// 			Identifier: 1,
// 			CheckTimes: 1,
// 		},
// 		{
// 			Identifier: 1,
// 			CheckTimes: 2,
// 		},
// 	}

// 	processCheckInfo(bugWithRpm, checks)
// 	extengs := &IntegrationExtends{}
// 	evalutors := map[int][]*model.Evaluator{
// 		1: {{Nickname: "nicknake", Email: "email"}},
// 	}
// 	processRpmAndEvaluatorsInfo(extengs, bugWithRpm, evalutors)
// }

// func TestXxx(t *testing.T) {
// 	// model.InitDb()
// 	// defer model.CloseDb()
// 	// db := model.GetDB()
// 	// db.AutoMigrate(&model.Integration{})
// 	// db.AutoMigrate(&model.IntegrationCheck{})
// 	// db.AutoMigrate(&model.IntegrationBug{})
// 	// db.AutoMigrate(&model.IntegrationCve{})
// 	// req := &CreateIgReq{
// 	// 	StartDate:  "2025-04-01 00:00:00",
// 	// 	EndDate:    "2025-04-03 00:00:00",
// 	// 	ProductIds: []int{1, 2},
// 	// }
// 	// uid := 1
// 	// user := model.User{
// 	// 	Id:       &uid,
// 	// 	Nickname: "张三",
// 	// }
// 	// i := NewIntegration()
// 	// 执行测试
// 	// c := &gin.Context{}
// 	// c.Set("user_info", user)

// 	// i.CreateIntegration(c, req)
// 	// i.getProductList([]int{73, 15, 66, 69})
// 	// pager := utils.NewPager(1, 10)
// 	// res, _ := i.GetIntegrationCveList(c, 20, pager)

// 	// list := res.List.([]*CveList)
// 	// for _, v := range list {
// 	// 	fmt.Println(v.SrcName)
// 	// 	fmt.Println(v.Rpms)
// 	// 	fmt.Println()
// 	// }
// 	// fmt.Println(res)

// 	// i.FlushBuildInfo(21)
// 	// i.ExportIntegration(c, 20)
// }
