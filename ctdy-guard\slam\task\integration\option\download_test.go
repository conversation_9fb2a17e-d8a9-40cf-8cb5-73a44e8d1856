package option

import (
	"slam/model"
	"slam/utils"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
)

func TestDownloadOptionExecute(t *testing.T) {
	// Setup
	gin.SetMode(gin.TestMode)

	t.Run("successful download execution", func(t *testing.T) {
		// Mocks
		mockBugModel := new(MockIntegrationRowsRepo)
		mockCveModel := new(MockIntegrationRowsRepo)
		mockCheckModel := new(MockIntegrationCheckRepo)

		// Test data
		igId := 1
		opUser := &OpUser{
			UserInfo: UserInfo{
				UserId:   1,
				UserName: "testuser",
				Email:    "<EMAIL>",
			},
		}

		// Mock expectations
		// For bug sheet
		mockBugModel.On("GetIntegrationList", &model.PacksListReq{IgId: igId}, utils.Pager{Index: 1, Size: 10000}).Return(
			[]*model.IntegrationBug{{Id: 1, SrcName: "test-bug-package"}},
			int64(1),
			nil,
		)

		// For cve sheet
		mockCveModel.On("GetIntegrationList", &model.PacksListReq{IgId: igId}, utils.Pager{Index: 1, Size: 10000}).Return(
			[]*model.IntegrationCve{{Id: 1, SrcName: "test-cve-package"}},
			int64(1),
			nil,
		)

		// For bug check records
		mockCheckModel.On("GetCheckListByIgId", igId, model.IntegrationCheckTypeBug).Return(
			[]*model.IntegrationCheck{{Identifier: 1, CheckerName: "checker1"}},
			nil,
		)

		// For cve check records
		mockCheckModel.On("GetCheckListByIgId", igId, model.IntegrationCheckTypeCve).Return(
			[]*model.IntegrationCheck{{Identifier: 1, CheckerName: "checker2"}},
			nil,
		)

		// Create DownloadOption instance with mocks
		downloadOption := &DownloadOption{
			BaseOption: BaseOption{
				ID:   "download",
				Name: "下载",
			},
			igBugModel:   mockBugModel,
			igCveModel:   mockCveModel,
			igCheckModel: mockCheckModel,
		}

		// Execute
		ctx, _ := gin.CreateTestContext(nil)
		result, err := downloadOption.Execute(ctx, opUser, igId)

		// Assertions
		assert.NoError(t, err)
		assert.NotNil(t, result)
		mockBugModel.AssertExpectations(t)
		mockCveModel.AssertExpectations(t)
		mockCheckModel.AssertExpectations(t)
	})
}
