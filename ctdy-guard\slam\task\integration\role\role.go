/*
 * @Author: lwq <EMAIL>
 * @Date: 2025-07-08
 * @LastEditors: lwq <EMAIL>
 * @LastEditTime: 2025-07-30
 * @FilePath: /ctdy-guard/slam/task/integration/role/role.go
 * @Description: 角色接口定义 集合各个角色，主要设置每个角色的权限
 * @Copyright (c) 2025 by 2022.kylinos.cn, All Rights Reserved.
 */
package role

import (
	"slam/task/integration/option"
	"slam/utils"
)

const (
	RepoManagerRoleID = iota + 1
	TechManagerRoleID
	ProjectManagerRoleID
	ArchitectRoleID
)

// Role 定义角色接口
type RoleInterface interface {
	// GetID 获取角色ID
	GetID() int
	// GetName 获取角色名称
	GetName() string
	// CheckAccess 获取允许的操作列表
	CheckAccess(optionID int) bool
}

type IgUser struct {
	UserId   int    // 用户ID
	UserName string // 用户名
	RoleId   int    // 角色ID
	RoleName string // 角色名称
	Email    string // 用户邮箱
}

// BaseRole 基础角色实现
type BaseRole struct {
	ID             int
	Name           string
	AllowedOptions []int
}

func (r *BaseRole) GetID() int {
	return r.ID
}

func (r *BaseRole) GetName() string {
	return r.Name
}

func (r *BaseRole) CheckAccess(optionID int) bool {
	return utils.ExistSliceInt(optionID, r.AllowedOptions)
}

func GetRoles() map[int]RoleInterface {
	roles := make(map[int]RoleInterface)
	roles[ArchitectRoleID] = NewArchitectRole()
	roles[TechManagerRoleID] = NewTechManagerRole()
	roles[ProjectManagerRoleID] = NewProjectManagerRole()
	roles[RepoManagerRoleID] = NewRepoManagerRole()
	return roles
}

// ArchitectRole 架构负责人角色
type ArchitectRole struct {
	BaseRole
}

func NewArchitectRole() *ArchitectRole {
	return &ArchitectRole{
		BaseRole: BaseRole{
			ID:   ArchitectRoleID,
			Name: "架构负责人",
			AllowedOptions: []int{
				option.CreateOptionID,
				option.ReviewOptionID,
				option.UpdateOptionID,
				option.DownloadOptionID,
			},
		},
	}
}

// TechManagerRole 技术经理角色
type TechManagerRole struct {
	BaseRole
}

func NewTechManagerRole() *TechManagerRole {
	return &TechManagerRole{
		BaseRole: BaseRole{
			ID:   TechManagerRoleID,
			Name: "技术经理",
			AllowedOptions: []int{
				option.CheckOptionID,
				option.DownloadOptionID,
				option.UpdateOptionID,
			},
		},
	}
}

// ProjectManagerRole 项目经理角色
type ProjectManagerRole struct {
	BaseRole
}

func NewProjectManagerRole() *ProjectManagerRole {
	return &ProjectManagerRole{
		BaseRole: BaseRole{
			ID:   ProjectManagerRoleID,
			Name: "项目经理",
			AllowedOptions: []int{
				option.ReCheckOptionID,
				option.DownloadOptionID,
				option.UpdateOptionID,
				option.ConfirmOptionID,
			},
		},
	}
}

// RepoManagerRole 仓库管理员角色
type RepoManagerRole struct {
	BaseRole
}

func NewRepoManagerRole() *RepoManagerRole {
	return &RepoManagerRole{
		BaseRole: BaseRole{
			ID:   RepoManagerRoleID,
			Name: "仓库管理员",
			AllowedOptions: []int{
				// option.CreateOptionID,
				// option.UpdateOptionID,
				// option.RotateOptionID,
				// option.AddOptionId,
				// option.RemoveOptionId,
				// option.ReplenishOptionID,
				// option.TestResultOptionID,
				// option.ReleaseOptionID,
				// option.DownloadOptionID,
				// option.FlagOptionID,
				// 暂时给库管所有权限
				option.CreateOptionID,
				option.UpdateOptionID,
				option.ReviewOptionID,
				option.ReplenishOptionID,
				option.AddOptionId,
				option.RemoveOptionId,
				option.CheckOptionID,
				option.TestResultOptionID,
				option.ReCheckOptionID,
				option.RotateOptionID,
				option.DownloadOptionID,
				option.LockOptionID,
				option.UnlockOptionID,
				option.FlagOptionID,
				option.ConfirmOptionID,
			},
		},
	}
}
