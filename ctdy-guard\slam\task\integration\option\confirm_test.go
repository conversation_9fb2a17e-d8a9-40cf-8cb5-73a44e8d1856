package option

import (
	"slam/model"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
)

func TestConfirmOptionExecute(t *testing.T) {
	NewConfirmOption()
	gin.SetMode(gin.TestMode)

	t.Run("successful confirmation", func(t *testing.T) {
		// Mocks
		mockIgModel := new(MockIntegrationRepo)
		mockCountModel := new(MockIntegrationCountRepo)

		// Test data
		opUser := &OpUser{
			UserInfo: UserInfo{
				UserId:   1,
				UserName: "testuser",
				Email:    "<EMAIL>",
			},
			OpIgInfo: &model.Integration{
				Id:     10,
				Status: 1,
			},
		}

		// Mock expectations
		mockCountModel.On("GetIntegrationCount", 10, 1).Return(&model.IntegrationCount{
			TotalBugCount: 5,
			TotalCveCount: 3,
		}, nil)

		mockCountModel.On("UpdateCountInfo", 10, 1, map[string]any{
			"review_bug_count": 5,
			"review_cve_count": 3,
		}).Return(nil)

		// Create ConfirmOption instance
		confirmOption := &ConfirmOption{
			BaseOption: BaseOption{
				ID:   "confirm",
				Name: "确认集成单",
			},
			igModel:      mockIgModel,
			igCountModel: mockCountModel,
		}

		// Execute
		ctx, _ := gin.CreateTestContext(nil)
		result, err := confirmOption.Execute(ctx, opUser, nil)

		// Assertions
		assert.NoError(t, err)
		assert.Nil(t, result)
		mockCountModel.AssertExpectations(t)
	})
}
