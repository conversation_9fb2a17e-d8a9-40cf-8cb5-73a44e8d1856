package model

import "time"

type KojiList struct {
	Id              *int      `gorm:"column:id;primaryKey;autoIncrement:true;type:serial4;not null" json:"id,string,omitempty" label:"序号，主键"`
	Xflow_id        int       `gorm:"column:xflow_id" json:"xflow_id"`
	Type            string    `gorm:"column:type" json:"type"`
	Ip              string    `gorm:"column:ip" json:"ip"`
	Task_id         string    `gorm:"column:task_id;not null" json:"task_id"`
	Src_name        string    `gorm:"column:src_name" json:"src_name"`
	Src_name_origin string    `gorm:"column:src_name_origin" json:"src_name_origin"`
	Tag             int       `gorm:"column:tag" json:"tag"`
	Url             string    `gorm:"column:url" json:"url"`
	Status          string    `gorm:"column:status" json:"status"`
	Stock_koji      int       `gorm:"column:stock_koji" json:"stock_koji"`
	CmdOption       []string  `gorm:"column:cmd_option;serializer:json" json:"cmd_option"`
	CreatedAt       time.Time `gorm:"column:create_time" json:"create_time,omitempty" label:"创建时间"`
	UpdatedAt       time.Time `gorm:"column:update_time" json:"update_time,omitempty" label:"更新时间"`
}

func (KojiList) TableName() string {
	return "slam_kojilist"
}

func GetkojiByXflowIdAndKojiIpAndSrpmName(xflowId int, kojiIp, srpmName string) string {
	var kojiList []KojiList
	err := DB.Table("slam_kojilist").Where("xflow_id = ? and ip = ? and src_name = ?", xflowId, kojiIp, srpmName).Find(&kojiList).Error
	if err != nil {
		return ""
	}
	if len(kojiList) > 1 {
		var taskId string
		err = DB.Table("slam_kojilist").Where("xflow_id = ? and ip = ? and src_name = ? and type = ?", xflowId, kojiIp, srpmName, "task").Pluck("task_id", &taskId).Error
		if err != nil {
			return ""
		}
		return taskId
	}
	return ""
}

type KojiPlatform struct {
	Id        *int      `gorm:"column:id;primaryKey;autoIncrement:true;type:serial4;not null" json:"id,string,omitempty" label:"序号，主键"`
	Addr      string    `gorm:"column:addr" json:"addr" label:"平台地址:IP+PORT"`
	Arch      []int     `gorm:"column:arch;serializer:json" json:"arch" label:"涵盖架构"`
	CreatedAt time.Time `gorm:"column:create_time" json:"create_time,omitempty" label:"创建时间"`
	UpdatedAt time.Time `gorm:"column:update_time" json:"update_time,omitempty" label:"更新时间"`
}

func (KojiPlatform) TableName() string {
	return "slam_koji_platform"
}

func GetKojiAddrList() ([]string, error) {
	var kojiAddrList []string
	err = DB.Model(&KojiPlatform{}).Where("addr != ?", "http://************:80").Select("addr").Find(&kojiAddrList).Error
	return kojiAddrList, err
}
