/*
 * @Author: lwq <EMAIL>
 * @Date: 2025-07-08
 * @LastEditors: lwq <EMAIL>
 * @LastEditTime: 2025-07-14
 * @FilePath: /ctdy-guard/slam/task/integration/option/update.go
 * @Description: 集成操作
 * @Copyright (c) 2025 by 2022.kylinos.cn, All Rights Reserved.
 */
package option

import (
	"slam/model"

	"github.com/gin-gonic/gin"
)

// UpdateOption 更新集成单名称和备注操作
type UpdateOption struct {
	BaseOption
	igModel model.IntegrationRepo // 集成单模型
}

type EditIgReq struct {
	IgId   int
	IgName string
	Remark string
}

func NewUpdateOption() *UpdateOption {
	return &UpdateOption{
		BaseOption: BaseOption{
			ID:   "update",
			Name: "更新集成单",
		},
		igModel: model.NewIntegrationModel(),
	}
}

func (o *UpdateOption) Execute(c *gin.Context, opUser *OpUser, params any) (any, error) {
	op := params.(*EditIgReq)

	ig := &model.Integration{
		Id:        op.IgId,
		AliasName: op.IgName,
		Remark:    op.Remark,
	}
	return nil, o.igModel.UpdateIntegration(ig)
}
