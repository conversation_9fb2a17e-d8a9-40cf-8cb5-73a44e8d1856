/*
 * @Author: lwq <EMAIL>
 * @Date: 2025-07-08
 * @LastEditors: lwq <EMAIL>
 * @LastEditTime: 2025-07-14
 * @FilePath: /ctdy-guard/slam/task/integration/option/lock.go
 * @Description: 集成操作
 * @Copyright (c) 2025 by 2022.kylinos.cn, All Rights Reserved.
 */
package option

import (
	"slam/global"
	"slam/model"
	"slam/slog"

	"github.com/gin-gonic/gin"
)

// LockOption 加锁操作
type LockOption struct {
	BaseOption
	igModel model.IntegrationRepo // 集成单模型
}

func NewLockOption() *LockOption {
	return &LockOption{
		BaseOption: BaseOption{
			ID:   "lock",
			Name: "加锁",
		},
		igModel: model.NewIntegrationModel(),
	}
}

func (o *LockOption) Execute(c *gin.Context, opUser *OpUser, params any) (any, error) {
	igInfo := opUser.OpIgInfo
	// 执行流转操作,把当前集成单状态流转到下一个状态，目前可以直接状态值加1
	igInfo.LockStatus = global.INTEGRATION_LOCKED

	if err := o.igModel.UpdateIntegration(igInfo); err != nil {
		slog.ErrorWithContext(c, "更新集成单状态失败", err)
		return nil, err
	}
	return nil, nil
}
