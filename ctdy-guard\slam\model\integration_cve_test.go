package model

import (
	"regexp"
	"slam/global"
	"slam/utils"
	"testing"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/stretchr/testify/assert"
)

func TestIntegrationCveWithRpms(t *testing.T) {
	i := &IntegrationCveWithRpms{}
	i.GetID()
	i.GetXflowID()
	i.GetProductID()
	i.GetRpms()
}

func TestIntegrationCveModelBatchInsertIntegration(t *testing.T) {
	assert := assert.New(t)

	// 初始化测试数据库
	gormDB, tmock, err := initSqlmock()
	assert.NoError(err)

	model := &integrationCveModel{db: gormDB}

	t.Run("success case", func(t *testing.T) {
		// 设置SQL预期
		tmock.ExpectBegin()
		tmock.ExpectQuery(regexp.QuoteMeta(`INSERT INTO "slam_integration_cve" ("integration_id","xflow_id","cve_id","submiter_id","submiter_name","src_name","product_id","arch_info","task_type","level","fix_info","check_res","is_publish","source","is_del","create_time","update_time") VALUES ($1,$2,$3,$4,$5,$6,$7,$8,$9,$10,$11,$12,$13,$14,$15,$16,$17) RETURNING "id"`)).
			WithArgs(1, 101, "CVE-2025-001", 1001, "submiter1", "package1", 1, "x86_64", "cvefix", "high", "fix info", 0, 1, 1, 0, sqlmock.AnyArg(), sqlmock.AnyArg()).
			WillReturnRows(sqlmock.NewRows([]string{"id"}).AddRow(1))
		tmock.ExpectCommit()

		// 执行测试
		cves := []*IntegrationCve{
			{
				IntegrationId: 1,
				XflowId:       101,
				CveId:         "CVE-2025-001",
				SubmiterId:    1001,
				SubmiterName:  "submiter1",
				SrcName:       "package1",
				ProductId:     1,
				ArchInfo:      "x86_64",
				TaskType:      "cvefix",
				Level:         "high",
				FixInfo:       "fix info",
				CheckRes:      0,
				IsPublish:     1,
				Source:        1,
				IsDel:         0,
			},
		}
		err := model.BatchInsertIntegration(cves)

		// 验证结果
		assert.NoError(err)
		assert.NoError(tmock.ExpectationsWereMet())
	})

	t.Run("wrong type case", func(t *testing.T) {
		err := model.BatchInsertIntegration("not a slice")
		assert.Error(err)
		assert.Equal("rows类型断言失败", err.Error())
	})
}

func TestIntegrationCveModelGetIntegrationList(t *testing.T) {
	assert := assert.New(t)

	// 初始化测试数据库
	gormDB, tmock, err := initSqlmock()
	assert.NoError(err)

	model := &integrationCveModel{db: gormDB}

	// 测试用例1: 基本查询
	t.Run("BasicQuery", func(t *testing.T) {
		req := &PacksListReq{
			IgId: 1,
		}
		pager := utils.Pager{
			Index: 1,
			Size:  10,
		}

		// 期望的计数查询
		tmock.ExpectQuery(regexp.QuoteMeta(`SELECT count(*) FROM slam_integration_cve as ic LEFT JOIN slam_integration_rpm as ir ON ic.xflow_id = ir.xflow_id AND ic.src_name = ir.src_name AND ic.arch_info = ir.arch_info LEFT JOIN slam_stockcenterlist as cl ON ic.xflow_id = cl.xflow_id WHERE ic.integration_id = $1 AND ic.is_del = $2`)).
			WithArgs(1, 0).
			WillReturnRows(tmock.NewRows([]string{"count"}).AddRow(1))

		// 期望的SQL查询 - 修正SQL语句，添加缺失的空格
		tmock.ExpectQuery(regexp.QuoteMeta(`SELECT ic.*, COALESCE(cl.stock_type, '') as stock_type, json_agg(CASE WHEN ir.nvr IS NOT NULL THEN json_build_object('src_name', concat_ws('.', ir.nvr, ir.arch_info, 'rpm'), 'signd_sha256', ir.signd_sha256) ELSE NULL END) FILTER (WHERE ir.nvr IS NOT NULL) as rpms FROM slam_integration_cve as ic LEFT JOIN slam_integration_rpm as ir ON ic.xflow_id = ir.xflow_id AND ic.src_name = ir.src_name AND ic.arch_info = ir.arch_info LEFT JOIN slam_stockcenterlist as cl ON ic.xflow_id = cl.xflow_id WHERE ic.integration_id = $1 AND ic.is_del = $2 GROUP BY ic.id, ic.src_name, cl.stock_type ORDER BY ic.id desc LIMIT 10`)).
			WithArgs(1, 0).
			WillReturnRows(tmock.NewRows([]string{"id", "integration_id", "xflow_id", "src_name", "stock_type"}).
				AddRow(1, 1, 1001, "test-package", "stock"))

		result, total, err := model.GetIntegrationList(req, pager)

		assert.NoError(err)
		assert.Equal(int64(1), total)
		assert.NotNil(result)
		assert.NoError(tmock.ExpectationsWereMet())
	})
}

func TestIntegrationCveModelGetXflowIdsByIgid(t *testing.T) {
	assert := assert.New(t)

	// 初始化测试数据库
	gormDB, tmock, err := initSqlmock()
	assert.NoError(err)

	model := &integrationCveModel{db: gormDB}

	t.Run("GetXflowIdsByIgid_Success", func(t *testing.T) {
		// 设置SQL预期
		tmock.ExpectQuery(regexp.QuoteMeta(`SELECT DISTINCT "xflow_id" FROM "slam_integration_cve" WHERE integration_id = $1`)).
			WithArgs(1).
			WillReturnRows(sqlmock.NewRows([]string{"xflow_id"}).
				AddRow(1001).
				AddRow(1002).
				AddRow(1003))

		// 执行测试
		xflowIds, err := model.GetXflowIdsByIgid(1)

		// 验证结果
		assert.NoError(err)
		assert.Equal([]int{1001, 1002, 1003}, xflowIds)
		assert.NoError(tmock.ExpectationsWereMet())
	})
}

func TestIntegrationCveModelBatchUpdateWithMapByIgid(t *testing.T) {
	assert := assert.New(t)

	// 初始化测试数据库
	gormDB, tmock, err := initSqlmock()
	assert.NoError(err)

	model := &integrationCveModel{db: gormDB}

	t.Run("success case", func(t *testing.T) {
		// 设置SQL预期
		tmock.ExpectBegin()
		tmock.ExpectExec(regexp.QuoteMeta(`UPDATE "slam_integration_cve" SET "is_publish"=$1 WHERE integration_id = $2`)).
			WithArgs(2, 1).
			WillReturnResult(sqlmock.NewResult(0, 2))
		tmock.ExpectCommit()

		// 执行测试
		umap := map[string]any{
			"is_publish": 2,
		}
		err := model.BatchUpdateWithMapByIgid(1, umap)

		// 验证结果
		assert.NoError(err)
		assert.NoError(tmock.ExpectationsWereMet())
	})
}

func TestIntegrationCveModelUpdateWithMap(t *testing.T) {
	assert := assert.New(t)

	// 初始化测试数据库
	gormDB, tmock, err := initSqlmock()
	assert.NoError(err)

	model := &integrationCveModel{db: gormDB}

	t.Run("success case", func(t *testing.T) {
		// 设置SQL预期
		tmock.ExpectBegin()
		tmock.ExpectExec(regexp.QuoteMeta(`UPDATE "slam_integration_cve" SET "check_res"=$1 WHERE id = $2 AND is_del = $3`)).
			WithArgs(1, 1, global.NUMBER_FALSE).
			WillReturnResult(sqlmock.NewResult(0, 1))
		tmock.ExpectCommit()

		// 执行测试
		umap := map[string]any{
			"check_res": 1,
		}
		rowsAffected, err := model.UpdateWithMap(1, umap)

		// 验证结果
		assert.NoError(err)
		assert.Equal(1, rowsAffected)
		assert.NoError(tmock.ExpectationsWereMet())
	})
}

func TestIntegrationCveModelRemoveRows(t *testing.T) {
	assert := assert.New(t)

	// 初始化测试数据库
	gormDB, tmock, err := initSqlmock()
	assert.NoError(err)

	model := &integrationCveModel{db: gormDB}

	t.Run("success case", func(t *testing.T) {
		// 设置SQL预期
		tmock.ExpectBegin()
		tmock.ExpectExec(regexp.QuoteMeta(`UPDATE "slam_integration_cve" SET "is_del"=$1,"update_time"=$2 WHERE id in ($3,$4) AND source = $5 AND "is_del" = $6`)).
			WithArgs(global.NUMBER_TRUE, sqlmock.AnyArg(), 1, 2, global.INTEGRATION_SOURCE_MANUAL, global.NUMBER_FALSE).
			WillReturnResult(sqlmock.NewResult(0, 2))
		tmock.ExpectCommit()

		// 执行测试
		rowsAffected, err := model.RemoveRows([]int{1, 2})

		// 验证结果
		assert.NoError(err)
		assert.Equal(int64(2), rowsAffected)
		assert.NoError(tmock.ExpectationsWereMet())
	})
}

func TestIntegrationCveModelGetProductListByIgid(t *testing.T) {
	assert := assert.New(t)

	// 初始化测试数据库
	gormDB, tmock, err := initSqlmock()
	assert.NoError(err)

	model := &integrationCveModel{db: gormDB}

	t.Run("success case", func(t *testing.T) {
		// 设置SQL预期
		tmock.ExpectQuery(regexp.QuoteMeta(`SELECT id, product_id FROM "slam_integration_cve" WHERE integration_id = $1`)).
			WithArgs(1).
			WillReturnRows(sqlmock.NewRows([]string{"id", "product_id"}).
				AddRow(1, 101).
				AddRow(2, 102))

		// 执行测试
		result, err := model.GetProductListByIgid(1)

		// 验证结果
		assert.NoError(err)
		assert.Equal(map[int]int{1: 101, 2: 102}, result)
		assert.NoError(tmock.ExpectationsWereMet())
	})
}

func TestIntegrationCveModelUpdateFromHistoryToNewIg(t *testing.T) {
	assert := assert.New(t)

	// 初始化测试数据库
	gormDB, tmock, err := initSqlmock()
	assert.NoError(err)

	model := &integrationCveModel{db: gormDB}

	t.Run("success case", func(t *testing.T) {
		// 设置SQL预期
		tmock.ExpectBegin()
		tmock.ExpectExec(regexp.QuoteMeta(`UPDATE "slam_integration_cve" SET "check_res"=$1,"integration_id"=$2,"is_publish"=$3,"source"=$4 WHERE is_publish = $5 AND is_del = $6`)).
			WithArgs(global.NUMBER_FALSE, 10, IntegrationCheckPass, global.INTEGRATION_SOURCE_HISTORY, IntegrationCheckUnPass, global.NUMBER_FALSE).
			WillReturnResult(sqlmock.NewResult(0, 3))
		tmock.ExpectCommit()

		// 执行测试
		count, err := model.UpdateFromHistoryToNewIg(10)

		// 验证结果
		assert.NoError(err)
		assert.Equal(3, count)
		assert.NoError(tmock.ExpectationsWereMet())
	})
}
