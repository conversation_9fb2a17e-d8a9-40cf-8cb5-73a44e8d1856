/*
 * @Author: lwq <EMAIL>
 * @Date: 2025-04-23
 * @LastEditors: lwq <EMAIL>
<<<<<<< HEAD
 * @LastEditTime: 2025-04-25
=======
 * @LastEditTime: 2025-04-29
>>>>>>> origin/slam
 * @FilePath: /ctdy-guard/slam/model/subscribe_rule.go
 * @Description: 订阅规则模型
 * @Copyright (c) 2025 by 2022.kylinos.cn, All Rights Reserved.
 */
package model

import (
	"time"

	"gorm.io/gorm"
)

// 通知频率常量定义
const (
	RateHourly  string = "hourly"  // 每小时
	RateDaily   string = "daily"   // 每天
	RateWeekly  string = "weekly"  // 每周
	RateMonthly string = "monthly" // 每月
)

type SubscribeRule struct {
	Id          int       `gorm:"column:id;primaryKey;autoIncrement:true;type:serial4;not null" label:"序号，主键"`
	UserID      []string  `gorm:"column:user_id;serializer:json;not null;default:''" label:"渠道用户id"`
	TemplateId  int       `gorm:"column:template_id;type:integer;not null;default:0" label:"模板id"`
	RuleName    string    `gorm:"column:rule_name;type:varchar(255);not null;default:''" label:"规则名称"`
	NotifyType  string    `gorm:"column:notify_type;type:varchar(255);not null;default:''" label:"通知类型"`
	ChannelType string    `gorm:"column:channel_type;type:varchar(255);not null;default:''" label:"渠道类型"`
	Frequency   string    `gorm:"column:frequency;type:varchar(255);not null;default:''" label:"通知频率"`
	Minute      int       `gorm:"column:fminute;type:integer;not null;default:0" label:"分钟"`
	Hour        int       `gorm:"column:fhour;type:integer;not null;default:0" label:"小时"`
	Day         int       `gorm:"column:fday;type:integer;not null;default:0" label:"天"`
	Week        int       `gorm:"column:fweek;type:integer;not null;default:0" label:"周"`
	Month       int       `gorm:"column:fmonth;type:integer;not null;default:0" label:"月"`
	Enabled     bool      `gorm:"column:enabled;type:boolean;not null;default:true" label:"是否启用"`
	CreatedAt   time.Time `gorm:"column:create_time;not null" label:"创建时间"`
	UpdatedAt   time.Time `gorm:"column:update_time;not null" label:"更新时间"`
}

// TableName 表名
func (*SubscribeRule) TableName() string {
	return "slam_subscribe_rule"
}

// subscribeRuleModel 订阅规则模型
type subscribeRuleModel struct {
	db *gorm.DB
}

// SubscribeRuleRepo 订阅规则仓库接口
type SubscribeRuleRepo interface {
	/**
	 * @author: lwq <EMAIL>
	 * @description: GetRulesByCurrentTime 根据当前时间和不同的通知频率构建查询条件
	 * @return {[]*SubscribeRule, error}
	 */
	GetRulesByCurrentTime() ([]*SubscribeRule, error)
}

// NewSubscribeRule 初始化订阅规则模型
func NewSubscribeRule() SubscribeRuleRepo {
	return &subscribeRuleModel{
		db: GetDB(),
	}
}

// GetRulesByCurrentTime 根据当前时间和不同的通知频率构建查询条件
func (s *subscribeRuleModel) GetRulesByCurrentTime() ([]*SubscribeRule, error) {
	now := time.Now()
	minute := now.Minute()
	hour := now.Hour()
	day := now.Day()
	week := int(now.Weekday())
	// 周日为0，转为7
	if week == 0 {
		week = 7
	}

	// 构建查询条件
	query := s.db.Where("enabled = ?", true).
		Where("(frequency = ? AND fminute = ?) OR "+
			// "1=1 OR "+
			"(frequency = ? AND fminute = ? AND fhour = ?) OR "+
			"(frequency = ? AND fminute = ? AND fhour = ? AND fweek = ?) OR "+
			"(frequency = ? AND fminute = ? AND fhour = ? AND fday = ?)",
			RateHourly, minute,
			RateDaily, minute, hour,
			RateWeekly, minute, hour, week,
			RateMonthly, minute, hour, day,
		)

	var rules []*SubscribeRule
	err := query.Find(&rules).Error
	return rules, err
}
