package utils

import (
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
)

func TestResponseSuccess(t *testing.T) {
	gin.SetMode(gin.TestMode)

	tests := []struct {
		name     string
		response *Response
		expected int
	}{
		{
			name:     "success response",
			response: NewResponseSuccess(),
			expected: http.StatusOK,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			w := httptest.NewRecorder()
			c, _ := gin.CreateTestContext(w)

			ResponseSuccess(c, tt.response)

			assert.Equal(t, tt.expected, w.Code)
		})
	}
}

func TestResponseFaied(t *testing.T) {
	gin.SetMode(gin.TestMode)

	t.Run("Happy Path", func(t *testing.T) {
		w := httptest.NewRecorder()
		c, _ := gin.CreateTestContext(w)

		response := NewResponseFailed()
		ResponseFaied(c, response)

		assert.Equal(t, http.StatusOK, w.Code)
		assert.Equal(t, `{"code":422,"msg":"failed","data":{},"traceid":""}`, w.Body.String())
	})

	t.Run("Empty Response", func(t *testing.T) {
		w := httptest.NewRecorder()
		c, _ := gin.CreateTestContext(w)

		response := &Response{Code: 422, Msg: "", Data: nil}
		ResponseFaied(c, response)

		assert.Equal(t, http.StatusOK, w.Code)
		assert.Equal(t, `{"code":422,"msg":"","data":null,"traceid":""}`, w.Body.String())
	})
}

func TestResponseUnAuth(t *testing.T) {
	gin.SetMode(gin.TestMode)

	tests := []struct {
		name     string
		response *Response
		status   int
	}{
		{
			name:     "Unauthenticated response",
			response: NewResponseFailed(), // Using the failed response for unauthorized
			status:   http.StatusUnauthorized,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			recorder := httptest.NewRecorder()
			c, _ := gin.CreateTestContext(recorder)

			ResponseUnAuth(c, tt.response)

			assert.Equal(t, tt.status, recorder.Code)
			assert.JSONEq(t, `{"code":422,"msg":"failed","data":{},"traceid":""}`, recorder.Body.String())
		})
	}
}
