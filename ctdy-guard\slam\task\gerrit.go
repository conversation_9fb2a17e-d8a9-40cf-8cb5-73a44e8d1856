package task

import (
	"bytes"
	"context"
	"crypto/tls"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"log"
	"math/rand"
	"net/http"
	"net/url"
	"os"
	"os/exec"
	"path"
	"path/filepath"
	"regexp"
	"slam/model"
	"slam/slog"
	"slam/utils"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/bndr/gojenkins"
	"golift.io/xtractr"
)

const (
	MERGE_IF_NECESSARY RepoSubmitType = iota + 1
	REBASE_IF_NECESSARY
	REBASE_ALWAYS
	FAST_FORWARD_ONLY
	MERGE_ALWAYS
	CHERRY_PICK
)

const (
	Rework                 RevisionKind = "REWORK"
	TrivialRebase          RevisionKind = "TRIVIAL_REBASE"
	MergeFirstParentUpdate RevisionKind = "MERGE_FIRST_PARENT_UPDATE"
	NoCodeChange           RevisionKind = "NO_CODE_CHANGE"
	NoChange               RevisionKind = "NO_CHANGE"
)

const GERRIT_INIT_PARENT_DIR = "/opt/files/slam/tmp/gerrit/"
const JENKINS_MAX_KOJI_TASK_COUNT = 1024

var (
	INTERNAL_SSH_KEY_FILE                   = "~/.ssh/id_ed25519"
	INTERNAL_CONNECTION_USER                = utils.GerritAk + "@" + utils.GerritDomain
	INTERNAL_HTTPS_CONNECTION_TYPE          = "https://"
	INTERNAL_HTTPS_CONNECTION_GERRIT_URL    = INTERNAL_HTTPS_CONNECTION_TYPE + utils.GerritDomain + ":" + strconv.Itoa(utils.GerritPort)
	INTERNAL_HTTPS_CONNECTION_JENKINS_URL   = INTERNAL_HTTPS_CONNECTION_TYPE + utils.DomainName + ":" + strconv.Itoa(utils.JenkinsPort)
	INTERNAL_HTTPS_CONNECTION_JENKINS_IPURL = INTERNAL_HTTPS_CONNECTION_TYPE + utils.Host + ":" + strconv.Itoa(utils.JenkinsPort)
	GERRIT_API_PROJECT_URL                  = "/a/projects/"
	GERRIT_API_BRANCHES_URL                 = "/a/projects/%s/branches/%s"
)

var G_GerritToken string = ""
var CompressTypeReflectMap sync.Map
var RepoSubmitTypeStr = []string{"MERGE_IF_NECESSARY", "REBASE_IF_NECESSARY", "REBASE_ALWAYS", "FAST_FORWARD_ONLY", "MERGE_ALWAYS", "CHERRY_PICK"}

type RepoSubmitType int
type RevisionKind string
type RecipientType string
type AccessRightsUpdateInfo map[string]AccessSectionInfo

type CommitMessageInfo struct {
	BranchName          string `json:"branchName,omitempty"`
	SrcRPMCanonicalName string `json:"srcRPMCanonicalName,omitempty"`
	XflowID             int    `json:"xflowId,omitempty"`
	ReleaseDist         string `json:"releaseDist,omitempty"`
	LectoTypeCenter     string `json:"LectoTypeCenter,omitempty"`
	CIConfigKoji        string `json:"CIConfigKoji,omitempty"`
	CIConfigArch        string `json:"CIConfigArch,omitempty"`
}

var KojiArchReflect sync.Map

type AccessSectionInfo struct {
	Permissions map[string]PermissionInfo `json:"permissions"`
}

type PermissionInfo struct {
	Label     string                        `json:"label,omitempty"`
	Exclusive bool                          `json:"exclusive"`
	Rules     map[string]PermissionRuleInfo `json:"rules"`
}

type PermissionRuleInfo struct {
	// Action Possible Values: ALLOW, DENY or BLOCK, INTERACTIVE and BATCH
	Action string `json:"action"`
	Force  bool   `json:"force"`
	Min    int    `json:"min"`
	Max    int    `json:"max"`
}

type BranchInput struct {
	Ref       string `json:"ref,omitempty"`
	Revision  string `json:"revision,omitempty"`
	CanDelete bool   `json:"can_delete,omitempty"`
}

type ProjectDescriptionInput struct {
	Description   string `json:"description,omitempty"`
	CommitMessage string `json:"commit_message,omitempty"`
}

type AttentionSetInput struct {
	User          string                       `json:"user,omitempty"`
	Reason        string                       `json:"reason"`
	Notify        string                       `json:"notify,omitempty"`
	NotifyDetails map[RecipientType]NotifyInfo `json:"notify_details,omitempty"`
}

type ProjectNewTagInput struct {
	Ref      string `json:"ref"`
	Message  string `json:"message,omitempty"`
	Revision string `json:"revision,omitempty"`
}

type ProjectInput struct {
	Name                             string                       `json:"name,omitempty"`
	Parent                           string                       `json:"parent,omitempty"`
	Description                      string                       `json:"description,omitempty"`
	PermissionsOnly                  bool                         `json:"permissions_only"`
	CreateEmptyCommit                bool                         `json:"create_empty_commit"`
	SubmitType                       string                       `json:"submit_type,omitempty"`
	Branches                         []string                     `json:"branches,omitempty"`
	Owners                           []string                     `json:"owners,omitempty"`
	UseContributorAgreements         string                       `json:"use_contributor_agreements,omitempty"`
	UseSignedOffBy                   string                       `json:"use_signed_off_by,omitempty"`
	CreateNewChangeForAllNotInTarget string                       `json:"create_new_change_for_all_not_in_target,omitempty"`
	UseContentMerge                  string                       `json:"use_content_merge,omitempty"`
	RequireChangeID                  string                       `json:"require_change_id,omitempty"`
	MaxObjectSizeLimit               string                       `json:"max_object_size_limit,omitempty"`
	PluginConfigValues               map[string]map[string]string `json:"plugin_config_values,omitempty"`
}

type Timestamp struct {
	time.Time
}

type GroupOptionsInfo struct {
	VisibleToAll bool `json:"visible_to_all,omitempty"`
}

type AccountInfo struct {
	AccountID   int    `json:"_account_id,omitempty"`
	Name        string `json:"name,omitempty"`
	DisplayName string `json:"display_name,omitempty"`
	Email       string `json:"email,omitempty"`
	Username    string `json:"username,omitempty"`
	Avatars     []struct {
		URL    string `json:"url,omitempty"`
		Height int    `json:"height,omitempty"`
	} `json:"avatars,omitempty"`
	MoreAccounts    bool     `json:"_more_accounts,omitempty"`
	SecondaryEmails []string `json:"secondary_emails,omitempty"`
	Status          string   `json:"status,omitempty"`
	Inactive        bool     `json:"inactive,omitempty"`
	Tags            []string `json:"tags,omitempty"`
}

type GroupInfo struct {
	ID          string           `json:"id"`
	Name        string           `json:"name,omitempty"`
	URL         string           `json:"url,omitempty"`
	Options     GroupOptionsInfo `json:"options"`
	Description string           `json:"description,omitempty"`
	GroupID     int              `json:"group_id,omitempty"`
	Owner       string           `json:"owner,omitempty"`
	OwnerID     string           `json:"owner_id,omitempty"`
	CreatedOn   *Timestamp       `json:"created_on,omitempty"`
	MoreGroups  bool             `json:"_more_groups,omitempty"`
	Members     []AccountInfo    `json:"members,omitempty"`
	Includes    []GroupInfo      `json:"includes,omitempty"`
}

type ProjectAccessInfo struct {
	Revision       string                       `json:"revision"`
	InheritsFrom   ProjectInfo                  `json:"inherits_from"`
	Local          map[string]AccessSectionInfo `json:"local"`
	IsOwner        bool                         `json:"is_owner"`
	OwnerOf        []string                     `json:"owner_of"`
	CanUpload      bool                         `json:"can_upload"`
	CanAdd         bool                         `json:"can_add"`
	CanAddTags     bool                         `json:"can_add_tags"`
	ConfigVisible  bool                         `json:"config_visible"`
	Groups         map[string]GroupInfo         `json:"groups"`
	ConfigWebLinks []string                     `json:"configWebLinks"`
}

type ProjectParentInput struct {
	Parent        string `json:"parent"`
	CommitMessage string `json:"commit_message,omitempty"`
}

type InheritedBooleanInfo struct {
	Value           bool   `json:"value"`
	ConfiguredValue string `json:"configured_value"`
	InheritedValue  bool   `json:"inherited_value,omitempty"`
}

type ReviewersInput struct {
	Action   string `json:"action,omitempty"`
	Type     string `json:"type,omitempty"`
	Filter   string `json:"filter,omitempty"`
	Reviewer string `json:"reviewer,omitempty"`
}

type WebLinkInfo struct {
	Name     string `json:"name,omitempty"`
	URL      string `json:"url,omitempty"`
	ImageURL string `json:"image_url,omitempty'"`
}

type GitPersonInfo struct {
	Name  string     `json:"name,omitempty"`
	Email string     `json:"email,omitempty"`
	Date  *Timestamp `json:"date,omitempty"`
	TZ    int        `json:"tz,omitempty"`
}

type AbandonInput struct {
	Message       string       `json:"message,omitempty"`
	Notify        string       `json:"notify,omitempty"`
	NotifyDetails []NotifyInfo `json:"notify_details,omitempty"`
}

type ApprovalInfo struct {
	AccountInfo
	Value int    `json:"value,omitempty"`
	Date  string `json:"date,omitempty"`
}

type NotifyInfo struct {
	Accounts []AccountInfo `json:"accounts"`
}

type CommitMessageInput struct {
	Message       string       `json:"message,omitempty"`
	Notify        string       `json:"notify,omitempty"`
	NotifyDetails []NotifyInfo `json:"notify_details"`
}

type ChangeEditInput struct {
	RestorePath string `json:"restore_path,omitempty"`
	OldPath     string `json:"old_path,omitempty"`
	NewPath     string `json:"new_path,omitempty"`
}

type ChangeEditMessageInput struct {
	Message string `json:"message"`
}

type ChangeMessageInfo struct {
	ID             string      `json:"id"`
	Author         AccountInfo `json:"author,omitempty"`
	Date           Timestamp   `json:"date"`
	Message        string      `json:"message"`
	Tag            string      `json:"tag,omitempty"`
	RevisionNumber int         `json:"_revision_number,omitempty"`
}

type CherryPickInput struct {
	Message           string                       `json:"message,omitempty"`
	Destination       string                       `json:"destination"`
	Base              string                       `json:"base,omitempty"`
	Parent            int                          `json:"parent,omitempty"`
	Notify            string                       `json:"notify,omitempty"`
	NotifyDetails     map[RecipientType]NotifyInfo `json:"notify_details,omitempty"`
	KeepReviewers     bool                         `json:"keep_reviewers,omitempty"`
	AllowConflicts    bool                         `json:"allow_conflicts,omitempty"`
	Topic             string                       `json:"topic,omitempty"`
	AllowEmpty        bool                         `json:"allow_empty,omitempty"`
	CommitterEmail    string                       `json:"committer_email,omitempty"`
	ValidationOptions map[string]string            `json:"validation_options,omitempty"`
}

type DiffFileMetaInfo struct {
	Name        string        `json:"name"`
	ContentType string        `json:"content_type"`
	Lines       int           `json:"lines"`
	WebLinks    []WebLinkInfo `json:"web_links,omitempty"`
}

type ProjectInfo struct {
	ID          string            `json:"id"`
	Name        string            `json:"name"`
	Parent      string            `json:"parent,omitempty"`
	Description string            `json:"description,omitempty"`
	State       string            `json:"state,omitempty"`
	Branches    map[string]string `json:"branches,omitempty"`
	WebLinks    []WebLinkInfo     `json:"web_links,omitempty"`
}

type DiffWebLinkInfo struct {
	Name                     string `json:"name"`
	URL                      string `json:"url"`
	ImageURL                 string `json:"image_url"`
	ShowOnSideBySideDiffView bool   `json:"show_on_side_by_side_diff_view"`
	ShowOnUnifiedDiffView    bool   `json:"show_on_unified_diff_view"`
}

type FetchInfo struct {
	URL      string            `json:"url"`
	Ref      string            `json:"ref"`
	Commands map[string]string `json:"commands,omitempty"`
}

type FixInput struct {
	DeletePatchSetIfCommitMissing bool   `json:"delete_patch_set_if_commit_missing"`
	ExpectMergedAs                string `json:"expect_merged_as"`
}

type GroupBaseInfo struct {
	ID   int    `json:"id"`
	Name string `json:"name"`
}

type IncludedInInfo struct {
	Branches []string          `json:"branches"`
	Tags     []string          `json:"tags"`
	External map[string]string `json:"external,omitempty"`
}

type RebaseInput struct {
	Base               string            `json:"base,omitempty"`
	Strategy           string            `json:"strategy,omitempty"`
	AllowConflicts     bool              `json:"allow_conflicts,omitempty"`
	OnBehalfOfUploader bool              `json:"on_behalf_of_uploader,omitempty"`
	CommitterEmail     string            `json:"committer_email,omitempty"`
	ValidationOptions  map[string]string `json:"validation_options,omitempty"`
}

type RestoreInput struct {
	Message string `json:"message,omitempty"`
}

type RevertInput struct {
	Message string `json:"message,omitempty"`
}

type ReviewInfo struct {
	Labels map[string]int `json:"labels"`
}

type ReviewerUpdateInfo struct {
	Updated   Timestamp   `json:"updated"`    // Timestamp of the update.
	UpdatedBy AccountInfo `json:"updated_by"` // The account which modified state of the reviewer in question.
	Reviewer  AccountInfo `json:"reviewer"`   // The reviewer account added or removed from the change.
	State     string      `json:"state"`      // The reviewer state, one of "REVIEWER", "CC" or "REMOVED".
}

type ReviewerInfo struct {
	AccountInfo
	Approvals map[string]string `json:"approvals"`
}

type AddReviewerResult struct {
	Input     string         `json:"input,omitempty"`
	Reviewers []ReviewerInfo `json:"reviewers,omitempty"`
	CCS       []ReviewerInfo `json:"ccs,omitempty"`
	Error     string         `json:"error,omitempty"`
	Confirm   bool           `json:"confirm,omitempty"`
}

type ReviewResult struct {
	ReviewInfo
	Reviewers  map[string]AddReviewerResult `json:"reviewers,omitempty"`
	Ready      bool                         `json:"ready,omitempty"`
	Error      string                       `json:"error,omitempty"`
	ChangeInfo ChangeInfo                   `json:"change_info"`
}

type TopicInput struct {
	Topic string `json:"topic,omitempty"`
}

type SubmitRecord struct {
	Status       string                            `json:"status"`
	Ok           map[string]map[string]AccountInfo `json:"ok,omitempty"`
	Reject       map[string]map[string]AccountInfo `json:"reject,omitempty"`
	Need         map[string]interface{}            `json:"need,omitempty"`
	May          map[string]map[string]AccountInfo `json:"may,omitempty"`
	Impossible   map[string]interface{}            `json:"impossible,omitempty"`
	ErrorMessage string                            `json:"error_message,omitempty"`
}

type SubmitInput struct {
	OnBehalfOf    string                       `json:"on_behalf_of,omitempty"`
	Notify        string                       `json:"notify,omitempty"`
	NotifyDetails map[RecipientType]NotifyInfo `json:"notify_details,omitempty"`
	WaitForMerge  bool                         `json:"wait_for_merge,omitempty"`
}

type SubmitInfo struct {
	Status     string `json:"status"`
	OnBehalfOf string `json:"on_behalf_of,omitempty"`
}

type RuleInput struct {
	Rule    string `json:"rule"`
	Filters string `json:"filters,omitempty"`
}

type ReviewerInput struct {
	Reviewer  string `json:"reviewer"`
	Confirmed bool   `json:"confirmed,omitempty"`
}

type FixReplacementInfo struct {
	Path        string       `json:"path"`
	Range       CommentRange `json:"range"`
	Replacement string       `json:"replacement,omitempty"`
}

type FixSuggestionInfo struct {
	FixID        string             `json:"fix_id"`
	Description  string             `json:"description"`
	Replacements FixReplacementInfo `json:"replacements"`
}

type RobotCommentInput struct {
	CommentInput
	RobotID        string              `json:"robot_id"`
	RobotRunID     string              `json:"robot_run_id"`
	URL            string              `json:"url,omitempty"`
	Properties     *map[string]*string `json:"properties,omitempty"`
	FixSuggestions *FixSuggestionInfo  `json:"fix_suggestions,omitempty"`
}

type ReviewInput struct {
	Message                          string                         `json:"message,omitempty"`
	Tag                              string                         `json:"tag,omitempty"`
	Labels                           map[string]int                 `json:"labels,omitempty"`
	Comments                         map[string][]CommentInput      `json:"comments,omitempty"`
	RobotComments                    map[string][]RobotCommentInput `json:"robot_comments,omitempty"`
	StrictLabels                     bool                           `json:"strict_labels,omitempty"`
	Drafts                           string                         `json:"drafts,omitempty"`
	Notify                           string                         `json:"notify,omitempty"`
	OmitDuplicateComments            bool                           `json:"omit_duplicate_comments,omitempty"`
	OnBehalfOf                       string                         `json:"on_behalf_of,omitempty"`
	Reviewers                        []ReviewerInput                `json:"reviewers,omitempty"`
	Ready                            bool                           `json:"ready,omitempty"`
	WorkInProgress                   bool                           `json:"work_in_progress,omitempty"`
	AddToAttentionSet                []AttentionSetInput            `json:"add_to_attention_set,omitempty"`
	RemoveFromAttentionSet           []AttentionSetInput            `json:"remove_from_attention_set,omitempty"`
	IgnoreAutomaticAttentionSetRules bool                           `json:"ignore_automatic_attention_set_rules,omitempty"`
}

type CommitInfo struct {
	Commit    string        `json:"commit,omitempty"`
	Parents   []CommitInfo  `json:"parents"`
	Author    GitPersonInfo `json:"author"`
	Committer GitPersonInfo `json:"committer"`
	Subject   string        `json:"subject"`
	Message   string        `json:"message"`
	WebLinks  []WebLinkInfo `json:"web_links,omitempty"`
}

type RelatedChangeAndCommitInfo struct {
	ChangeID              string     `json:"change_id,omitempty"`
	Commit                CommitInfo `json:"commit"`
	ChangeNumber          int        `json:"_change_number,omitempty"`
	RevisionNumber        int        `json:"_revision_number,omitempty"`
	CurrentRevisionNumber int        `json:"_current_revision_number,omitempty"`
	Status                string     `json:"status,omitempty"`
}

type CommentInput struct {
	ID         string        `json:"id,omitempty"`
	Path       string        `json:"path,omitempty"`
	Side       string        `json:"side,omitempty"`
	Line       int           `json:"line,omitempty"`
	Range      *CommentRange `json:"range,omitempty"`
	InReplyTo  string        `json:"in_reply_to,omitempty"`
	Updated    *Timestamp    `json:"updated,omitempty"`
	Message    string        `json:"message,omitempty"`
	Unresolved *bool         `json:"unresolved,omitempty"`
}

type MoveInput struct {
	DestinationBranch string `json:"destination_branch"`
	Message           string `json:"message,omitempty"`
	KeepAllVotes      bool   `json:"keep_all_votes"`
}

type AccountInput struct {
	Username     string   `json:"username,omitempty"`
	Name         string   `json:"name,omitempty"`
	Email        string   `json:"email,omitempty"`
	SSHKey       string   `json:"ssh_key,omitempty"`
	HTTPPassword string   `json:"http_password,omitempty"`
	Groups       []string `json:"groups,omitempty"`
}

type ChangeInput struct {
	Project           string                 `json:"project"`
	Branch            string                 `json:"branch"`
	Subject           string                 `json:"subject"`
	Topic             string                 `json:"topic,omitempty"`
	Status            string                 `json:"status,omitempty"`
	IsPrivate         bool                   `json:"is_private,omitempty"`
	WorkInProgress    bool                   `json:"work_in_progress,omitempty"`
	BaseChange        string                 `json:"base_change,omitempty"`
	BaseCommit        string                 `json:"base_commit,omitempty"`
	NewBranch         bool                   `json:"new_branch,omitempty"`
	ValidationOptions map[string]interface{} `json:"validation_options,omitempty"`
	Merge             *MergeInput            `json:"merge,omitempty"`
	Author            *AccountInput          `json:"author,omitempty"`
	Notify            string                 `json:"notify,omitempty"`
	NotifyDetails     string                 `json:"notify_details,omitempty"`
}

type AttentionSetInfo struct {
	Account    AccountInfo `json:"account"`
	LastUpdate Timestamp   `json:"last_update"`
	Reason     string      `json:"reason"`
}

type ActionInfo struct {
	Method  string `json:"method,omitempty"`
	Label   string `json:"label,omitempty"`
	Title   string `json:"title,omitempty"`
	Enabled bool   `json:"enabled,omitempty"`
}

type ChangeInfo struct {
	ID                     string                      `json:"id"`
	URL                    string                      `json:"url,omitempty"`
	Project                string                      `json:"project"`
	Branch                 string                      `json:"branch"`
	Topic                  string                      `json:"topic,omitempty"`
	AttentionSet           map[string]AttentionSetInfo `json:"attention_set,omitempty"`
	Assignee               AccountInfo                 `json:"assignee,omitempty"`
	Hashtags               []string                    `json:"hashtags,omitempty"`
	ChangeID               string                      `json:"change_id,omitempty"`
	Subject                string                      `json:"subject"`
	Status                 string                      `json:"status"`
	Created                Timestamp                   `json:"created,omitempty"`
	Updated                Timestamp                   `json:"updated,omitempty"`
	Submitted              *Timestamp                  `json:"submitted,omitempty"`
	Submitter              AccountInfo                 `json:"submitter,omitempty"`
	Starred                bool                        `json:"starred,omitempty"`
	Reviewed               bool                        `json:"reviewed,omitempty"`
	SubmitType             string                      `json:"submit_type,omitempty"`
	Mergeable              bool                        `json:"mergeable,omitempty"`
	Submittable            bool                        `json:"submittable,omitempty"`
	Insertions             int                         `json:"insertions"`
	Deletions              int                         `json:"deletions"`
	TotalCommentCount      int                         `json:"total_comment_count,omitempty"`
	UnresolvedCommentCount int                         `json:"unresolved_comment_count,omitempty"`
	Number                 int                         `json:"_number"`
	Owner                  AccountInfo                 `json:"owner"`
	Actions                map[string]ActionInfo       `json:"actions,omitempty"`
	Labels                 map[string]LabelInfo        `json:"labels,omitempty"`
	PermittedLabels        map[string][]string         `json:"permitted_labels,omitempty"`
	RemovableReviewers     []AccountInfo               `json:"removable_reviewers,omitempty"`
	Reviewers              map[string][]AccountInfo    `json:"reviewers,omitempty"`
	PendingReviewers       map[string][]AccountInfo    `json:"pending_reviewers,omitempty"`
	ReviewerUpdates        []ReviewerUpdateInfo        `json:"reviewer_updates,omitempty"`
	Messages               []ChangeMessageInfo         `json:"messages,omitempty"`
	CurrentRevision        string                      `json:"current_revision,omitempty"`
	Revisions              map[string]RevisionInfo     `json:"revisions,omitempty"`
	MoreChanges            bool                        `json:"_more_changes,omitempty"`
	Problems               []ProblemInfo               `json:"problems,omitempty"`
	IsPrivate              bool                        `json:"is_private,omitempty"`
	WorkInProgress         bool                        `json:"work_in_progress,omitempty"`
	HasReviewStarted       bool                        `json:"has_review_started,omitempty"`
	RevertOf               int                         `json:"revert_of,omitempty"`
	SubmissionID           string                      `json:"submission_id,omitempty"`
	CherryPickOfChange     int                         `json:"cherry_pick_of_change,omitempty"`
	CherryPickOfPatchSet   int                         `json:"cherry_pick_of_patch_set,omitempty"`
	ContainsGitConflicts   bool                        `json:"contains_git_conflicts,omitempty"`
	BaseChange             string                      `json:"base_change,omitempty"`
	CommitMessage          string                      `json:"commitMessage,omitempty"`
	Comments               []ChangeComments            `json:"comments,omitempty"`
	AllReviewers           []ReviewerInfo              `json:"allReviewers,omitempty"`
	CurrentPatchSet        PatchSetInfo                `json:"currentPatchSet,omitempty"`
	CreatedOn              int                         `json:"createdOn,omitempty"`
	LastUpdated            int                         `json:"lastUpdated,omitempty"`
	Open                   bool                        `json:"open,omitempty"`
	PatchSets              []PatchSetInfo              `json:"patchSets,omitempty"`
}

type PatchSetInfo struct {
	Number         int                    `json:"number,omitempty"`
	Ref            string                 `json:"ref,omitempty"`
	Revision       string                 `json:"revision,omitempty"`
	CreatedOn      int                    `json:"createdOn,omitempty"`
	Uploader       AccountInfo            `json:"uploader,omitempty"`
	Parents        []string               `json:"parents,omitempty"`
	Author         AccountInfo            `json:"author,omitempty"`
	Kind           string                 `json:"kind,omitempty"`
	Comments       []ChangeComments       `json:"comments,omitempty"`
	Approvals      []PatchsetApprovalInfo `json:"approvals,omitempty"`
	SizeInsertions int                    `json:"sizeInsertions,omitempty"`
	SizeDeletions  int                    `json:"sizeDeletions,omitempty"`
}

type PatchsetApprovalInfo struct {
	Value       string      `json:"value,omitempty"`
	GrantedOn   Timestamp   `json:"granted_on,omitempty"`
	By          AccountInfo `json:"by,omitempty"`
	Type        string      `json:"type,omitempty"`
	Description string      `json:"description,omitempty"`
}

type ChangeComments struct {
	File     string       `json:"file,omitempty"`
	Line     int          `json:"line,omitempty"`
	Reviewer ReviewerInfo `json:"reviewer,omitempty"`
	Message  string       `json:"message,omitempty"`
}

type LabelInfo struct {
	Optional     bool              `json:"optional,omitempty"`
	Approved     AccountInfo       `json:"approved,omitempty"`
	Rejected     AccountInfo       `json:"rejected,omitempty"`
	Recommended  AccountInfo       `json:"recommended,omitempty"`
	Disliked     AccountInfo       `json:"disliked,omitempty"`
	Blocking     bool              `json:"blocking,omitempty"`
	Value        int               `json:"value,omitempty"`
	DefaultValue int               `json:"default_value,omitempty"`
	All          []ApprovalInfo    `json:"all,omitempty"`
	Values       map[string]string `json:"values,omitempty"`
}

type MergeInput struct {
	Source         string `json:"source"`
	SourceBranch   string `json:"source_branch,omitempty"`
	Strategy       string `json:"strategy,omitempty"`
	AllowConflicts bool   `json:"allow_conflicts,omitempty"`
}

type ParentInfo struct {
	BranchName             string `json:"branch_name,omitempty"`
	CommitID               string `json:"commit_id,omitempty"`
	IsMergedInTargetBranch bool   `json:"is_merged_in_target_branch"`
	ChangeID               string `json:"change_id,omitempty"`
	ChangeNumber           int    `json:"change_number,omitempty"`
	PatchSetNumber         int    `json:"patch_set_number,omitempty"`
	ChangeStatus           string `json:"change_status,omitempty"`
}

type FileInfo struct {
	Status        string `json:"status,omitempty"`
	Binary        bool   `json:"binary,omitempty"`
	OldPath       string `json:"old_path,omitempty"`
	LinesInserted int    `json:"lines_inserted,omitempty"`
	LinesDeleted  int    `json:"lines_deleted,omitempty"`
	SizeDelta     int    `json:"size_delta"`
	Size          int    `json:"size"`
}

type RevisionInfo struct {
	Kind              RevisionKind          `json:"kind,omitempty"`
	Draft             bool                  `json:"draft,omitempty"`
	Number            int                   `json:"_number"`
	Created           Timestamp             `json:"created"`
	Uploader          AccountInfo           `json:"uploader"`
	Ref               string                `json:"ref"`
	Fetch             map[string]FetchInfo  `json:"fetch"`
	Commit            CommitInfo            `json:"commit,omitempty"`
	Files             map[string]FileInfo   `json:"files,omitempty"`
	Actions           map[string]ActionInfo `json:"actions,omitempty"`
	Reviewed          bool                  `json:"reviewed,omitempty"`
	MessageWithFooter string                `json:"messageWithFooter,omitempty"`
	ParentsData       []ParentInfo          `json:"parents_data,omitempty"`
}

type CommentInfo struct {
	PatchSet        int           `json:"patch_set,omitempty"`
	ID              string        `json:"id"`
	Path            string        `json:"path,omitempty"`
	Side            string        `json:"side,omitempty"`
	Line            int           `json:"line,omitempty"`
	Range           *CommentRange `json:"range,omitempty"`
	InReplyTo       string        `json:"in_reply_to,omitempty"`
	Message         string        `json:"message,omitempty"`
	Updated         *Timestamp    `json:"updated"`
	Author          AccountInfo   `json:"author,omitempty"`
	Unresolved      *bool         `json:"unresolved,omitempty"`
	ChangeMessageID string        `json:"change_message_id,omitempty"`
	CommitID        string        `json:"commit_id,omitempty"`
}

type ProblemInfo struct {
	Message string `json:"message"`
	Status  string `json:"status,omitempty"`
	Outcome string `json:"outcome,omitempty"`
}

type CommentRange struct {
	StartLine      int `json:"start_line"`
	StartCharacter int `json:"start_character"`
	EndLine        int `json:"end_line"`
	EndCharacter   int `json:"end_character"`
}

type TagInfo struct {
	Ref       string        `json:"ref,omitempty"`
	Revision  string        `json:"revision,omitempty"`
	Object    string        `json:"object,omitempty"`
	Message   string        `json:"message,omitempty"`
	Created   *Timestamp    `json:"created,omitempty"`
	CanDelete bool          `json:"can_delete,omitempty"`
	WebLinks  WebLinkInfo   `json:"web_links,omitempty"`
	Tagger    GitPersonInfo `json:"tagger,omitempty"`
}

type ReviewersJsonResult struct {
	Type         string       `json:"type"`
	Description  string       `json:"description"`
	Value        string       `json:"value"`
	GrantedOn    int64        `json:"grantedOn"`
	ReviewerData ReviewerData `json:"by"`
}
type ReviewerData struct {
	Name     string `json:"name"`
	Email    string `json:"email"`
	UserName string `json:"username"`
}
type AllStruct struct {
	Tag                  string  `json:"tag"`
	Value                int     `json:"value"`
	Date                 string  `json:"date"`
	PostSubmit           bool    `json:"post_submit"`
	PermittedVotingRange string  `json:"permitted_voting_range"`
	AccountID            float64 `json:"_account_id"`
	Name                 string  `json:"name"`
	Email                string  `json:"email"`
	UserName             string  `json:"username"`
	Tags                 string  `json:"tags"`
	Status               string  `json:"status"`
}

type CommandExecTimeoutError struct {
	Code   int    `default:"0x00000001"`
	Reason string `default:"当前命令执行超时，请稍后重试"`
	Error  error  `default:"errors.New(\"当前命令执行超时，请稍后重试\")"`
}

type CommandExecFailedError struct {
	Code   int    `default:"0x00000010"`
	Reason string `default:"当前命令执行失败，请稍后重试"`
	Error  error  `default:"errors.New(\"当前命令执行失败，请稍后重试\")"`
}

type RepoNotExistError struct {
	Code   int    `default:"0x00000011"`
	Reason string `default:"仓库不存在，请检查仓库名称是否正确"`
	Error  error  `default:"errors.New(\"仓库不存在，请检查仓库名称是否正确\")"`
}

type SSHGerritQueryError struct {
	Code   int    `default:"0x00000001"`
	Reason string `default:"SSH获取Gerrit信息失败"`
	Error  error  `default:"errors.New(\"SSH获取Gerrit信息失败\")"`
}

type GerritTokenInvalidError struct {
	Code   int    `default:"0x00000010"`
	Reason string `default:"Gerrit令牌失效，请联系管理员"`
	Error  error  `default:"errors.New(\"Gerrit令牌失效，请联系管理员\")"`
}

type GerritUnmergedError struct {
	Code   int    `default:"0x00000011"`
	Reason string `default:"Gerrit未合并，禁止提交"`
	Error  error  `default:"errors.New(\"Gerrit未合并，禁止提交\")"`
}

type GerritCIFailedError struct {
	Code   int    `default:"0x00000100"`
	Reason string `default:"Gerrit CI失败，禁止提交"`
	Error  error  `default:"errors.New(\"Gerrit CI失败，禁止提交\")"`
}

type GerritCIURLInvalidError struct {
	Code   int    `default:"0x00000101"`
	Reason string `default:"Gerrit CI URL无效，禁止提交"`
	Error  error  `default:"errors.New(\"Gerrit CI URL无效，禁止提交\")"`
}

type CommitMessageNotExistError struct {
	Code   int    `default:"0x00000110"`
	Reason string `default:"提交信息不存在，禁止提交"`
	Error  error  `default:"errors.New(\"提交信息不存在，禁止提交\")"`
}

type JenkinsJobNotExistError struct {
	Code   int    `default:"0x00000111"`
	Reason string `default:"Jenkins Job不存在，禁止提交"`
	Error  error  `default:"errors.New(\"Jenkins Job不存在，禁止提交\")"`
}

type JenkinsContextNotExistError struct {
	Code   int    `default:"0x00001000"`
	Reason string `default:"Jenkins Context不存在，禁止提交"`
	Error  error  `default:"errors.New(\"Jenkins Context不存在，禁止提交\")"`
}

type CIKojiInvalidError struct {
	Code   int    `default:"0x00001001"`
	Reason string `default:"CI Koji无效，禁止提交"`
	Error  error  `default:"errors.New(\"CI Koji无效，禁止提交\")"`
}

type KojiTagProductTagNotMatchError struct {
	Code   int    `default:"0x00001010"`
	Reason string `default:"Koji Tag与Product Tag不匹配，禁止提交"`
	Error  error  `default:"errors.New(\"Koji Tag与Product Tag不匹配，禁止提交\")"`
}

type KojiRepo struct {
	model.KojiRepo
}

type Logger struct {
	xtractr *log.Logger
	debug   *log.Logger
	info    *log.Logger
}

func (l *Logger) Printf(msg string, v ...interface{}) {
	l.xtractr.Printf(msg, v...)
}

func (l *Logger) Debugf(msg string, v ...interface{}) {
	l.debug.Printf(msg, v...)
}

func (l *Logger) Infof(msg string, v ...interface{}) {
	l.info.Printf(msg, v...)
}

var sshGerritQueryError = SSHGerritQueryError{
	Code:   0x00000001,
	Reason: "SSH查询Gerrit信息失败，请检查Gerrit SSH信息是否正确",
	Error:  errors.New("SSH查询Gerrit信息失败，请检查Gerrit SSH信息是否正确"),
}

var gerritTokenInvalidError = GerritTokenInvalidError{
	Code:   0x00000002,
	Reason: "gerrit令牌Token失效，需要重新创建Token",
	Error:  errors.New("gerrit令牌Token失效，需要重新创建Token"),
}

var commandExecFailedError = CommandExecFailedError{
	Code:   0x00000010,
	Reason: "当前命令执行失败，请稍后重试",
	Error:  errors.New("当前命令执行失败，请稍后重试"),
}

func (rst RepoSubmitType) String() string {
	return RepoSubmitTypeStr[rst-1]
}

func RepoSubmitTypeValues() []string {
	return RepoSubmitTypeStr
}

func RepoSubmitTypeExistOf(str string) bool {
	for _, v := range RepoSubmitTypeStr {
		if v == str {
			return true
		}
	}
	return false
}

var DeptParentReflect sync.Map

func InitDeptParentReflect() {
	DeptParentReflect.Store("server/", "Server-Projects")
	DeptParentReflect.Store("gyos/", "Gyos-Projects")
	DeptParentReflect.Store("institute/", "Institute-Projects")
	DeptParentReflect.Store("gfb/", "Gfb-Projects")
	DeptParentReflect.Store("desktop/", "Desktop-Projects")
}

func InitGerritPasswd() {
	go GetGerritGeneralHttpPasswd()
}

func InitCompressTypeReflectMap() {
	CompressTypeReflectMap.Store("*.tar.gz", "tar -zxvf *.tar.gz -C source/")
	CompressTypeReflectMap.Store("*.tar.xz", "tar -xvf *.tar.xz -C source/")
	CompressTypeReflectMap.Store("*.xz", "xz -dc *.xz > source/")
	CompressTypeReflectMap.Store("*.tar", "tar -xvf *.tar -C source/")
	CompressTypeReflectMap.Store("*.tar.bz2", "tar -xvf *.tar.bz2 -C source/")
	CompressTypeReflectMap.Store("*.bz2", "bzip2 -dc *.bz2 > source/")
	CompressTypeReflectMap.Store("*.zip", "unzip -d source/ *.zip")
}

func InitKojiArchReflectMap() {
	KojiArchReflect.Store(0, "noarch")
	KojiArchReflect.Store(1, "x86_64")
	KojiArchReflect.Store(2, "aarch64")
	KojiArchReflect.Store(3, "mips64el")
	KojiArchReflect.Store(4, "loongarch64")
	KojiArchReflect.Store(5, "i686")
	KojiArchReflect.Store(6, "sw_64")
}

func GetGerritGeneralHttpPasswd() bool {
	timeout := 30 * time.Second
	// 创建一个带有超时的context
	ctx, cancel := context.WithTimeout(context.Background(), timeout)
	defer cancel()

	// 使用context创建命令
	cmdString := fmt.Sprintf("ssh -p 29418 %s gerrit set-account --generate-http-password ctdy-build", INTERNAL_CONNECTION_USER)
	cmd := exec.CommandContext(ctx, "bash", "-c", cmdString)
	output, err := cmd.Output()
	if err != nil {
		// 检查是否是超时错误
		if errors.Is(ctx.Err(), context.DeadlineExceeded) {
			slog.Error.Printf("%v : %v %v ", CommandExecTimeoutError{
				Code:   0x00000001,
				Reason: "当前命令执行超时，请稍后重试",
				Error:  errors.New("当前命令执行超时，请稍后重试"),
			}.Reason, string(output), err)
			return false
		} else {
			slog.Error.Printf("%v : %v %v", commandExecFailedError.Reason, string(output), err)
			return false
		}
	}
	if !strings.Contains(string(output), "New password:") {
		slog.Error.Printf(" %v : %v %v", commandExecFailedError.Reason, string(output), err)
		return false
	}

	lines := strings.Fields(string(output))
	if len(lines) != 3 {
		slog.Error.Printf("  %v: %v %v  ", commandExecFailedError.Reason, string(output), err)
		return false
	}

	G_GerritToken = lines[2]
	slog.Error.Println("[CodeBase GerritInitialization] Gerrit HTTP Token: ", G_GerritToken)
	return true
}

func (t *Timestamp) UnmarshalJSON(data []byte) error {
	if string(data) == "null" {
		return nil
	}

	// 去除JSON字符串的引号
	str := strings.Trim(string(data), "\"")

	// 支持的时间格式列表
	formats := []string{
		"2006-01-02 15:04:05.000000000",  // Gerrit常用格式
		"2006-01-02 15:04:05.000000",     // 微秒格式
		"2006-01-02 15:04:05.000",        // 毫秒格式
		"2006-01-02 15:04:05",            // 秒格式
		time.RFC3339Nano,                 // RFC3339纳秒格式
		time.RFC3339,                     // RFC3339格式
		"2006-01-02T15:04:05.000000000Z", // ISO8601纳秒格式
		"2006-01-02T15:04:05.000000Z",    // ISO8601微秒格式
		"2006-01-02T15:04:05.000Z",       // ISO8601毫秒格式
		"2006-01-02T15:04:05Z",           // ISO8601秒格式
	}

	var parseErr error
	for _, format := range formats {
		if parsedTime, err := time.Parse(format, str); err == nil {
			t.Time = parsedTime
			return nil
		} else {
			parseErr = err
		}
	}

	return fmt.Errorf("无法解析时间格式 '%s': %v", str, parseErr)
}

func (t Timestamp) MarshalJSON() ([]byte, error) {
	if t.Time.IsZero() {
		return []byte("null"), nil
	}
	// 使用Gerrit常用的时间格式进行序列化
	formatted := fmt.Sprintf("\"%s\"", t.Time.Format("2006-01-02 15:04:05.000000000"))
	return []byte(formatted), nil
}

func stringSplitBySemicolon(fullString string, splitPatternStartTag string, splitPatternEndTag string) []string {
	startIndex := strings.Index(fullString, splitPatternStartTag)
	if startIndex == -1 {
		slog.Error.Println("Get Start Index Failed")
		return nil
	}
	subStr := fullString[startIndex+1:]
	EndIndex := strings.Index(subStr, splitPatternEndTag)
	if EndIndex == -1 {
		slog.Error.Println("Get EndIndex Failed")
		return nil
	}
	spliceBytes := strings.Split(subStr[:EndIndex], ":")
	return spliceBytes
}

func WalkMatch(root string) (string, string) {
	var CompressTypeTag string = ""
	var CompressCommand string = ""
	InitCompressTypeReflectMap()
	err := filepath.Walk(root, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}
		if info.IsDir() {
			return nil
		}
		CompressTypeReflectMap.Range(func(k, v interface{}) bool {
			if matched, err := filepath.Match(k.(string), filepath.Base(path)); err != nil {
				slog.Error.Println(err)
			} else if matched {
				CompressTypeTag = k.(string)
				CompressCommand = v.(string)
			}
			return true
		})
		return nil
	})
	if err != nil {
		slog.Error.Println(err)
	}
	return CompressTypeTag, CompressCommand
}

func sendHTTPRequest(requestURL string, requestMethod string, requestBasicAuthUserName string, requestBasicAuthPassword string, requestBodyParams []byte) string {
	// 创建一个自定义的Transport，禁用SSL证书验证
	transport := &http.Transport{
		TLSClientConfig: &tls.Config{InsecureSkipVerify: true},
	}
	// 创建一个自定义的Client
	client := &http.Client{Transport: transport}
	// 创建一个请求
	slog.Error.Printf("RequestURL: %v\n", bytes.NewBufferString(requestURL).String())
	req, err := http.NewRequest(requestMethod, bytes.NewBufferString(requestURL).String(), bytes.NewBuffer(requestBodyParams))
	if err != nil {
		slog.Error.Println("创建请求失败:", err)
		return err.Error()
	}
	// 设置请求的认证信息
	req.SetBasicAuth(requestBasicAuthUserName, requestBasicAuthPassword)
	req.Header.Set("Content-Type", "application/json")
	// 发送请求
	resp, err := client.Do(req)
	if err != nil {
		slog.Error.Println("发送请求失败:", err)
		return err.Error()
	}
	// 读取响应内容
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		slog.Error.Println("读取响应失败:", err)
		return err.Error()
	}
	defer func(Body io.ReadCloser) {
		err := Body.Close()
		if err != nil {
			slog.Error.Println("关闭读写缓冲失败", err)
		}
	}(resp.Body)
	// 响应内容存储
	retData := string(body)
	return retData
}

func RequestGerritSSHQuery(gerritURL string) (string, error) {
	digitIndex := strings.LastIndex(gerritURL, "/")
	cmd := exec.Command("ssh", "-p", "29418", "-i", INTERNAL_SSH_KEY_FILE, INTERNAL_CONNECTION_USER, "gerrit query --format JSON "+gerritURL[digitIndex+1:]+" --comments --all-reviewers --commit-message --current-patch-set --no-limit")
	sshRetData, err := cmd.Output()
	if err != nil {
		slog.Error.Println("exec cmd failed, output:", string(sshRetData))
		return "", sshGerritQueryError.Error
	}
	//slog.Info.Println("[Gerrit 入库] Gerrit SSH信息 : ", string(sshRetData))
	retData := string(sshRetData)
	if strings.Contains(retData, "Unauthorized") {
		slog.Error.Println(gerritTokenInvalidError.Reason)
		return "", gerritTokenInvalidError.Error
	}
	return retData, nil
}

type GerritDataInvalidError struct {
	Code   int    `default:"0x00001011"`
	Reason string `default:"Gerrit JSON数据格式不正确，无法解析"`
	Error  error  `default:"errors.New(\"Gerrit JSON数据格式不正确，无法解析\")"`
}

func GerritCommitDataExtract(requestGerritData string) (ChangeInfo, string, []string, string, error) {
	var gerritChangeInfo ChangeInfo
	var GerritCIURL string
	var ReviewUserList []string
	requestGerritData = strings.Split(requestGerritData, `{"type":"stats","rowCount":1,`)[0]
	err := json.Unmarshal([]byte(requestGerritData), &gerritChangeInfo)
	if err != nil {
		slog.Error.Println("GerritCommitDataExtract json.Unmarshal failed")
		return gerritChangeInfo, "", []string{}, "", GerritDataInvalidError{
			Code:   0x00001011,
			Reason: "Gerrit JSON数据格式不正确，无法解析",
			Error:  errors.New("Gerrit JSON数据格式不正确，无法解析"),
		}.Error
	}

	if !strings.Contains(gerritChangeInfo.Status, "MERGED") {
		slog.Error.Println("Gerrit为非MERGED状态，禁止提交")
		return gerritChangeInfo, "", []string{}, "", GerritUnmergedError{
			Code:   0x00000011,
			Reason: "Gerrit未合并，禁止提交",
			Error:  errors.New("Gerrit未合并，禁止提交"),
		}.Error
	}

	commentsData := gerritChangeInfo.Comments
	GerritCIURLRegExpression := regexp.MustCompile(`https?://ci-jenkins.kylinos.cn\S+`)
	for _, comment := range commentsData {
		if len(comment.Reviewer.Name) > 1 && comment.Reviewer.Username == "jenkins" {
			if strings.Contains(comment.Message, `Build Successful`) && strings.Contains(comment.Message, `: SUCCESS`) {
				GerritCIURL = GerritCIURLRegExpression.FindString(comment.Message)
			}
		}
	}
	commitMsgInfo := gerritChangeInfo.CommitMessage
	for _, reviewer := range gerritChangeInfo.AllReviewers {
		ReviewUserList = append(ReviewUserList, reviewer.Name)
	}
	return gerritChangeInfo, commitMsgInfo, ReviewUserList, GerritCIURL, nil
}

func GetJenkinsInfo(jenkinsURL string) (string, error) {
	var jobName, jobID = "", ""
	jenkinsHostURLRegExp := regexp.MustCompile(`https?://[^\s/]+/`)
	jenkinsHostURL := jenkinsHostURLRegExp.FindString(jenkinsURL)
	jenkinsURLRegExp := regexp.MustCompile(`https?://[^\s/]+/job/([^/]+)/(\d+)/`)
	matches := jenkinsURLRegExp.FindStringSubmatch(jenkinsURL)
	var jenkinsJobNotExistError = JenkinsJobNotExistError{
		Code:   0x00000111,
		Reason: "Jenkins Job不存在",
		Error:  errors.New("Jenkins Job不存在"),
	}
	if len(matches) == 3 {
		jobName = matches[1]
		jobID = matches[2]
	} else {
		slog.Error.Println(jenkinsJobNotExistError.Reason)
		return "", jenkinsJobNotExistError.Error
	}
	ctx := context.Background()
	defer ctx.Done()
	jenkins := gojenkins.CreateJenkins(nil, jenkinsHostURL, utils.JenkinsAk, utils.JenkinsSk)
	_, err := jenkins.Init(ctx)
	if err != nil {
		var jenkinsContextNotExistError = JenkinsContextNotExistError{
			Code:   0x00001000,
			Reason: "Jenkins Context不存在",
			Error:  errors.New("Jenkins Context不存在"),
		}
		slog.Error.Println(jenkinsContextNotExistError.Reason)
		return "", jenkinsContextNotExistError.Error
	}
	jobIDNumber, err := strconv.Atoi(jobID)
	build, err := jenkins.GetBuild(ctx, jobName, int64(jobIDNumber))
	if err != nil {
		slog.Error.Println(jenkinsJobNotExistError.Reason)
		return "", jenkinsJobNotExistError.Error
	}
	buildResult := build.GetConsoleOutput(ctx)
	return buildResult, nil
}

func GetCIKojisInfoAll(kojiTaskURLs []string) ([]model.KojiInfo, error) {
	var kojiInfos []model.KojiInfo
	for _, kojiTaskURL := range kojiTaskURLs {
		currentKojiURL := strings.Trim(strings.Trim(kojiTaskURL, "Koji Task: "), " closed Successfully")
		singleKoji, _ := KojiGetTaskInfo(currentKojiURL)
		if singleKoji == nil {
			return nil, errors.New("CI Koji无效，禁止提交")
		}
		singleInfo := model.Koji.GetKojiInfoFromList(singleKoji)
		if singleInfo == nil {
			return nil, errors.New("CI Koji无效，禁止提交")
		}
		kojiInfos = append(kojiInfos, *singleInfo)
	}
	return kojiInfos, nil
}

func GetCIKojisInfo(kojiTaskURLs []string, kojiPlatforms []model.KojiPlatform) ([]model.KojiInfo, error) {
	var kojiInfos []model.KojiInfo
	ciKojiInvalidError := CIKojiInvalidError{
		Code:   0x00001001,
		Reason: "CI Koji信息无效，请联系管理员",
		Error:  errors.New("CI Koji信息无效，请联系管理员"),
	}

	for _, kojiTaskURL := range kojiTaskURLs {
		currentKojiURL := strings.Trim(strings.Trim(kojiTaskURL, "Koji Task: "), " closed Successfully")
		singleKoji, _ := KojiGetTaskInfo(currentKojiURL)
		if singleKoji == nil {
			return nil, ciKojiInvalidError.Error
		}

		singleInfo := model.Koji.GetKojiInfoFromList(singleKoji)
		if singleInfo == nil {
			return nil, ciKojiInvalidError.Error
		}

		for j := 0; j < len(kojiPlatforms); j++ {
			if strings.Contains(kojiPlatforms[j].Addr, "************:80") {
				kojiPlatforms[j].Addr = strings.Split(kojiPlatforms[j].Addr, ":80")[0]
			}
			if strings.Contains(singleInfo.Ip, ":8242") {
				kojiInfos = append(kojiInfos, *singleInfo)
				break
			}
			if kojiPlatforms[j].Addr == singleInfo.Ip {
				kojiInfos = append(kojiInfos, *singleInfo)
				break
			}
		}
	}
	if kojiInfos == nil {
		return nil, KojiTagProductTagNotMatchError{
			Code:   0x00001002,
			Reason: "Gerrit链接地址中最后一次CI任务没有编译信息，请进入有编译任务的CI链接Retrigger并等待完成后再提交门禁入库",
			Error:  errors.New("Gerrit链接地址中最后一次CI任务没有编译信息，请进入有编译任务的CI链接Retrigger并等待完成后再提交门禁入库"),
		}.Error
	}
	return kojiInfos, nil
}

func GetGerritInfoByUrl(gerritURL string, productId int) (*model.GerritInfo, string) {
	var GerritInfoStruct model.GerritInfo
	var gerritChangeInfo ChangeInfo
	var gerritCIURL string
	GerritInfoStruct.Gerrit_url = gerritURL
	retData, err := RequestGerritSSHQuery(gerritURL)
	if err != nil {
		slog.Error.Println("Gerrit信息获取失败：", err)
		return nil, sshGerritQueryError.Reason
	}
	gerritChangeInfo, commitMsgInfo, ReviewUserList, gerritCIURL, err := GerritCommitDataExtract(retData)
	GerritInfoStruct.Commit = commitMsgInfo
	GerritInfoStruct.Reviewers = ReviewUserList
	// Case：Jenkins 迁移URL变更导致任务链接中的旧地址需强制替换为新地址
	if strings.Contains(gerritCIURL, "server.kylinos.cn:8085") {
		gerritCIURL = strings.Replace(gerritCIURL, "server.kylinos.cn:8085", "ci-jenkins.kylinos.cn", -1)
	}
	if err != nil {
		slog.Error.Println("Gerrit信息提取失败：", err)
		return nil, err.Error()
	}

	GerritInfoStruct.GerritRepoName = gerritChangeInfo.Project
	GerritInfoStruct.GerritBranch = gerritChangeInfo.Branch
	GerritInfoStruct.CommitID = gerritChangeInfo.CurrentPatchSet.Revision

	buildResult, err := GetJenkinsInfo(gerritCIURL)
	GerritInfoStruct.Jenkins_url = gerritCIURL
	if err != nil {
		slog.Error.Println("Jenkins信息获取失败：", err)
		return nil, err.Error()
	}

	kojiTaskRegExp := regexp.MustCompile(`Koji Task: https?://(?:[^\s/$.?#]+\.)+[^\s/$.?#]+(?::\d+)?/koji/taskinfo\?taskID=\d+ closed Successfully`)
	kojiTaskURLs := kojiTaskRegExp.FindAllString(buildResult, -1)
	kojiTaskURLs = utils.RemoveRepeatElement(kojiTaskURLs)

	currentProduct := model.GetProductListById(productId)
	admissionKojis := currentProduct.ProductKojis
	kojiPlatforms := model.Koji.GetKojiPlatformByIds(admissionKojis)
	if kojiPlatforms == nil {
		return nil, "产品列表中Koji信息不存在，请联系管理员"
	}
	kojiInfos, err := GetCIKojisInfo(kojiTaskURLs, kojiPlatforms)
	if err != nil {
		slog.Error.Println("Koji信息获取失败：", err)
		return nil, err.Error()
	}
	kojiInfosAll, err := GetCIKojisInfoAll(kojiTaskURLs)
	GerritInfoStruct.KojiInfoAll = kojiInfosAll
	if err != nil {
		slog.Error.Println("Koji信息获取失败：", err)
		return nil, err.Error()
	}
	GerritInfoStruct.KojiInfo = kojiInfos
	//slog.Info.Println("[Gerrit 入库] GerritInfoStruct: ", GerritInfoStruct)
	//slog.Info.Println("[Gerrit 入库] KojiInfoStruct: ", kojiInfos)
	return &GerritInfoStruct, ""
}

/*func GetGerritInfoByHttpUrl(gerritURL string, productId int) (*model.GerritInfo, string) {
	var GerritInfoStruct model.GerritInfo
	GerritInfoStruct.Gerrit_url = gerritURL
	digitIndex := strings.LastIndex(gerritURL, "/")
	apiGerritURL := INTERNAL_HTTPS_CONNECTION_GERRIT_URL + "/a/changes" + gerritURL[digitIndex:] + "/detail"
	retData := sendHTTPRequest(apiGerritURL, "GET", utils.GerritAk, strings.TrimSpace(G_GerritToken), nil)

	if strings.Contains(retData, "Unauthorized") {
		slog.Error.Println("gerrit令牌Token失效，需要重新创建Token")
		return nil, "Gerrit令牌失效，请联系管理员"
	}
	tag1 := INTERNAL_HTTPS_CONNECTION_JENKINS_URL + "/job/"
	tag2 := " : SUCCESS"
	index2 := strings.LastIndex(retData, tag2)
	if index2 == -1 {
		slog.Error.Println("get Verified index failed")
		return nil, "Gerrit信息获取失败：流程状态有误"
	}
	index1 := strings.LastIndex(retData[:index2], tag1)
	if index1 == -1 {
		tagIp := INTERNAL_HTTPS_CONNECTION_JENKINS_IPURL + "/job/"
		index1 = strings.LastIndex(retData[:index2], tagIp)
		if index1 == -1 {
			slog.Error.Println("get url index failed")
			slog.Error.Println("GetGerritInfoByUrl LastIndex failed:", tag1)
			return nil, "Gerrit信息获取失败：url获取失败"
		}
	}
	mergeableTag := "status\""
	mergeableEndTag := ","
	mergeableStatusBytes := stringSplitBySemicolon(retData, mergeableTag, mergeableEndTag)
	mergeableStatus := mergeableStatusBytes[1]
	if mergeableStatus != "\"MERGED\"" {
		slog.Error.Println("Gerrit为非MERGED状态，禁止提交")
		return nil, "Gerrit为非MERGED状态，禁止提交"
	}
	commitmsgTag := "subject"
	commitmsgEndTag := ","
	commitMsgBytes := stringSplitBySemicolon(retData, commitmsgTag, commitmsgEndTag)
	commitMsgInfo := commitMsgBytes[1]
	GerritInfoStruct.Commit = commitMsgInfo

	reviewersSplitTag := "labels\":"
	reviewersEndSplitTag := "permitted_labels"
	reviewersDataIndex := strings.Index(retData, reviewersSplitTag)
	if reviewersDataIndex == -1 {
		slog.Error.Println("Get commitmsg Index Failed")
		return nil, "Gerrit信息获取失败：提交信息获取失败"
	}
	reviewersDataEndIndex := strings.Index(retData[reviewersDataIndex:], reviewersEndSplitTag)
	if reviewersDataEndIndex == -1 {
		slog.Error.Println("Get commitmsg EndIndex Failed")
		return nil, "Gerrit信息获取失败：提交信息end获取失败"
	}
	reviewersDataJsonString := retData[reviewersDataIndex+11 : reviewersDataIndex+reviewersDataEndIndex+1]
	var JsonRes []ReviewersJsonResult
	var ReviewUserList []string
	json.Unmarshal([]byte(reviewersDataJsonString), &JsonRes)
	for data := range JsonRes {
		if JsonRes[data].Type == "Code-Review" {
			ReviewUserList = append(ReviewUserList, JsonRes[data].ReviewerData.UserName)
		}
		if JsonRes[data].Type == "Verified" {
			ReviewUserList = append(ReviewUserList, JsonRes[data].ReviewerData.UserName)
		}
	}
	ReviewUserList = utils.RemoveRepeatElement(ReviewUserList)
	for i := 0; i < len(ReviewUserList); i++ {
		if ReviewUserList[i] == "jenkins" {
			ReviewUserList = append(ReviewUserList[:i], ReviewUserList[i+1:]...)
		}
	}
	GerritInfoStruct.Reviewers = ReviewUserList
	jenkinsURL := retData[index1:index2]
	GerritInfoStruct.Jenkins_url = jenkinsURL
	retData = sendHTTPRequest(jenkinsURL+"consoleText", "GET", utils.JenkinsAk, utils.JenkinsSk, nil)
	tmpList := strings.Split(retData, "Created Task: ")
	if len(tmpList) == 1 {
		slog.Error.Println("Jenkins地址字符串分割失败")
		return nil, "Jenkins地址字符串分割失败"
	}

	currentProduct := model.GetProductListById(productId)
	admissionKojis := currentProduct.ProductKojis
	kojiPlatforms := model.GetKojiPlatformByIds(admissionKojis)
	if kojiPlatforms == nil {
	// 如果kojiPlatforms为nil，则表示产品列表中Koji信息不存在
		return nil, "产品列表中Koji信息不存在，请联系管理员"
	}

	var kojiInfos []model.KojiInfo
	for i := 1; i < len(tmpList); i++ {
		idx := strings.Index(tmpList[i], "\n")
		if idx == -1 {
			slog.Error.Println("idx遍历查询失败")
			return nil, "idx遍历查询失败"
		}
		currentKojiURL := tmpList[i][:idx]
		singleKoji, reason := KojiGetTaskInfo(currentKojiURL)
		if singleKoji == nil {
		// 如果singleKoji为nil，则表示获取koji信息失败
			return nil, reason
		}
		singleInfo := model.GetKojiInfoFromList(singleKoji)
		if singleInfo == nil {
			return nil, "查询koji信息失败"
		}

		for j := 0; j < len(kojiPlatforms); j++ {
			if strings.Contains(kojiPlatforms[j].Addr, ":80") {
				kojiPlatforms[j].Addr = strings.Split(kojiPlatforms[j].Addr, ":80")[0]
			}
			if kojiPlatforms[j].Addr == singleInfo.Ip {
				kojiInfos = append(kojiInfos, *singleInfo)
				break
			}
		}
	}
	if kojiInfos == nil {
		return nil, "目标任务不在产品列表中"
	}
	GerritInfoStruct.KojiInfo = kojiInfos
	return &GerritInfoStruct, ""
}*/

func CheckRepoExists(repoName string, requestBasicAuthUserName string, requestBasicAuthPassword string) (bool, string) {
	gerritAPIURL := fmt.Sprintf("%s", INTERNAL_HTTPS_CONNECTION_GERRIT_URL+GERRIT_API_PROJECT_URL+repoName)
	retData := sendHTTPRequest(gerritAPIURL, "GET", requestBasicAuthUserName, requestBasicAuthPassword, nil)
	if strings.Contains(retData, repoName) && strings.Contains(retData, "\"state\":\"ACTIVE\"") {
		return true, ""
	} else if strings.Contains(retData, "Not found") {
		return false, "指定仓库不存在"
	} else {
		return false, "未授权"
	}
}

func GetInitialRevisionFromBranchName(repoName string, branchName string, requestBasicAuthUserName string, requestBasicAuthPassword string) (string, bool) {
	gerritAPIURL := fmt.Sprintf(INTERNAL_HTTPS_CONNECTION_GERRIT_URL+GERRIT_API_BRANCHES_URL, repoName, branchName)
	retData := sendHTTPRequest(gerritAPIURL, "GET", requestBasicAuthUserName, requestBasicAuthPassword, nil)
	slog.Error.Printf("GetInitialRevisionFromBranchName RetData:%v\n", retData)
	if strings.Contains(retData, "revision") {
		revisionPattern, _ := regexp.Compile(`"revision":"(.*?)"`)
		return revisionPattern.FindStringSubmatch(retData)[1], true
	} else {
		return "", false
	}
}

func CheckBranchExists(repoName string, branchName string, requestBasicAuthUserName string, requestBasicAuthPassword string) (bool, string) {
	gerritAPIURL := fmt.Sprintf(INTERNAL_HTTPS_CONNECTION_GERRIT_URL+GERRIT_API_BRANCHES_URL, repoName, branchName)
	retData := sendHTTPRequest(gerritAPIURL, "GET", requestBasicAuthUserName, requestBasicAuthPassword, nil)
	if strings.Contains(retData, "\"ref\":\"refs/heads/"+branchName+"\"") {
		return true, ""
	} else if strings.Contains(retData, "Not found") {
		return false, "指定分支不存在"
	} else {
		return false, "未授权"
	}
}

func CreateNewBranchCheckRepoExists(repoName string, branchName string, supplementDescription string, requestBasicAuthUserName string, requestBasicAuthPassword string) (bool, string, string) {
	// 判断仓库是否存在
	var errString string
	var isRepoExists bool
	var deptParentProject string
	InitDeptParentReflect()
	repoName = strings.ReplaceAll(repoName, "/", "%2F")
	if !strings.Contains(repoName, "%2F") {
		repoName = fmt.Sprintf("server%s%s", "%2F", repoName)
	}
	DeptParentReflect.Range(func(k, v interface{}) bool {
		if strings.Contains(strings.ReplaceAll(repoName, "%2F", "/"), k.(string)) {
			deptParentProject = v.(string)
		}
		return true
	})
	isRepoExists, errString = CheckRepoExists(repoName, requestBasicAuthUserName, requestBasicAuthPassword)
	if errString == "未授权" {
		return false, "未授权", repoName
	}
	if isRepoExists == false {
		newRepoParams := ProjectInput{
			Parent:                           deptParentProject,
			Description:                      supplementDescription,
			PermissionsOnly:                  false,
			CreateEmptyCommit:                true,
			SubmitType:                       RepoSubmitType.String(REBASE_IF_NECESSARY),
			Branches:                         []string{branchName},
			Owners:                           []string{},
			UseContributorAgreements:         "",
			UseSignedOffBy:                   "",
			CreateNewChangeForAllNotInTarget: "",
			UseContentMerge:                  "",
			RequireChangeID:                  "",
			MaxObjectSizeLimit:               "",
			PluginConfigValues:               nil,
		}
		repoCreateStatus, errString := CreateNewRepo(repoName, newRepoParams, requestBasicAuthUserName, requestBasicAuthPassword)
		if repoCreateStatus == false {
			return false, "仓库不存在，创建新仓库失败，请管理员手动检查" + errString, repoName
		}
		isRepoExists = true
		errString = ""
	}
	return isRepoExists, errString, repoName
}

func CreateNewBranchParamsSanityCheck(repoName string, branchName string, rightsInheritanceFrom string, initialRevision string, requestBasicAuthUserName string, requestBasicAuthPassword string) (BranchInput, string) {
	var createBranchParameters = BranchInput{
		Ref:      branchName,
		Revision: initialRevision,
	}
	if rightsInheritanceFrom != "" {
		createBranchParameters.Revision = rightsInheritanceFrom
	}

	if rightsInheritanceFrom == "" && initialRevision == "" {
		createBranchParameters.Revision = ""
	}
	return createBranchParameters, ""
}

// CreateNewBranch /**
// * repoName：在哪个仓库下创建分支
// * branchName: 创建的新分支名称
// * rightInheritanceFrom: 新分支继承自哪个tag(eg: os-release-v10-sp3)
// * initialRevision: 新分支继承自哪个Revision
// * supplementDescription: 新建的分支增补的描述信息
// * Deprecated: newRepoInheritanceRights: 若为新创建项目，则权限继承自哪个系列
// * requestBasicAuthUserName：HTTP请求用户名
// * requestBasicAuthPassword: HTTP请求密码
// */
func CreateNewBranch(repoName string, branchName string, rightsInheritanceFrom string, initialRevision string, supplementDescription string, requestBasicAuthUserName string, requestBasicAuthPassword string) (bool, string, string) {
	if len(repoName) < 2 {
		slog.Error.Println("CreateNewBranch 失败，仓库名称为空")
		return false, "失败，仓库名称为空", ""
	}

	if len(branchName) < 2 {
		slog.Error.Println("CreateNewBranch 失败，分支名为空")
		return false, "失败，分支名为空", ""
	}
	isRepoExists, errString, repoName := CreateNewBranchCheckRepoExists(repoName, branchName, supplementDescription, requestBasicAuthUserName, requestBasicAuthPassword)
	if !isRepoExists {
		slog.Error.Printf("%v 仓库不存在，已尝试创建仓库，返回结果仍失败，错误：%v\n", repoName, errString)
		return false, errString, repoName
	}

	// 判断分支是否存在
	isBranchExists, _ := CheckBranchExists(repoName, branchName, requestBasicAuthUserName, requestBasicAuthPassword)
	if isBranchExists == true {
		// return false, "分支已存在，不能创建已有分支"
		slog.Error.Printf("仓库: %v 分支: %v 均已存在，流程继续", repoName, branchName)
	}
	slog.Error.Printf("rightsInheritanceFrom:%v\n", rightsInheritanceFrom)
	if len(initialRevision) < 1 && rightsInheritanceFrom != "" {
		retInitialRevision, getInitialRevisionStatus := GetInitialRevisionFromBranchName(repoName, rightsInheritanceFrom, requestBasicAuthUserName, requestBasicAuthPassword)
		initialRevision = retInitialRevision
		if getInitialRevisionStatus == false {
			return false, "initialRevision未传入，rightsInheritanceFrom值不正确，未获取到对应的Revision值", repoName
		}
	}

	gerritAPIURL := fmt.Sprintf(INTERNAL_HTTPS_CONNECTION_GERRIT_URL+GERRIT_API_BRANCHES_URL, repoName, branchName)

	createBranchParameters, errString := CreateNewBranchParamsSanityCheck(repoName, branchName, rightsInheritanceFrom, initialRevision, requestBasicAuthUserName, requestBasicAuthPassword)
	if errString != "" {
		return false, errString, repoName
	}

	jsonMarshaledBytes, err := json.Marshal(createBranchParameters)
	if err != nil {
		slog.Error.Println("Error: ", err)
	}
	retData := sendHTTPRequest(gerritAPIURL, "PUT", requestBasicAuthUserName, requestBasicAuthPassword, jsonMarshaledBytes)
	slog.Error.Println("CreateNewBranch RetData:", retData)
	if strings.Contains(retData, "\"revision\":") && strings.Contains(retData, "\"ref\": \"refs/heads/") {
		slog.Error.Println("新分支创建成功")
	} else if strings.Contains(retData, "already exists") {
		slog.Error.Println("分支已存在，可继续流程")
	} else if !strings.Contains(retData, ")]}'") && !strings.Contains(retData, "already exists") {
		return false, "创建新分支失败", repoName
	}

	// ForeDescriptionData := GetRepoDescription(repoName, requestBasicAuthUserName, requestBasicAuthPassword)
	// descriptionInput := ProjectDescriptionInput{
	//	 Description:   ForeDescriptionData + "\n" + supplementDescription,
	//	 CommitMessage: "门禁系统自动补充分支信息",
	// }
	// SetRepoDescription(repoName, descriptionInput, requestBasicAuthUserName, requestBasicAuthPassword)

	return true, "新分支创建成功", repoName
}

func CreateNewRepo(repoName string, repoParameters ProjectInput, requestBasicAuthUserName string, requestBasicAuthPassword string) (bool, string) {
	gerritAPIURL := fmt.Sprintf("%s", INTERNAL_HTTPS_CONNECTION_GERRIT_URL+GERRIT_API_PROJECT_URL+repoName)
	if repoParameters.SubmitType == "" {
		repoParameters.SubmitType = RepoSubmitType.String(REBASE_IF_NECESSARY)
	}
	//submitType defaults value MERGE_IF_NECESSARY, could be REBASE_IF_NECESSARY, REBASE_ALWAYS, FAST_FORWARD_ONLY, MERGE_ALWAYS, CHERRY_PICK
	jsonMarshaledBytes, err := json.Marshal(repoParameters)
	slog.Error.Printf("CreateNewRepoParams: %v\n", string(jsonMarshaledBytes))
	if err != nil {
		slog.Error.Println("Error:  ", err)
	}
	retData := sendHTTPRequest(gerritAPIURL, "PUT", requestBasicAuthUserName, requestBasicAuthPassword, jsonMarshaledBytes)
	slog.Error.Printf("CreateNewRepoRetData: %v\n", retData)
	if strings.Contains(retData, repoName) && strings.Contains(retData, "\"state\":\"ACTIVE\"") {
		return true, ""
	} else {
		return false, "创建仓库失败"
	}
}

func GetGerritGroupNameByUUID(groupUUID string, requestBasicAuthUserName string, requestBasicAuthPassword string) (string, bool) {
	gerritAPIURL := fmt.Sprintf("https://" + utils.DomainName + ":" + strconv.Itoa(utils.GerritPort) + "/a/groups/" + groupUUID + "/name")
	retData := sendHTTPRequest(gerritAPIURL, "GET", requestBasicAuthUserName, requestBasicAuthPassword, nil)
	if strings.Contains(retData, ")]}'") {
		return strings.ReplaceAll(retData, ")]}'", ""), true
	} else {
		return "", false
	}
}

func CreateProjectTag(repoName string, tagName string, targetTagRevision string, requestBasicAuthUserName string, requestBasicAuthPassword string, kojiList []*model.KojiList, xflowId int) TagInfo {
	gerritAPIURL := fmt.Sprintf(INTERNAL_HTTPS_CONNECTION_GERRIT_URL+"/a/projects/%s/tags/%s", repoName, tagName)
	var kojiTaskURLs string
	for _, koji := range kojiList {
		kojiTaskURLs += koji.Url + "\n"
	}
	annotatedMsg := fmt.Sprintf("门禁系统源码仓管理系统自动创建Tag - 门禁任务ID：%v Koji任务ID：%v", xflowId, kojiTaskURLs)
	newTagInputParameters := ProjectNewTagInput{
		Ref:      strings.ReplaceAll(tagName, "%2F", "/"),
		Message:  annotatedMsg,
		Revision: targetTagRevision,
	}
	jsonMarshaledBytes, err := json.Marshal(newTagInputParameters)
	slog.Error.Println("CreateProjectTag Params:", string(jsonMarshaledBytes))
	if err != nil {
		slog.Error.Println(" Error: ", err)
	}
	retData := sendHTTPRequest(gerritAPIURL, "PUT", requestBasicAuthUserName, requestBasicAuthPassword, jsonMarshaledBytes)
	slog.Error.Println("CreateProjectTag RetData:", retData)
	if strings.Contains(retData, ")]}'") {
		realData := strings.Split(retData, ")]}'")[1]
		var projectTagInfo TagInfo
		err = json.Unmarshal([]byte(realData), &projectTagInfo)
		if err != nil {
			slog.Error.Println("  Error:  ", err)
		}
		return projectTagInfo
	} else {
		return TagInfo{}
	}
}

func DeleteProjectTag(repoName string, tagName string, requestBasicAuthUserName string, requestBasicAuthPassword string) bool {
	gerritAPIURL := fmt.Sprintf("https://"+utils.DomainName+":"+strconv.Itoa(utils.GerritPort)+"/a/projects/%s/tags/%s", repoName, tagName)
	sendHTTPRequest(gerritAPIURL, "DELETE", requestBasicAuthUserName, requestBasicAuthPassword, nil)
	return true
}

func CheckBranchReviewers(repoName string, branchName string, requestBasicAuthUserName string, requestBasicAuthPassword string, CodeReviewerList []string) bool {
	gerritAPIURL := fmt.Sprintf(INTERNAL_HTTPS_CONNECTION_GERRIT_URL+"/a/projects/%s/reviewers", repoName)
	retData := sendHTTPRequest(gerritAPIURL, "GET", requestBasicAuthUserName, requestBasicAuthPassword, nil)
	for _, codeReviewer := range CodeReviewerList {
		if !strings.Contains(retData, codeReviewer) {
			return false
		}
	}
	return true
}

func AddReviewersToBranch(repoName string, branchName string, CodeReviewList []string, VerifiedList []string, requestBasicAuthUserName string, requestBasicAuthPassword string) bool {
	gerritAPIURL := fmt.Sprintf(INTERNAL_HTTPS_CONNECTION_GERRIT_URL+"/a/projects/%s/reviewers", repoName)
	var retData string
	for _, codeReviewer := range CodeReviewList {
		reviewParameters := ReviewersInput{
			Action:   "ADD",
			Type:     "REVIEWER",
			Filter:   "branch:" + branchName,
			Reviewer: codeReviewer,
		}
		jsonMarshaledBytes, _ := json.Marshal(reviewParameters)
		retData = sendHTTPRequest(gerritAPIURL, "POST", requestBasicAuthUserName, requestBasicAuthPassword, jsonMarshaledBytes)
		slog.Error.Printf("CodeReviewerRequestRetData: %v\n", retData)
	}
	codeReviewListResult := CheckBranchReviewers(repoName, branchName, requestBasicAuthUserName, requestBasicAuthPassword, CodeReviewList)

	for _, verifier := range VerifiedList {
		reviewParameters := ReviewersInput{
			Action:   "ADD",
			Type:     "REVIEWER",
			Filter:   "branch:" + branchName,
			Reviewer: verifier,
		}
		jsonMarshaledBytes, _ := json.Marshal(reviewParameters)
		retData = sendHTTPRequest(gerritAPIURL, "POST", requestBasicAuthUserName, requestBasicAuthPassword, jsonMarshaledBytes)
		slog.Error.Printf("CodeVerifierRequestRetData: %v\n", retData)
	}
	verifiedListResult := CheckBranchReviewers(repoName, branchName, requestBasicAuthUserName, requestBasicAuthPassword, VerifiedList)
	return codeReviewListResult && verifiedListResult
}

func AddCodeReviewLabel(repoName string, branchName string, CodeReviewList []string, VerifiedList []string, CodeReadList []string, CodeSubmitList []string, requestBasicAuthUserName string, requestBasicAuthPassword string) bool {
	gerritAPIURL := fmt.Sprintf("%s", INTERNAL_HTTPS_CONNECTION_GERRIT_URL+GERRIT_API_PROJECT_URL+repoName+"/access")
	var accessSectionInfoInput = make(map[string]AccessSectionInfo)
	var permissionInfoInput = make(map[string]PermissionInfo)
	var codeReviewRuleInput = make(map[string]PermissionRuleInfo)
	var verifiedRuleInput = make(map[string]PermissionRuleInfo)
	var submitRuleInput = make(map[string]PermissionRuleInfo)
	var readRuleInput = make(map[string]PermissionRuleInfo)
	if len(CodeReviewList) > 0 {
		for _, reviewerName := range CodeReviewList {
			codeReviewRuleInput["user:"+reviewerName] = PermissionRuleInfo{
				Action: "ALLOW",
				Force:  false,
				Min:    -2,
				Max:    2,
			}
		}
	}
	if len(VerifiedList) > 0 {
		for _, verifierName := range VerifiedList {
			verifiedRuleInput["user:"+verifierName] = PermissionRuleInfo{
				Action: "ALLOW",
				Force:  false,
				Min:    -1,
				Max:    1,
			}
		}
	}

	verifiedRuleInput["user:ctdy-build"] = PermissionRuleInfo{
		Action: "ALLOW",
		Force:  true,
		Min:    -1,
		Max:    1,
	}
	if len(CodeReadList) > 0 {
		for _, readUserName := range CodeReadList {
			readRuleInput["user:"+readUserName] = PermissionRuleInfo{
				Action: "ALLOW",
			}
		}
	}

	if len(CodeSubmitList) > 0 {
		for _, codeSubmitUser := range CodeSubmitList {
			submitRuleInput["user:"+codeSubmitUser] = PermissionRuleInfo{
				Action: "ALLOW",
			}
		}
	}

	submitRuleInput["user:ctdy-build"] = PermissionRuleInfo{
		Action: "ALLOW",
	}

	permissionInfoInput["label-Code-Review"] = PermissionInfo{
		Label:     "Code-Review",
		Exclusive: false,
		Rules:     codeReviewRuleInput,
	}

	permissionInfoInput["label-Verified"] = PermissionInfo{
		Label:     "Verified",
		Exclusive: false,
		Rules:     verifiedRuleInput,
	}

	permissionInfoInput["submit"] = PermissionInfo{
		Label:     "",
		Exclusive: false,
		Rules:     submitRuleInput,
	}

	permissionInfoInput["read"] = PermissionInfo{
		Label:     "",
		Exclusive: false,
		Rules:     readRuleInput,
	}
	accessSectionInfoInput["refs/heads/"+branchName] = AccessSectionInfo{permissionInfoInput}

	codeReviewLabelAccessInput := map[string]AccessRightsUpdateInfo{
		"add": accessSectionInfoInput,
	}

	jsonMarshaledBytes, _ := json.Marshal(codeReviewLabelAccessInput)
	slog.Error.Println("[Debug] CodeReviewLabelAccessRequestJson")
	slog.Error.Println(string(jsonMarshaledBytes))
	sendHTTPRequest(gerritAPIURL, "POST", requestBasicAuthUserName, requestBasicAuthPassword, jsonMarshaledBytes)
	return true
}

func GetRepoDescription(repoName string, requestBasicAuthUserName string, requestBasicAuthPassword string) string {
	var repoDescription string
	gerritAPIURL := fmt.Sprintf("%s", INTERNAL_HTTPS_CONNECTION_GERRIT_URL+GERRIT_API_PROJECT_URL+repoName+"/description")
	retData := sendHTTPRequest(gerritAPIURL, "GET", requestBasicAuthUserName, requestBasicAuthPassword, nil)
	if len(retData) > len(")]}'") {
		repoDescription = strings.Split(retData, ")]}'")[1]
		if strings.Contains(repoDescription, "Unauthorized") || strings.Contains(repoDescription, "Not Found") {
			return ""
		}
	} else {
		return ""
	}
	return repoDescription
}

func SetRepoDescription(repoName string, descriptionParameters ProjectDescriptionInput, requestBasicAuthUserName string, requestBasicAuthPassword string) (bool, string) {
	gerritAPIURL := fmt.Sprintf("%s", INTERNAL_HTTPS_CONNECTION_GERRIT_URL+GERRIT_API_PROJECT_URL+repoName+"/description")
	descriptionParameters.CommitMessage = "门禁系统自动更新仓库描述"
	jsonMarshaledBytes, err := json.Marshal(descriptionParameters)
	if err != nil {
		slog.Error.Println("Error:", err)
	}
	retData := sendHTTPRequest(gerritAPIURL, "PUT", requestBasicAuthUserName, requestBasicAuthPassword, jsonMarshaledBytes)
	if descriptionParameters.Description == "" {
		return false, "传入的Description值为空，非法"
	}
	if strings.Contains(retData, ")]}'") {
		return true, ""
	} else {
		return false, "Description设置失败"
	}
}

func DownloadSRPMFromStockCenter(srpmFilePath string, xflowId int) (bool, string) {
	srpmFileNameIndex := strings.LastIndex(srpmFilePath, "/")
	srpmName := srpmFilePath[srpmFileNameIndex+1:]
	tmpDirectory := GERRIT_INIT_PARENT_DIR + strconv.Itoa(xflowId) + "/middle-products/"
	err := os.MkdirAll(tmpDirectory, 0755)
	if err != nil {
		return false, ""
	}
	downloadRPM, error := os.OpenFile(tmpDirectory+srpmName, os.O_CREATE|os.O_APPEND|os.O_RDWR, 0666)
	if error != nil {
		return false, "临时目录下无法创建待下载文件，请检查是否有权限访问"
	}

	defer func() { _ = downloadRPM.Close() }()

	response, _ := http.Get(srpmFilePath)
	defer func() { _ = response.Body.Close() }()

	nBytes, _ := io.Copy(downloadRPM, response.Body)
	if nBytes > 0 {
		return true, tmpDirectory + srpmName
	} else {
		return false, strconv.Itoa(-1)
	}
}

func CopyExistsSRPM(srpmFilePath string, xflowId int) (bool, string) {
	srpmFileNameIndex := strings.LastIndex(srpmFilePath, "/")
	srpmName := srpmFilePath[srpmFileNameIndex+1:]
	source, err := os.Open(srpmFilePath) //open the source file
	if err != nil {
		slog.Error.Println("Open SRPMFilePath Failed, Please Recheck SRPM Exists, SRPMFilePath:" + srpmFilePath)
	}
	defer source.Close()
	targetDirectory := GERRIT_INIT_PARENT_DIR + strconv.Itoa(xflowId) + "/middle-products/"
	err = os.MkdirAll(targetDirectory, 0755)
	if err != nil {
		return false, ""
	}
	destination, err := os.Create(targetDirectory + srpmName) //create the destination file
	if err != nil {
		slog.Error.Println("Create Directory Failed, Please Recheck DiskSpace, SRPMFilePath:" + targetDirectory + srpmName)
	}
	defer destination.Close()
	_, err = io.Copy(destination, source) //copy the contents of source to destination file
	if err != nil && destination != nil && source != nil {
		slog.Error.Println("SRPM Copy Failed, destination: " + destination.Name() + ", source: " + source.Name())
	}
	return true, targetDirectory + srpmName
}

func GetSRPMFromStockCenter(srpmFilePath string, xflowId int) (bool, string) {
	var taskStatus bool
	var target string
	if strings.Contains(srpmFilePath, "http") {
		taskStatus, target = DownloadSRPMFromStockCenter(srpmFilePath, xflowId)
	} else {
		taskStatus, target = CopyExistsSRPM(srpmFilePath, xflowId)
	}
	return taskStatus, target
}

func SRPMDecompress(srpmPath string, xflowId int) (bool, string) {
	randSeed := rand.New(rand.NewSource(time.Now().UnixNano())).Float64()
	tmpDirectory := GERRIT_INIT_PARENT_DIR + strconv.Itoa(xflowId) + "/" + strconv.FormatFloat(randSeed, 'E', -1, 32)
	err := os.MkdirAll(tmpDirectory, 0755)
	if err != nil {
		return false, ""
	}
	srpmFileNameIndex := strings.LastIndex(srpmPath, "/")
	srpmName := srpmPath[srpmFileNameIndex+1:]
	srcDirectory := GERRIT_INIT_PARENT_DIR + strconv.Itoa(xflowId) + "/middle-products"
	err = utils.CopyFile(srpmName, srpmName, srcDirectory, tmpDirectory)
	if err != nil {
		return false, "源文件不存在"
	}
	runCommand := fmt.Sprintf("rpm2cpio %v | cpio -div", srpmName)
	decompressCmd := exec.Command("bash", "-c", runCommand)
	decompressCmd.Dir = tmpDirectory
	_ = decompressCmd.Run()
	if _, err := filepath.Glob(path.Join(tmpDirectory, "*.spec")); err == nil {
		return true, path.Join(tmpDirectory, srpmName)
	} else {
		return false, "解压SRPM失败"
	}
}

/*func GerritDirectoryInitialization(srpmPath string) (bool, string) {
	var compressCommand string
	var compressTypeTag string
	srpmLastIndex := strings.LastIndex(srpmPath, "/")
	srpmDirectory := srpmPath[:srpmLastIndex]
	srpmBasePath := path.Join(srpmDirectory, "source/")

	mkGerritDirsCmd := exec.Command("mkdir", "-p", "patches/orig", "patches/kylin", "source")
	mkGerritDirsCmd.Dir = srpmDirectory
	mkGerritDirsCmdBytes, _ := mkGerritDirsCmd.Output()
	mkGerritDirsCmdRaw := string(mkGerritDirsCmdBytes)
	if len(mkGerritDirsCmdRaw) > 1 {
		return false, "创建Gerrit目录结构失败"
	}

	compressTypeTag, compressCommand = WalkMatch(srpmDirectory)

	decomporessSourceCmd := exec.Command("bash", "-c", compressCommand)
	decomporessSourceCmd.Dir = srpmDirectory
	_ = decomporessSourceCmd.Run()
	if _, err := filepath.Glob(path.Join(srpmDirectory, "source/*")); err != nil {
		return false, "解压SRPM失败"
	}

	var getSourceDirectoryName string
	getSourceDirectoryNames, _ := ioutil.ReadDir(srpmBasePath)
	if len(getSourceDirectoryNames) >= 2 {
		return false, "特殊情况，需要单独处理"
	}
	for _, sourceDirectory := range getSourceDirectoryNames {
		getSourceDirectoryName = strings.TrimSpace(sourceDirectory.Name())
	}

	runCommand := fmt.Sprintf("mv %v/* .", getSourceDirectoryName)
	removeSourceFilesCmd := exec.Command("bash", "-c", runCommand)
	removeSourceFilesCmd.Dir = srpmBasePath
	_ = removeSourceFilesCmd.Run()

	runCommand = fmt.Sprintf("rm -rf %v", getSourceDirectoryName)
	removeOriginalSourceDirectoryCmd := exec.Command("bash", "-c", runCommand)
	removeOriginalSourceDirectoryCmd.Dir = srpmBasePath
	_ = removeOriginalSourceDirectoryCmd.Run()

	runCommand = fmt.Sprintf("mv *.patch %v patches/orig/", compressTypeTag)
	mvFilesCmd := exec.Command("bash", "-c", runCommand)
	mvFilesCmd.Dir = srpmDirectory
	mvFilesCmdBytes, _ := mvFilesCmd.Output()
	mvFilesCmdRaw := string(mvFilesCmdBytes)
	if len(mvFilesCmdRaw) > 1 {
		return false, "转移至orig目录失败"
	}

	removeSrcRPMCmd := exec.Command("bash", "-c", "rm -rf *.src.rpm")
	removeSrcRPMCmd.Dir = srpmDirectory
	_ = removeSrcRPMCmd.Run()

	return true, srpmDirectory
} */

func MoveSRCRPMExtraFiles(srpmDirectory string) bool {
	// 需要补充移动其他文件，测试发现存在其他文件
	var runCommand string
	fileList, err := utils.PathWalk(srpmDirectory)
	if err != nil {
		return false
	}
	for _, fileName := range fileList {
		if !strings.Contains(fileName, "/source/") && !strings.Contains(fileName, "/patches/") && !strings.Contains(fileName, ".spec") {
			if fileName == "/" || fileName == "" {
				return false
			}
			runCommand = fmt.Sprintf("mv %v patches/orig/", fileName)
			mvFilesCmd := exec.Command("bash", "-c", runCommand)
			mvFilesCmd.Dir = srpmDirectory
			mvFilesCmdBytes, _ := mvFilesCmd.Output()
			mvFilesCmdRaw := string(mvFilesCmdBytes)
			if len(mvFilesCmdRaw) > 1 {
				return false
			}
		}
	}
	return true
}

func xtractSourceTarBall(targetPath string) *xtractr.Response {
	logger := &Logger{
		xtractr: log.New(os.Stdout, "[XTRACTR] ", 0),
		debug:   log.New(os.Stdout, "[DEBUG] ", 0),
		info:    log.New(os.Stdout, "[INFO] ", 0),
	}
	q := xtractr.NewQueue(&xtractr.Config{Logger: logger, Parallel: 10, FileMode: 0644, DirMode: 0755})
	defer q.Stop()
	response := make(chan *xtractr.Response)
	_, err := q.Extract(&xtractr.Xtract{
		Name:       "ctdy-guard GerritDirectory Initialization SRCRPMs Decompression",
		Filter:     xtractr.Filter{Path: targetPath},
		CBChannel:  response,
		DeleteOrig: false,
		CBFunction: func(response *xtractr.Response) {
			for _, archive := range response.NewFiles {
				lastSubDir := strings.LastIndex(archive, "/")
				if archive == "/" {
					return
				}
				runCommand := fmt.Sprintf("mv %v %v", archive, path.Join(archive[:lastSubDir], "source"))
				mvFilesCmd := exec.Command("bash", "-c", runCommand)
				mvFilesCmd.CombinedOutput()
			}
		},
	})
	if err != nil {
		return &xtractr.Response{Error: errors.New("ExtractQueue CreateError")}
	}
	resp := <-response
	for _, archive := range resp.Archives {
		slog.Error.Printf("Extraction started: %s", strings.Join(archive, ", "))
	}
	resp = <-response
	if resp.Error != nil {
		log.Printf("Error: %s", resp.Error.Error())
	}
	slog.Error.Printf("Extracted Files:\n - %s", strings.Join(resp.NewFiles, "\n -"))

	return resp
}

func GerritDirectoryInitialization(srpmPath string) (bool, string) {
	var compressTypeTag string
	srpmLastIndex := strings.LastIndex(srpmPath, "/")
	srpmDirectory := srpmPath[:srpmLastIndex]
	// srpmFileName := srpmPath[srpmLastIndex+1:]
	srpmDirectory = strings.TrimSuffix(srpmDirectory, "/")
	srpmBasePath := path.Join(srpmDirectory, "source")
	srpmBasePath = strings.TrimSuffix(srpmBasePath, "/")

	err := utils.MakeDir(path.Join(srpmDirectory, "patches"))
	err = utils.MakeDir(path.Join(srpmDirectory, "patches/orig"))
	err = utils.MakeDir(path.Join(srpmDirectory, "patches/kylin"))
	if err != nil {
		return false, "创建目录失败"
	}

	slog.Error.Println("[CodeBase GerritDirectoryInitialization] SRPMPath: " + srpmPath)

	deleteSRPMCommand := fmt.Sprintf("rm -rf %v %v", path.Join(srpmDirectory, "*.src.rpm"), path.Join(srpmDirectory, "patches/orig", "*.src.rpm"))
	removeSrcRPMCmd := exec.Command("bash", "-c", deleteSRPMCommand)
	removeSrcRPMCmd.Dir = srpmDirectory
	_ = removeSrcRPMCmd.Run()

	resp := xtractSourceTarBall(srpmDirectory)
	if resp.Error != nil {
		return false, "解压SRPM失败"
	}

	runCommand := fmt.Sprintf("mv *.patch %v patches/orig/", compressTypeTag)
	mvFilesCmd := exec.Command("bash", "-c", runCommand)
	mvFilesCmd.Dir = srpmDirectory
	mvFileCmdResult, _ := mvFilesCmd.Output()
	mvFilesCmdRaw := string(mvFileCmdResult)
	if len(mvFilesCmdRaw) > 1 {
		return false, "转移至orig目录失败"
	}

	MoveSRCRPMExtraFiles(srpmDirectory)

	return true, srpmDirectory
}

func CloneRepoAndSubmit(repoName string, branchName string, srpmDirectory string, description string, xflowId int, commitMsgInfo CommitMessageInfo) (bool, string, string) {
	randSeed := rand.New(rand.NewSource(time.Now().UnixNano())).Float64()
	tmpDirectory := GERRIT_INIT_PARENT_DIR + strconv.Itoa(xflowId) + "/" + strconv.FormatFloat(randSeed, 'E', -1, 32)
	mkRepoDirectoryCmd := exec.Command("mkdir", "-p", tmpDirectory)
	mkRepoDirectoryBytes, _ := mkRepoDirectoryCmd.Output()
	mkRepoDirectoryRaw := string(mkRepoDirectoryBytes)
	if len(mkRepoDirectoryRaw) > 1 {
		return false, "创建目录失败，可能目录已存在", ""
	}

	var cloneRepoCmdStdOutBuilder strings.Builder

	repoNameUnescaped, err := url.PathUnescape(repoName)
	cloneRepoCmd := exec.Command("git", "clone", "-b", branchName, "ssh://"+INTERNAL_CONNECTION_USER+":29418/"+repoNameUnescaped)
	cloneRepoCmd.Dir = tmpDirectory
	cloneRepoCmd.Stdout = &cloneRepoCmdStdOutBuilder
	_ = cloneRepoCmd.Run()
	if len(cloneRepoCmdStdOutBuilder.String()) < 1 && strings.Contains(cloneRepoCmdStdOutBuilder.String(), "fatal:") {
		return false, "git仓库clone失败", ""
	}

	addSafeDirectoryCmdLine := fmt.Sprintf("git config --global --add safe.directory %v", path.Join(tmpDirectory, path.Base(repoNameUnescaped)))
	addSafeDirectoryCmd := exec.Command("bash", "-c", addSafeDirectoryCmdLine)
	addSafeDirectoryCmd.Dir = tmpDirectory
	_ = addSafeDirectoryCmd.Run()

	var cloneRepoGitHooksCmdStdout, cloneRepoGitHooksCmdStdErr strings.Builder
	cloneRepoGitHooksCmdLine := fmt.Sprintf("mkdir -p `git rev-parse --git-dir`/hooks/ && curl -Lo `git rev-parse --git-dir`/hooks/commit-msg https://%v:%v/tools/hooks/commit-msg && chmod +x `git rev-parse --git-dir`/hooks/commit-msg", utils.GerritDomain, utils.GerritPort)
	cloneRepoGitHooksCmd := exec.Command("bash", "-c", cloneRepoGitHooksCmdLine)
	cloneRepoGitHooksCmd.Dir = path.Join(tmpDirectory, path.Base(repoNameUnescaped))
	cloneRepoGitHooksCmd.Stdout = &cloneRepoGitHooksCmdStdout
	cloneRepoGitHooksCmd.Stderr = &cloneRepoGitHooksCmdStdErr
	_ = cloneRepoGitHooksCmd.Run()
	_, err = os.Stat(path.Join(tmpDirectory, path.Base(repoNameUnescaped), ".git", "hooks", "commit-msg"))
	if err != nil {
		return false, "GitHooks克隆失败", err.Error()
	}

	gitDeleteCmd := exec.Command("bash", "-c", "rm -rf *")
	gitDeleteCmd.Dir = path.Join(tmpDirectory, path.Base(repoNameUnescaped))
	_ = gitDeleteCmd.Run()

	destPath := path.Join(tmpDirectory, path.Base(repoNameUnescaped))
	runCommand := fmt.Sprintf("cp -fr %v/* %v/", srpmDirectory, destPath)
	transferFileCmd := exec.Command("bash", "-c", runCommand)
	transferFileCmd.Dir = path.Join(tmpDirectory, path.Base(repoNameUnescaped))
	transferFileCmdBytes, _ := transferFileCmd.Output()
	transferFileCmdRaw := string(transferFileCmdBytes)
	if len(transferFileCmdRaw) > 1 {
		return false, "转移文件至目标目录失败", ""
	}

	gitAddCmd := exec.Command("git", "add", "--all")
	gitAddCmd.Dir = path.Join(tmpDirectory, path.Base(repoNameUnescaped))
	_ = gitAddCmd.Run()

	var commitMsg string
	commitMsgDetailString := fmt.Sprintf("\n 门禁系统任务ID：%v\n 发行商：%v\n 发行版本：%v\n ", commitMsgInfo.XflowID, commitMsgInfo.ReleaseDist, commitMsgInfo.LectoTypeCenter)
	srcRPMFileStartIndex := strings.LastIndex(commitMsgInfo.SrcRPMCanonicalName, "/")
	commitMsg = fmt.Sprintf("[初始化] 门禁系统源码仓自动初始化 - 初始化分支: %v 源码包名：%v\n\n %v", commitMsgInfo.BranchName, commitMsgInfo.SrcRPMCanonicalName[srcRPMFileStartIndex+1:], commitMsgDetailString)
	// randomTaskId := fmt.Sprintf("%06v", rand.New(rand.NewSource(time.Now().UnixNano())).Int31n(1000000))
	gitCommitCmd := exec.Command("git", "commit", "-m", commitMsg)
	gitCommitCmd.Dir = path.Join(tmpDirectory, path.Base(repoNameUnescaped))
	gitCommitCmdBytes, _ := gitCommitCmd.Output()
	gitCommitCmdRaw := string(gitCommitCmdBytes)
	if len(gitCommitCmdRaw) < 1 {
		return false, "commit信息提交失败", ""
	}

	var gitPushCmdStdOut, gitPushCmdStdErr strings.Builder
	pushRefs := fmt.Sprintf("%v:refs/for/%v", branchName, branchName)
	gitPushCmd := exec.Command("git", "push", "origin", pushRefs)
	gitPushCmd.Dir = path.Join(tmpDirectory, path.Base(repoNameUnescaped))
	gitPushCmd.Stdout = &gitPushCmdStdOut
	gitPushCmd.Stderr = &gitPushCmdStdErr
	_ = gitPushCmd.Run()
	slog.Error.Println("========= gitPushCmdStdOutString:", gitPushCmdStdOut.String())
	slog.Error.Println("========= gitPushCmdStdErrString:", gitPushCmdStdErr.String())
	if len(gitPushCmdStdErr.String()) < 1 {
		return false, "push推送失败", ""
	}
	gitPushCmdStdOutString := strings.TrimSpace(gitPushCmdStdErr.String())
	if strings.Contains(gitPushCmdStdOutString, "SUCCESS") && strings.Contains(gitPushCmdStdOutString, branchName) && strings.Contains(gitPushCmdStdOutString, "[NEW]") {
		getPushChangeIdReg := regexp.MustCompile(`/\+/(\d+)`)
		getPushChangeIdMatch := getPushChangeIdReg.FindStringSubmatch(gitPushCmdStdOutString)
		PushChangeId := getPushChangeIdMatch[1]
		return true, PushChangeId, commitMsg
	} else {
		return false, "可能因网络原因或其他原因推送至Gerrit失败，请稍后再试", ""
	}
}

// 目前无法通过pushChangeId获取Revision值，还需进一步调试API
func GerritReview(pushChangeId string) bool {
	getPatchSetRevisionCmdLine := fmt.Sprintf("ssh -p 29418 -i %v %v gerrit query %v --current-patch-set", INTERNAL_SSH_KEY_FILE, INTERNAL_CONNECTION_USER, pushChangeId)
	getPatchSetRevisionCmd := exec.Command("bash", "-c", getPatchSetRevisionCmdLine)
	getPatchSetRevisionBytes, err := getPatchSetRevisionCmd.Output()
	getPatchSetRevisionRaw := string(getPatchSetRevisionBytes)
	slog.Error.Println("========getPatchSetRevisionRaw:", getPatchSetRevisionRaw)
	if err != nil || len(getPatchSetRevisionRaw) < 1 {
		slog.Error.Println("queryPushChangeId Match PatchSet Revision Failed, output:", err)
		return false
	}
	revisionReturnData := strings.Split(getPatchSetRevisionRaw, "revision:")
	patchSetRevisionData := strings.Split(revisionReturnData[1], "\n")
	patchSetRevision := patchSetRevisionData[0]

	codeReviewCmd := exec.Command("ssh", "-p", "29418", "-i", INTERNAL_SSH_KEY_FILE, INTERNAL_CONNECTION_USER, "gerrit review --code-review +2 "+patchSetRevision+" --message \"门禁系统源码仓初始化代码自动合入[CodeReview+2]\"")
	codeReviewBytes, err := codeReviewCmd.Output()
	codeReviewRaw := string(codeReviewBytes)
	slog.Error.Println(codeReviewCmd.String(), codeReviewRaw)
	if len(codeReviewRaw) > 1 {
		slog.Error.Println("CodeReview +2 Failed, Error: ", err)
		return false
	}

	verifiedCmd := exec.Command("ssh", "-p", "29418", "-i", INTERNAL_SSH_KEY_FILE, INTERNAL_CONNECTION_USER, "gerrit review --verified +1 "+patchSetRevision+" --message \"门禁系统源码仓初始化代码自动合入[Verified+1]\"")
	verifiedCmdBytes, err := verifiedCmd.Output()
	verifiedCmdRaw := string(verifiedCmdBytes)
	slog.Error.Println(verifiedCmd.String(), verifiedCmdRaw)
	if len(verifiedCmdRaw) > 1 {
		slog.Error.Println("Verified +1 Failed, Error: ", err)
		return false
	}

	submitCmd := exec.Command("ssh", "-p", "29418", "-i", INTERNAL_SSH_KEY_FILE, INTERNAL_CONNECTION_USER, "gerrit review --submit "+patchSetRevision)
	submitCmdBytes, err := submitCmd.Output()
	submitCmdRaw := string(submitCmdBytes)
	if len(submitCmdRaw) > 1 {
		slog.Error.Println("Submit Failed, Error: ", err)
		return false
	} else {
		return true
	}
}

// 获取所有分支列表，传入repoName，获取该仓库下所有的分支，返回类型为字符串数组
func GetRepoBranchList(repoName string) (bool, []string) {
	repoName = strings.ReplaceAll(repoName, "/", "%2F")
	gerritAPIURL := fmt.Sprintf("%s", INTERNAL_HTTPS_CONNECTION_GERRIT_URL+GERRIT_API_PROJECT_URL+repoName+"/branches/")
	retData := sendHTTPRequest(gerritAPIURL, "GET", utils.GerritAk, G_GerritToken, nil)
	retData = strings.Split(retData, ")]}'")[1]
	var branchListResult []BranchInput
	var repoBranchList []string
	err := json.Unmarshal([]byte(retData), &branchListResult)
	if err == nil {
		for _, branchRef := range branchListResult {
			if strings.Contains(branchRef.Ref, "refs/heads/") {
				repoBranchList = append(repoBranchList, strings.Split(branchRef.Ref, "refs/heads/")[1])
			}
		}
	}
	return true, repoBranchList
}

// 拼接新增仓库描述，需要传入分支名称和产品ID
// GenerateRepoDescription 函数根据提供的分支名称和产品ID生成仓库描述
//
// 参数:
//     branchName string - 分支名称
//     productId int - 产品ID
//
// 返回值:
//     bool - 操作是否成功
//     string - 生成的仓库描述
/*func GenerateRepoDescription(branchName string, productId int) (bool, string) {
	product := model.GetProductListById(productId)
	var kojiStringList []string
	var kojiString string
	var outputString string
	for _, v := range product.ProductKojis {
		kojiString = ""
		kojiInfo := model.GetKojiPlatformById(v)
		// Case 1: Ip Not Equal to ************
		if !strings.Contains(kojiInfo.Addr, "************") {
			return false, "Product Koji Ip Not Equal to ************"
		}

		// Case 2: Ip == ************:80 / 8242
		if strings.Contains(kojiInfo.Addr, "************") && !strings.Contains(kojiInfo.Addr, "************:8") {
			kojiPortIp := "242"
			kojiString = kojiString + kojiPortIp + " "
		}

		// Case 3: Ip: Exists
		if strings.Contains(kojiInfo.Addr, "************:8") {
			ipSplitString := strings.Split(kojiInfo.Addr, "************:8")[1]
			if ipSplitString != "" {
				kojiString = kojiString + ipSplitString
			}
		}
		validRegExpression := regexp.MustCompile(`(\b25[0-5]|\b2[0-4][0-9]|\b[01]?[0-9][0-9]?)(\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)){3}`)
		if !validRegExpression.MatchString(kojiInfo.Addr) {
			// Case 4: Other Case, Raise Exception
			return false, "Product Koji Ip Not Understand, Please Contact Administrator Catch Exception"
		}
		if strings.Contains(kojiString, "080") {
			kojiString = strings.ReplaceAll(kojiString, "080", "80")
		}
		if regexp.MustCompile(`\d+ \d.+`).MatchString(kojiString) {
			extractExpression := regexp.MustCompile(` \d+$`)
			kojiString = extractExpression.FindString(kojiString)
		}
		kojiStringList = append(kojiStringList, kojiString)
	}
	for _, kojiString := range kojiStringList {
		descriptionString := "项目:"
		if strings.Contains(branchName, "os-release-") {
			splitBranch := strings.Split(branchName, "os-release-")[1]
			if splitBranch != "" {
				splitBranch = strings.Replace(splitBranch, "-", "(", 1)
				splitBranch += ")"
				splitBranch = strings.ToUpper(splitBranch)
				descriptionString = descriptionString + splitBranch + "--koji:"
			}
			descriptionString = descriptionString + kojiString + ";"
			re := regexp.MustCompile(`项目:.*-koji:.*;`)
			if re.MatchString(descriptionString) {
				outputString = outputString + descriptionString
			} else {
				return false, "regExp Doesn't Match"
			}
		} else {
			return false, "Invalid BranchName"
		}
	}
	return true, outputString
}
*/
