package utils

import (
	"net/http"

	"github.com/gin-gonic/gin"
)

type Response struct {
	Code  int         `json:"code"`
	Msg   string      `json:"msg"`
	Data  interface{} `json:"data"`
	Trace string      `json:"traceid"`
}

func NewResponseSuccess() *Response {
	return &Response{
		Code: 200,
		Msg:  "success",
		Data: make(map[string]interface{}),
	}
}

func NewResponseFailed() *Response {
	return &Response{
		Code: 422,
		Msg:  "failed",
		Data: make(map[string]interface{}),
	}
}

var traceIDKey = "traceid"

func ResponseSuccess(c *gin.Context, response *Response) {
	response.Trace = c.GetString(traceIDKey)
	c.JSO<PERSON>(http.StatusOK, response)
}

func ResponseFaied(c *gin.Context, response *Response) {
	response.Trace = c.GetString(traceIDKey)
	c.JSON(http.StatusOK, response)
}

// 未认证
func ResponseUnAuth(c *gin.Context, response *Response) {
	response.Trace = c.GetString(traceIDKey)
	c.<PERSON><PERSON>(http.StatusUnauthorized, response)
}
