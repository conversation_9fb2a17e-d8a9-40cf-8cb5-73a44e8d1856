/*
 * @Author: lwq <EMAIL>
 * @Date: 2025-07-08
 * @LastEditors: lwq <EMAIL>
 * @LastEditTime: 2025-07-16
 * @FilePath: /ctdy-guard/slam/task/integration/option/recheck.go
 * @Description: 集成操作
 * @Copyright (c) 2025 by 2022.kylinos.cn, All Rights Reserved.
 */
package option

import "slam/model"

// ReCheckOption 复核操作,直接继承check的方法
type ReCheckOption struct {
	CheckOption
}

func NewReCheckOption() *ReCheckOption {
	return &ReCheckOption{
		CheckOption: CheckOption{
			BaseOption: BaseOption{
				ID:   "recheck",
				Name: "复核",
			},
			igCheckModel: model.NewIntegrationCheckModel(),
			igCountModel: model.NewIntegrationCountModel(),
			bugModel:     model.NewIntegrationBugModel(),
			cveModel:     model.NewIntegrationCveModel(),
		},
	}
}
