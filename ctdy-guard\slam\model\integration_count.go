/*
 * @Author: lwq lvwenqi<PERSON>@kylinos.cn
 * @Date: 2025-04-30
 * @LastEditors: lwq <EMAIL>
 * @LastEditTime: 2025-08-01
 * @FilePath: /ctdy-guard/slam/model/integration_count.go
 * @Description: 集成单模型
 * @Copyright (c) 2025 by 2022.kylinos.cn, All Rights Reserved.
 */
package model

import (
	"time"

	"gorm.io/gorm"
)

// Integration 集成单各个状态的统计数据
type IntegrationCount struct {
	Id             int       `gorm:"column:id;primaryKey;autoIncrement:true;type:serial4;not null" json:"id,string,omitempty" label:"序号，主键"`
	IntegrationId  int       `gorm:"column:integration_id;not null;default:0" json:"integration_id" label:"集成单id"`
	IgStatus       int       `gorm:"column:ig_status;type:int2;not null;default:1" json:"ig_status" label:"集成单状态"`
	TotalCount     int       `gorm:"column:total_count;not null;default:0" json:"total_count" label:"总包数"`
	TotalBugCount  int       `gorm:"column:total_bug_count;not null;default:0" json:"total_bug_count" label:"bug总记录数"`
	TotalCveCount  int       `gorm:"column:total_cve_count;not null;default:0" json:"total_cve_count" label:"cve总记录数"`
	ReviewBugCount int       `gorm:"column:review_bug_count;not null;default:0" json:"review_bug_count" label:"当前状态下完成审核的记录数"`
	ReviewCveCount int       `gorm:"column:review_cve_count;not null;default:0" json:"review_cve_count" label:"当前状态下完成审核的记录数"`
	UnpublishCount int       `gorm:"column:unpublish_count;not null;default:0" json:"unpublish_count" label:"不可发布的包数量"`
	CreatedAt      time.Time `gorm:"column:create_time;not null" json:"create_time,omitempty" label:"创建时间"`
	UpdatedAt      time.Time `gorm:"column:update_time;not null" json:"update_time,omitempty" label:"更新时间"`
}

// TableName 表名
func (*IntegrationCount) TableName() string {
	return "slam_integration_count"
}

type IntegrationCountRepo interface {
	/**
	 * @author: lwq <EMAIL>
	 * @description: GetIntegrationCount 根据igId查询
	 * @param igId int
	 * @param status int
	 * @return {*}
	 */
	GetIntegrationCount(igId int, status int) (*IntegrationCount, error)
	/**
	 * @author: lwq <EMAIL>
	 * @description: UpdateCountInfo 更新计数信息
	 * @param igId int
	 * @param status int
	 * @param umap map[string]any
	 * @return {*}
	 */
	UpdateCountInfo(igId, status int, umap map[string]any) error
	/**
	 * @author: lwq <EMAIL>
	 * @description: CreateIntegrationCount 创建计数信息
	 * @param igCount *IntegrationCount
	 * @return {*}
	 */
	CreateIntegrationCount(igCount *IntegrationCount) error
}
type IntegrationCountModel struct {
	db *gorm.DB
}

func NewIntegrationCountModel() *IntegrationCountModel {
	return &IntegrationCountModel{
		db: GetDB(),
	}

}
func (m *IntegrationCountModel) GetIntegrationCount(igId, status int) (*IntegrationCount, error) {
	// 实现获取集成单统计数据的逻辑
	var count IntegrationCount
	err := m.db.
		Where("integration_id = ?", igId).
		Where("ig_status >= ?", status).
		Order("id desc").First(&count).Error
	return &count, err
}

func (m *IntegrationCountModel) CreateIntegrationCount(igCount *IntegrationCount) error {
	// 实现创建集成单统计数据的逻辑
	return m.db.Create(igCount).Error
}

func (m *IntegrationCountModel) UpdateCountInfo(igId, status int, umap map[string]any) error {
	d := m.db.Model(&IntegrationCount{}).
		Where("integration_id = ?", igId).
		Where("ig_status = ?", status)

	return d.UpdateColumns(umap).Error
}
