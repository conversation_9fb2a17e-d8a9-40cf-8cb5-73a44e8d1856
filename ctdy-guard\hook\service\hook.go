package service

import (
	"bufio"
	"bytes"
	"context"
	"crypto/tls"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/hashicorp/go-retryablehttp"
	"io"
	"net/http"
	"net/url"
	"os"
	"os/exec"
	"path"
	"path/filepath"
	"reflect"
	"regexp"
	"slam-hook/config"
	"slam-hook/global"
	"slam-hook/logs"
	"slam-hook/model"
	"slam-hook/tools"
	"strconv"
	"strings"
	"time"
)

type HookParam struct {
	Id            int `json:"数据id"`
	TaskId        string
	XflowId       int `json:"xflow_id"`
	XflowType     string
	FilePath      string `json:"文件路径"`
	FileUnZipPath string `json:"srpm包解压路径"`
	KojiAddr      string
	GerritAddr    string
	Tag           string
	TagAction     string
	TagKoji       string `json:"tag新增时关联的koji"`
	SrpmName      string
	ProductId     int    `json:"product_id"`
	HashMethod    string `label:"哈希值校验方法：1 MD5；2 SHA256；3 SHA1；4 MD5、SHA1、SHA256（DEB）"`
	HashValue     string `label:"哈希值校验值"`
}

type HookRes struct {
	KojiIp       string     `json:"koji_ip"`
	SrcName      string     `json:"src_name"`
	CreatedTime  time.Time  `json:"created_time" label:"数据创建时间"`
	ResultStatus int        `json:"result_status"`
	HookResult   []HookItem `json:"hook_result"`
}

type HookItem struct {
	HookId  string `json:"hook_id" label:"Hook编号"`
	Level   string `json:"level" label:"Hook等级"`
	Msg     string `json:"msg" label:"描述"`
	Result  string ` json:"result" label:"结果"`
	Details string ` json:"details" label:"结果详情"`
}

var (
	LicenseToken string
	Client       *retryablehttp.Client
	KojiUsage    map[string]float64
	System       *TaskSystem
)

func InitHook() {
	KojiUsage = make(map[string]float64)
	tr := &http.Transport{
		TLSClientConfig: &tls.Config{InsecureSkipVerify: true},
	}

	Client = retryablehttp.NewClient()
	Client.HTTPClient = &http.Client{
		Transport: tr,
		Timeout:   30 * time.Second,
	}
	Client.RetryMax = 3
	Client.RetryWaitMin = 1 * time.Second
	Client.RetryWaitMax = 10 * time.Second
	startQAXSafed()
	LicenseToken = GetLicenseToken()
	RestartUnfinishedHookTask()
	System = NewTaskSystem(100, 800)
	System.Start()
}

// CornTask 创建一个空结构体。用于动态获取定时任务
type CornTask struct{}

// HookUnzipTask rpm包解压
func (ct *CornTask) HookUnzipTask() {
	hookTaskList, err := model.GetStatusIsOpen()
	if err != nil {
		logs.Log.Errorf("GetStatusIsOpen err, reason %s", err.Error())
		return
	}
	if len(hookTaskList) == 0 {
		logs.Log.Info("No hook task open.")
		return
	}

	for _, task := range hookTaskList {
		priority, _ := DeterminePriority(task.XflowId)
		t := Task{
			ID:       task.XflowId,
			Priority: priority,
			Data:     task,
		}
		System.AddTask(&t)
	}
}

func (ct *CornTask) GetWorkerStats() {
	stats := System.Stats()
	logs.Log.Infof("系统统计 - 总任务:%d, 完成步骤一:%d, 完成步骤二:%d, 失败步骤一:%d, 失败步骤二:%d, 限流:%d, 步骤一队列:%d, 步骤二队列:%d",
		stats["total_tasks"], stats["processed_step1"], stats["processed_step2"],
		stats["failed_step1"], stats["failed_step2"], stats["rate_limited"],
		stats["step1_queue"], stats["step2_queue"])
	for k, v := range KojiUsage {
		logs.Log.Infof("%s koji平台当前负载为：%v", k, v)
	}
}

// 队列任务处理
func processHookTask(queueTask *Task) error {
	task := queueTask.Data
	// 处理特殊任务类型
	if isSpecialTask(task) {
		return model.UpdateStatusByXflowIdAndPackageName(task.XflowId, global.HOOK_TASK_STATUS_READY, task.SrpmName)
	}

	// 获取并处理SRPM路径
	if err := processSrpmPath(task); err != nil {
		return err
	}

	return model.UpdateStatusByXflowIdAndPackageName(task.XflowId, global.HOOK_TASK_STATUS_READY, task.SrpmName)
}

// isSpecialTask 检查是否为特殊任务类型
func isSpecialTask(task model.HookTaskList) bool {
	return task.XflowType == "tagbuild" ||
		task.XflowType == "untag" ||
		task.XflowType == "stock_import" ||
		task.XflowType == "binary"
}

// processSrpmPath 处理SRPM路径
func processSrpmPath(task model.HookTaskList) error {
	srpmPath := getSrpmPath(task)
	tmpPath := filepath.Join(global.PACKAGE_TMP_PATH, strconv.Itoa(task.XflowId))
	isCommerce := task.IsCommerce
	// 桌面源码包不创建临时文件，需要build
	if !(strings.Contains(task.SrpmName, ".deb") && isCommerce == "") {
		if err := CreateTmpSrpm(srpmPath, tmpPath); err != nil {
			logs.Log.Errorf("CreateTmpSrpm err, reason: %s", err.Error())
			return err
		}
	}

	return unzipPackage(tmpPath, task)
}

// getSrpmPath 获取SRPM文件路径
func getSrpmPath(task model.HookTaskList) string {
	if task.XflowType == "lectotype" {
		return filepath.Join(global.LECTOTYPE_APPENDIX_DIR, strconv.Itoa(task.XflowId), task.SrpmName)
	}
	return filepath.Join(global.TMP_SRPM_PATH, strconv.Itoa(task.XflowId), task.SrpmName)
}

// unzipPackage 解压包文件
func unzipPackage(tmpPath string, task model.HookTaskList) error {
	if task.KojiAddr != "" && task.Tag != "" {
		KojiUnzip(tmpPath, task)
	} else {
		UnzipTask(tmpPath, task.SrpmName, task.IsCommerce, task.XflowId)
	}
	return nil
}

func UnzipTask(tmpPath, srpmName, isCommerce string, xflowId int) {
	if strings.Contains(srpmName, ".deb") {
		if isCommerce != "" {
			model.UpdateStatusByXflowIdAndPackageName(xflowId, global.HOOK_TASK_STATUS_READY, srpmName)
			return
		}
		if strings.Contains(srpmName, ":") {
			re := regexp.MustCompile(`(\d+):`)
			srpmName = re.ReplaceAllString(srpmName, "")
		}
		unzipFilePath := filepath.Join(tmpPath, srpmName[0:len(srpmName)-4])
		rpmName, _, _ := tools.DebNameParse(srpmName)
		res := UnzipDeb(unzipFilePath, rpmName, srpmName[0:len(srpmName)-4], xflowId)
		if !res {
			logs.Log.Errorf("UnzipDeb %s err", srpmName)
			return
		}
	} else {
		if strings.Contains(srpmName, ".rpm") && !strings.Contains(srpmName, ".src.rpm") {
			model.UpdateStatusByXflowIdAndPackageName(xflowId, global.HOOK_TASK_STATUS_READY, srpmName)
			return
		}
		unzipSrpmPath := filepath.Join(tmpPath, srpmName)
		unzipFilePath := filepath.Join(tmpPath, srpmName[0:len(srpmName)-8])
		rpmInfo, _ := tools.GetPackInfo(unzipSrpmPath)
		rpmName := rpmInfo.PackName
		res := UnzipSrc(unzipFilePath, unzipSrpmPath, rpmName)
		if !res {
			logs.Log.Errorf("UnzipSrc %s err", srpmName)
			return
		}
	}
	model.UpdateStatusByXflowIdAndPackageName(xflowId, global.HOOK_TASK_STATUS_READY, srpmName)
}

// CreateTmpSrpm 创建临时文件
func CreateTmpSrpm(sourceFilePath, tmpFilePath string) error {
	fileName := filepath.Base(sourceFilePath)
	err := tools.CreateDir(tmpFilePath)
	if err != nil {
		return errors.New(fmt.Sprintf(global.DirCreateError, tmpFilePath))
	}
	copyResult := tools.CopyFile(sourceFilePath, tmpFilePath, fileName)
	if !copyResult {
		logs.Log.Error(global.FileCopyError)
		return errors.New(global.FileCopyError)
	}
	return nil
}

// UnzipSrc 解压srpm包
func UnzipSrc(UnzipPath, srpmPath, rpmName string) bool {
	err := os.MkdirAll(UnzipPath, 0755)
	if err != nil {
		return false
	}
	CmdStr := fmt.Sprintf("rpm2cpio %s | cpio -iv", srpmPath)
	cmd := exec.Command("bash", "-c", CmdStr)
	cmd.Dir = UnzipPath
	output, err := cmd.Output()
	if err != nil {
		logs.Log.Error("rpm2cpio cmd failed, output:", string(output), err)
		return false
	} else {
		logs.Log.Info(string(output))
	}
	SpecPath := path.Join(UnzipPath, "*.spec")
	rpmbuildCmdStr := fmt.Sprintf("yes '' | rpmbuild --define \"_sourcedir %s\" --define \"_topdir %s\" -bp %s --nodeps", UnzipPath, UnzipPath, SpecPath)
	rpmbuildCmd := exec.Command("bash", "-c", rpmbuildCmdStr)
	output, _ = rpmbuildCmd.Output()
	logs.Log.Infoln("rpmbuild bp " + rpmName + string(output))
	return true
}

func UnzipDeb(UnzipPath, rpmName, rpmVersion string, xflowId int) bool {
	dscPath := path.Join(global.LECTOTYPE_APPENDIX_DIR, strconv.Itoa(xflowId), rpmName+"*.dsc")
	debUnzipPath := path.Join(UnzipPath, "BUILD", rpmVersion)
	_ = os.MkdirAll(filepath.Dir(debUnzipPath), 0755)

	debBuildCmdStr := fmt.Sprintf("dpkg-source --no-copy -x %s %s", dscPath, debUnzipPath)
	debBuildCmd := exec.Command("bash", "-c", debBuildCmdStr)
	debBuildCmd.Dir = filepath.Dir(dscPath)
	output, _ := debBuildCmd.Output()
	logs.Log.Infoln("dpkg-source " + rpmName + string(output))
	return true
}

// HookDeliverTask 获取状态为ready的任务并下发任务
func (ct *CornTask) HookDeliverTask() {
	hookTaskList, err := model.GetStatusIsReady()
	if err != nil {
		logs.Log.Error("GetStatusIsReady error, reason: ", err.Error())
		return
	}
	if len(hookTaskList) == 0 {
		logs.Log.Info("No hook task ready.")
		return
	}

	for _, hookTask := range hookTaskList {
		task := Task{
			ID:        *hookTask.Id,
			Step2Data: hookTask,
		}
		System.step2Queue.Enqueue(&task) // 这里是实际插入队列的操作
		logs.Log.Debugf("步骤二任务 %d 已加入队列", task.ID)
	}
	if len(hookTaskList) > 0 {
		System.step2Cond.Broadcast()
	}
}

// deliverSingleTask 处理单个任务的下发
func deliverSingleTask(queue2Task *Task) error {
	hookTask := queue2Task.Step2Data
	fmt.Println(hookTask)
	// 准备任务参数
	hookParam := prepareHookParam(hookTask)

	// 执行任务
	value := reflect.ValueOf(hookParam)
	method := value.MethodByName(hookTask.HookId)
	if !method.IsValid() {
		return fmt.Errorf("not found %s hook method", hookTask.HookId)
	}

	method.Call([]reflect.Value{})
	err := model.UpdateHookStatusRunning(*hookTask.Id)
	tools.CheckUpdateHookResultErr("UpdateHookStatusRunning", err)
	return err
}

// prepareHookParam 准备Hook参数
func prepareHookParam(hookTask model.HookTaskList) *HookParam {
	filePath := filepath.Join(global.PACKAGE_TMP_PATH, strconv.Itoa(hookTask.XflowId), hookTask.SrpmName)
	fileUnzipPath := getUnzipPath(hookTask)
	var xflowType string
	if hookTask.IsCommerce != "" {
		xflowType = "commerce"
	} else {
		xflowType = hookTask.XflowType
	}
	return &HookParam{
		Id:            *hookTask.Id,
		TaskId:        hookTask.TaskId,
		XflowId:       hookTask.XflowId,
		XflowType:     xflowType,
		FilePath:      filePath,
		FileUnZipPath: fileUnzipPath,
		KojiAddr:      hookTask.KojiAddr,
		GerritAddr:    hookTask.GerritInfo,
		Tag:           hookTask.Tag,
		TagAction:     hookTask.TagAction,
		TagKoji:       hookTask.TagKoji,
		SrpmName:      hookTask.SrpmName,
		ProductId:     hookTask.ProductId,
		HashMethod:    hookTask.HashMethod,
		HashValue:     hookTask.HashValue,
	}
}

// getUnzipPath 获取解压路径
func getUnzipPath(hookTask model.HookTaskList) string {
	if hookTask.XflowType == "tagbuild" || hookTask.XflowType == "untag" {
		return ""
	}

	if strings.Contains(hookTask.SrpmName, ".deb") {
		if strings.Contains(hookTask.SrpmName, ":") {
			re := regexp.MustCompile(`(\d+):`)
			hookTask.SrpmName = re.ReplaceAllString(hookTask.SrpmName, "")
		}
		if hookTask.IsCommerce != "" {
			return filepath.Join(global.PACKAGE_TMP_PATH, strconv.Itoa(hookTask.XflowId), hookTask.SrpmName)
		} else {
			return filepath.Join(
				global.PACKAGE_TMP_PATH,
				strconv.Itoa(hookTask.XflowId),
				hookTask.SrpmName[0:len(hookTask.SrpmName)-4],
				"BUILD",
				hookTask.SrpmName[0:len(hookTask.SrpmName)-4],
			)
		}
	}

	fileBuildPath := filepath.Join(
		global.PACKAGE_TMP_PATH,
		strconv.Itoa(hookTask.XflowId),
		hookTask.SrpmName[0:len(hookTask.SrpmName)-8],
		"BUILD",
	)

	entries, _ := os.ReadDir(fileBuildPath)
	for _, entry := range entries {
		if entry.IsDir() {
			return path.Join(fileBuildPath, entry.Name())
		}
	}
	return ""
}

func (ct *CornTask) HookTimeOutTask() {
	hookTaskXflowId, err := model.UpdateTimeOutHookTask()
	if err != nil {
		logs.Log.Error("GetTimeOutHookTask error, reason: ", err.Error())
		return
	}
	for _, xflowId := range hookTaskXflowId {
		tmpPath := filepath.Join(global.PACKAGE_TMP_PATH, strconv.Itoa(xflowId))
		go delTmpPath(tmpPath)
	}

}

func delTmpPath(packageTmpDir string) error {
	taskId := tools.CreateRandStr(10)
	_, err := os.Stat(packageTmpDir)
	if !os.IsNotExist(err) {
		err := os.Rename(packageTmpDir, packageTmpDir+taskId)
		if err != nil {
			logs.Log.Error("Error renaming folder:", err)
			return err
		}

		logs.Log.Infof("Folder renamed successfully")
		defer func() {
			if r := recover(); r != nil {
				logs.Log.Errorln("preHook panic，reason：", r)
			}
		}()
		// 删除操作

		err = os.RemoveAll(packageTmpDir + taskId)
		if err != nil {
			logs.Log.Infof("Error deleting folder:", err)
		} else {
			logs.Log.Infoln("Folder deleted successfully", packageTmpDir+taskId)
		}
	}
	return nil
}

func startQAXSafed() {
	cmd := exec.Command(global.QAX_SERVER_PATH)
	// 获取命令的标准输出
	_, err := cmd.Output()
	if err != nil {
		logs.Log.Errorf("start qaxSafed err, reason: %s", err)
	}
}

// RestartUnfinishedHookTask 重启时检测上次退出时未完成的任务并重新触发
func RestartUnfinishedHookTask() {
	runningTaskId := model.SelectRunningTask()
	err := model.UpdateStatusByIds(runningTaskId, "ready")
	if err != nil {
		logs.Log.Errorf("UpdateStatusByIds err, reason: %s", err.Error())
	}

	unzipTaskId := model.SelectUnzipTask()
	err = model.UpdateStatusByIds(unzipTaskId, "open")
	if err != nil {
		logs.Log.Errorf("UpdateStatusByIds err, reason: %s", err.Error())
	}
}

type LicenseLoginResponse struct {
	Code int                      `json:"code"`
	Data LicenseLoginResponseData `json:"data"`
	Msg  string                   `json:"msg"`
}

type LicenseLoginResponseData struct {
	AccessToken  string `json:"access_token"`
	RefreshToken string `json:"refresh_token"`
}

func (ct *CornTask) RefreshLicenseToken() {
	LicenseToken = GetLicenseToken()
	fmt.Println(LicenseToken)
}

func GetLicenseToken() string {
	licenseUrl, _ := url.JoinPath(config.LicenseAddress, "/user/login")
	data := make(map[string]string)
	data["username_or_phone"] = config.LicenseUserName
	data["password"] = config.LicensePassword
	loginBody, err := json.Marshal(data)
	if err != nil {
		logs.Log.Error("License login body json marshal err:", err)
		return LicenseToken
	}
	resp, err := Client.Post(licenseUrl, "application/json", bytes.NewBuffer(loginBody))
	if err != nil {
		logs.Log.Error("License login request err:", err)
		return LicenseToken
	}
	defer func(Body io.ReadCloser) {
		_ = Body.Close()
	}(resp.Body)

	if resp.StatusCode == http.StatusOK {
		var response LicenseLoginResponse
		if err = json.NewDecoder(resp.Body).Decode(&response); err != nil {
			return LicenseToken
		}
		if response.Code != 0 {
			logs.Log.Error("Get License Token failed, reason: ", response.Msg)
			return LicenseToken
		} else {
			LicenseToken = response.Data.AccessToken
		}
	}
	return LicenseToken
}

func KojiUnzip(tmpPath string, task model.HookTaskList) {
	// 设置30min的超时时间
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Minute)
	defer cancel()
	// 解析 URL
	parsedURL, err := url.Parse(task.KojiAddr)
	if err != nil {
		logs.Log.Error("parse url error ")
		return
	}
	// 提取 Scheme + Host（Base URL）
	baseUrl := fmt.Sprintf("%s://%s", parsedURL.Scheme, parsedURL.Host)
	baseURL := tools.ReplaceKojiAddr(baseUrl)
	tmpPackPath := path.Join(tmpPath, task.SrpmName)
	taskId, err := CreateTask(ctx, baseURL, task.Tag, tmpPackPath)
	fmt.Println("task id:", taskId)
	if err != nil {
		logs.Log.Error(err.Error())
		return
	}
	err = WaitForTaskCompletion(ctx, baseURL, taskId, 60*time.Second)
	if err != nil {
		logs.Log.Error(err.Error())
		return
	}
	outPath := path.Join(tmpPath, task.SrpmName[0:len(task.SrpmName)-8])
	tools.CreateDir(outPath)
	tarFileList, err := DownloadResult(ctx, baseURL, taskId, outPath)

	if err != nil {
		logs.Log.Error(err.Error())
		return
	}
	tarFilePath := path.Join(outPath, tarFileList[0])
	tarOutputPath := path.Join(outPath, "BUILD", task.SrpmName[0:len(task.SrpmName)-8])
	tools.CreateDir(tarOutputPath)
	tarCmd := fmt.Sprintf("tar -axvf %s -C %s", tarFilePath, tarOutputPath)
	tarCmdConf := tools.CmdConfig{
		Name:      "bash",
		Args:      []string{"-c", tarCmd},
		IsShowCmd: true,
	}
	_, err = tools.ExecuteWithTimeout(&tarCmdConf, 10*time.Minute)
	if err != nil {
		logs.Log.Error(err.Error())
		return
	}
	model.UpdateStatusByXflowIdAndPackageName(task.XflowId, global.HOOK_TASK_STATUS_READY, task.SrpmName)
	return
}

// CreateTask 执行创建任务
func CreateTask(ctx context.Context, kojiAdd, tag, srpmPath string) (string, error) {
	select {
	case <-ctx.Done(): // 监听取消
		return "", ctx.Err()
	default:
		kojiCmd := fmt.Sprintf("koji --user=%s --password=%s  --server=%s/kojihub rpmbuildbp --task-id --quiet --nowait  %s %s", config.KojiBPAk, config.KojiBPSk, kojiAdd, tag, srpmPath)
		createCmdConf := tools.CmdConfig{
			Name:      "bash",
			Args:      []string{"-c", kojiCmd},
			IsShowCmd: true,
		}
		output, err := tools.ExecuteWithTimeout(&createCmdConf, 60*time.Second)
		if err != nil {
			return "", fmt.Errorf("failed to create task: %w", err)
		}
		scanner := bufio.NewScanner(strings.NewReader(output.String()))
		var id string
		for scanner.Scan() {
			line := strings.TrimSpace(scanner.Text())
			if line != "" { // 跳过空行
				id = line
			}
		}
		return id, nil
	}
}

// QueryTaskStatus 执行查询任务状态
func QueryTaskStatus(ctx context.Context, baseURL, taskID string) (string, error) {
	select {
	case <-ctx.Done(): // 监听取消
		return "", ctx.Err()
	default:
		queryCmd := fmt.Sprintf("koji --user=%s --password=%s --server=%s/kojihub taskinfo %s", config.KojiBPAk, config.KojiBPSk, baseURL, taskID)
		queryCmdConf := tools.CmdConfig{
			Name:      "bash",
			Args:      []string{"-c", queryCmd},
			IsShowCmd: true,
		}
		output, err := tools.ExecuteWithTimeout(&queryCmdConf, 60*time.Second)

		if err != nil {
			return "", fmt.Errorf("failed to query task status: %w", err)
		}
		scanner := bufio.NewScanner(strings.NewReader(output.String()))
		var status string
		for scanner.Scan() {
			line := strings.TrimSpace(scanner.Text())
			if strings.Contains(line, "State:") {
				status = strings.Split(line, "State: ")[1]
				break
			}
		}
		return status, nil
	}
}

// DownloadResult 执行下载任务结果
func DownloadResult(ctx context.Context, baseURL, taskID string, outputPath string) ([]string, error) {
	select {
	case <-ctx.Done(): // 监听取消
		return []string{}, ctx.Err()
	default:
		var resultList []string
		// 查询任务状态,判断任务是否完成
		status, err := QueryTaskStatus(ctx, baseURL, taskID)
		if err != nil || status != "closed" {
			return resultList, fmt.Errorf("failed to query task status: %w", err)
		}
		// 执行下载命令
		downloadCmd := fmt.Sprintf("koji --user=%s --password=%s --server=%s/kojihub --topurl=%s/kojifiles download-task --all %s", config.KojiBPAk, config.KojiBPSk, baseURL, baseURL, taskID)
		downloadCmdConf := tools.CmdConfig{
			Name:      "bash",
			Args:      []string{"-c", downloadCmd},
			Dir:       outputPath,
			IsShowCmd: true,
		}
		output, err := tools.ExecuteWithTimeout(&downloadCmdConf, 120*time.Second)
		if err != nil {
			return resultList, fmt.Errorf("failed to download result: %w", err)
		}
		scanner := bufio.NewScanner(strings.NewReader(output.String()))

		for scanner.Scan() {
			line := strings.TrimSpace(scanner.Text())
			if strings.Contains(line, "Downloading ") {
				status := strings.Split(line, ": ")[1]
				resultList = append(resultList, status)
			}
		}
		return resultList, nil
	}
}

// WaitForTaskCompletion 等待任务完成
func WaitForTaskCompletion(ctx context.Context, baseURL, taskID string, interval time.Duration) error {
	ticker := time.NewTicker(interval)
	defer ticker.Stop()
	for {
		select {
		case <-ctx.Done():
			return ctx.Err()
		case <-ticker.C:
			status, err := QueryTaskStatus(ctx, baseURL, taskID)
			if err != nil {
				return err
			}
			switch status {
			case "closed":
				return nil
			case "failed":
				return fmt.Errorf("%w: %s", errors.New("task failed"), taskID)
			}
		}
	}
}

func (ct *CornTask) GetKojiPlatformStats() {
	kojiList, err := model.GetKojiAddrList()

	if err != nil {
		logs.Log.Errorf("failed to get koji platform list: %v", err)
		return
	}
	for _, kojiaddr := range kojiList {
		var usageList []float64
		if strings.Contains(kojiaddr, "http://************") {
			kojiaddr = tools.ReplaceKojiAddr(kojiaddr)
		}
		listHostCmd := fmt.Sprintf("koji --server=%s/kojihub list-hosts", kojiaddr)
		listHostCmdConf := tools.CmdConfig{
			Name:      "bash",
			Args:      []string{"-c", listHostCmd},
			IsShowCmd: true,
		}
		listHostOutput, err := tools.ExecuteWithTimeout(&listHostCmdConf, 120*time.Second)
		if err != nil {
			logs.Log.Errorf("failed to get koji platform list: %v", err)
			return
		}
		scanner := bufio.NewScanner(strings.NewReader(listHostOutput.String()))
		for scanner.Scan() {
			line := strings.TrimSpace(scanner.Text())
			if line == "" {
				continue // 跳过空行
			}
			if strings.Contains(line, "build") {
				fields := strings.Fields(line)
				if len(fields) < 5 {
					continue // 忽略格式错误的行
				}

				if fields[2] != "Y" {
					continue
				}
				usage, err := CalculateUsage(fields[3])
				if err == nil {
					usageList = append(usageList, usage)
				}
			}
		}
		average, err := tools.CalculateAverage(usageList)
		KojiUsage[kojiaddr] = average
	}
}

func CalculateUsage(loadCap string) (float64, error) {
	parts := strings.Split(loadCap, "/")
	if len(parts) != 2 {
		return 0, fmt.Errorf("invalid Load/Cap format")
	}

	load, err := strconv.ParseFloat(parts[0], 64)
	if err != nil {
		return 0, fmt.Errorf("failed to parse Load: %v", err)
	}

	cap, err := strconv.ParseFloat(parts[1], 64)
	if err != nil {
		return 0, fmt.Errorf("failed to parse Cap: %v", err)
	}

	if cap <= 0 {
		return 0, fmt.Errorf("capacity must be positive")
	}
	return load / cap, nil
}

func DeterminePriority(xflowId int) (int, error) {
	userId, xflowType, err := model.GetUserIdAndTypeByXflowId(xflowId)
	if err != nil {
		return 0, err
	}
	if xflowType == "lectotype" && userId == 527 {
		return 1, nil
	}
	return 2, nil
}
