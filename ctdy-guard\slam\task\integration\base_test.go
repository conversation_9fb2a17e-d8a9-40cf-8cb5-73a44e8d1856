package integration

import (
	"slam/model"
	"slam/task/integration/option"
	"slam/utils"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/mock"
)

// Mock structs for testing
type MockOption struct {
	mock.Mock
}

func (m *MockOption) Execute(c *gin.Context, user *option.OpUser, params any) (any, error) {
	args := m.Called(c, user, params)
	return args.Get(0), args.Error(1)
}

func (m *MockOption) GetID() string {
	args := m.Called()
	return args.String(0)
}

func (m *MockOption) GetName() string {
	args := m.Called()
	return args.String(0)
}

type MockProcessManager struct {
	mock.Mock
}

func (m *MockProcessManager) ExecuteOption(c *gin.Context, userOp *Option, params any) (any, error) {
	args := m.Called(c, userOp, params)
	return args.Get(0), args.Error(1)
}

type MockIntegrationRepo struct {
	mock.Mock
}

func (m *MockIntegrationRepo) GetIntegrationList(pager utils.Pager, req *model.PacksListReq) (*model.Data_List, error) {
	args := m.Called(pager, req)
	return args.Get(0).(*model.Data_List), args.Error(1)
}

func (m *MockIntegrationRepo) GetIntegrationById(igid int) (*model.Integration, error) {
	args := m.Called(igid)
	return args.Get(0).(*model.Integration), args.Error(1)
}

// 补充缺失的方法实现
func (m *MockIntegrationRepo) CreateIntegration(integration *model.Integration) (int, error) {
	args := m.Called(integration)
	return args.Get(0).(int), args.Error(1)
}

func (m *MockIntegrationRepo) UpdateIntegration(integration *model.Integration) error {
	args := m.Called(integration)
	return args.Error(0)
}

func (m *MockIntegrationRepo) DeleteIntegration(igid int) error {
	args := m.Called(igid)
	return args.Error(0)
}

func (m *MockIntegrationRepo) GetIntegrationByBuildId(buildId int) (*model.Integration, error) {
	args := m.Called(buildId)
	return args.Get(0).(*model.Integration), args.Error(1)
}

func (m *MockIntegrationRepo) GetIntegrationByName(name string) (*model.Integration, error) {
	args := m.Called(name)
	return args.Get(0).(*model.Integration), args.Error(1)
}

func (m *MockIntegrationRepo) GetIntegrationByAliasName(aliasName string) (*model.Integration, error) {
	args := m.Called(aliasName)
	return args.Get(0).(*model.Integration), args.Error(1)
}

func (m *MockIntegrationRepo) GetIntegrationListByStatus(status int, pager utils.Pager) (*model.Data_List, error) {
	args := m.Called(status, pager)
	return args.Get(0).(*model.Data_List), args.Error(1)
}

func (m *MockIntegrationRepo) UpdateIntegrationStatus(igid, status int) error {
	args := m.Called(igid, status)
	return args.Error(0)
}

func (m *MockIntegrationRepo) GetNotExistBuildRpmIds() ([]int, error) {
	args := m.Called()
	return args.Get(0).([]int), args.Error(1)
}

type MockIntegrationRowsRepo struct {
	mock.Mock
}

func (m *MockIntegrationRowsRepo) UpdateWithMap(id int, umap map[string]any) (int, error) {
	args := m.Called(id, umap)
	return args.Get(0).(int), args.Error(1)
}
func (m *MockIntegrationRowsRepo) RemoveRows(ids []int) (int64, error) {
	args := m.Called(ids)
	return args.Get(0).(int64), args.Error(1)

}
func (m *MockIntegrationRowsRepo) GetIntegrationList(req *model.PacksListReq, pager utils.Pager) (any, int64, error) {
	args := m.Called(req, pager)
	return args.Get(0), args.Get(1).(int64), args.Error(2)
}

func (m *MockIntegrationRowsRepo) GetProductListByIgid(igid int) (map[int]int, error) {
	args := m.Called(igid)
	return args.Get(0).(map[int]int), args.Error(1)
}

func (m *MockIntegrationRowsRepo) GetXflowIdsByIgid(igid int) ([]int, error) {
	args := m.Called(igid)
	return args.Get(0).([]int), args.Error(1)
}

func (m *MockIntegrationRowsRepo) UpdateFromHistoryToNewIg(igid int) (int, error) {
	args := m.Called(igid)
	return args.Get(0).(int), args.Error(1)
}

func (m *MockIntegrationRowsRepo) BatchInsertIntegration(integrationRows any) error {
	args := m.Called(integrationRows)
	return args.Error(0)
}

func (m *MockIntegrationRowsRepo) BatchUpdateWithMapByIgid(igid int, data map[string]interface{}) error {
	args := m.Called(igid, data)
	return args.Error(0)
}

type MockFuncsModel struct {
	mock.Mock
}

func (m *MockFuncsModel) ProductList(ids []int) []model.ProductList {
	args := m.Called(ids)
	return args.Get(0).([]model.ProductList)
}

type MockIntegrationCheckRepo struct {
	mock.Mock
}

func (m *MockIntegrationCheckRepo) GetCheckList(id, checkType int) ([]*model.IntegrationCheck, error) {
	args := m.Called(id, checkType)
	return args.Get(0).([]*model.IntegrationCheck), args.Error(1)
}

func (m *MockIntegrationCheckRepo) CreateIntegrationCheck(check []*model.IntegrationCheck) error {
	args := m.Called(check)
	return args.Error(0)
}

func (m *MockIntegrationCheckRepo) GetCheckListByIgId(igid, checkType int) ([]*model.IntegrationCheck, error) {
	args := m.Called(igid, checkType)
	return args.Get(0).([]*model.IntegrationCheck), args.Error(1)
}

type MockXfllowModelRepo struct {
	mock.Mock
}

func (m *MockXfllowModelRepo) GetNeedReplenishPackages(igid int, xflowIds []int, packageName string) ([]string, error) {
	args := m.Called(igid, xflowIds, packageName)
	return args.Get(0).([]string), args.Error(1)
}

func (m *MockXfllowModelRepo) GetPackageList(pager utils.Pager, req model.PacksListReq) (*model.Data_List, error) {
	args := m.Called(pager, req)
	return args.Get(0).(*model.Data_List), args.Error(1)
}

func (m *MockXfllowModelRepo) GetBuildInfosByXflowIds(xflowIds []int) ([]*model.BuildMapping, error) {
	args := m.Called(xflowIds)
	return args.Get(0).([]*model.BuildMapping), args.Error(1)
}

func (m *MockXfllowModelRepo) GetStockBugCveList(req *model.BugCveListReq) ([]*model.StockBugCveList, []*model.IntegrationBug, []*model.IntegrationCve, error) {
	args := m.Called(req)
	return args.Get(0).([]*model.StockBugCveList),
		args.Get(1).([]*model.IntegrationBug),
		args.Get(2).([]*model.IntegrationCve),
		args.Error(3)
}

func (m *MockXfllowModelRepo) GetStockCenterPacksByIds(ids []int) []*model.StockCenterPackList {
	args := m.Called(ids)
	return args.Get(0).([]*model.StockCenterPackList)
}

type MockIntegrationCountRepo struct {
	mock.Mock
}

func (m *MockIntegrationCountRepo) GetIntegrationCount(igid, status int) (*model.IntegrationCount, error) {
	args := m.Called(igid, status)
	return args.Get(0).(*model.IntegrationCount), args.Error(1)
}

func (m *MockIntegrationCountRepo) UpdateCountInfo(igid int, status int, data map[string]interface{}) error {
	args := m.Called(igid, status, data)
	return args.Error(0)
}

func (m *MockIntegrationCountRepo) CreateIntegrationCount(count *model.IntegrationCount) error {
	args := m.Called(count)
	return args.Error(0)
}

type MockKojiPlatformInterface struct {
	mock.Mock
}

func (m *MockKojiPlatformInterface) GetKojiPlatforms() []model.KojiPlatform {
	args := m.Called()
	return args.Get(0).([]model.KojiPlatform)
}

type MockIntegrationKojiRepo struct {
	mock.Mock
}

func (m *MockIntegrationKojiRepo) BatchCreateKoji(kojis []*model.IntegrationKoji) error {
	args := m.Called(kojis)
	return args.Error(0)
}

type MockIntegrationRpmRepo struct {
	mock.Mock
}

func (m *MockIntegrationRpmRepo) BatchInsertIntegrationBuild(rpmInfo []*model.IntegrationRpmInfo) error {
	args := m.Called(rpmInfo)
	return args.Error(0)
}

type MockIntegrationReplenishRepo struct {
	mock.Mock
}

func (m *MockIntegrationReplenishRepo) BatchInsertReplenish(rows []*model.IntegrationReplenish) error {
	args := m.Called(rows)
	return args.Error(0)
}

type MockIntegrationModel struct {
	mock.Mock
	model.IntegrationRepo
}

func (m *MockIntegrationModel) GetIntegrationById(id int) (*model.Integration, error) {
	args := m.Called(id)
	if integration := args.Get(0); integration != nil {
		return integration.(*model.Integration), args.Error(1)
	}
	return nil, args.Error(1)
}

func (m *MockIntegrationModel) UpdateIntegration(ig *model.Integration) error {
	args := m.Called(ig)
	return args.Error(0)
}

func (m *MockIntegrationModel) CreateIntegration(ig *model.Integration) (int, error) {
	args := m.Called(ig)
	return args.Int(0), args.Error(1)
}

func intPointer(i int) *int {
	return &i
}

func getContext(roleId int) *gin.Context {
	// Mock context with non-admin user info
	ctx := &gin.Context{}

	userId := utils.SubmitterId
	user := model.User{
		Id:       intPointer(userId),
		Nickname: "testuser",
		Role_id:  roleId,
		Email:    "<EMAIL>",
	}
	ctx.Set("user_info", user)

	return ctx
}
