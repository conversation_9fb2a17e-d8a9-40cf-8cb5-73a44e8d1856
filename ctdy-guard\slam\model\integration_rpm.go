/*
 * @Author: lwq lvwenqi<PERSON>@kylinos.cn
 * @Date: 2025-05-18
 * @LastEditors: lwq <EMAIL>
 * @LastEditTime: 2025-07-16
 * @FilePath: /ctdy-guard/slam/model/integration_rpm.go
 * @Description: 集成单koji构建数据模型
 * @Copyright (c) 2025 by 2022.kylinos.cn, All Rights Reserved.
 */
package model

import (
	"time"

	"gorm.io/gorm"
)

type IntegrationRpmInfo struct {
	Id            int       `gorm:"column:id;primaryKey;autoIncrement:true;type:serial4;not null" json:"id,omitempty" label:"序号，主键"`
	IntegrationId int       `gorm:"column:integration_id;not null" json:"integration_id" label:"集成单ID"`
	XflowId       int       `gorm:"column:xflow_id;not null" json:"xflow_id" label:"工作流ID"`
	TagId         int       `gorm:"column:tag_id;not null" json:"tag_id" label:"tagID"`//待定吧，不一定有这个
	SrcName       string    `gorm:"column:src_name;type:varchar(255);not null;default:''" json:"src_name" label:"源码包名"`
	PackName      string    `gorm:"column:pack_name;type:varchar(255);not null;default:''" json:"pack_name" label:"包名"`
	BuildId       int       `gorm:"column:build_id;not null" json:"build_id" label:"koji构建ID"`
	Arch          string    `gorm:"column:arch_info;type:varchar(25);not null;default:''" json:"arch_info" label:"架构信息"`
	Nvr           string    `gorm:"column:nvr;type:varchar(255);not null;default:''" json:"nvr" label:"NVR信息"`
	UnsigndMd5    string    `gorm:"column:unsignd_md5;type:varchar(255);not null;default:''" json:"unsignd_md5" label:"未签名MD5"`
	UnsigndSha1   string    `gorm:"column:unsignd_sha1;type:varchar(255);not null;default:''" json:"unsignd_sha1" label:"未签名SHA1"`
	UnsigndSha256 string    `gorm:"column:unsignd_sha256;type:varchar(255);not null;default:''" json:"unsignd_sha256" label:"未签名SHA256"`
	SigndMd5      string    `gorm:"column:signd_md5;type:varchar(255);not null;default:''" json:"signd_md5" label:"已签名MD5"`
	SigndSha1     string    `gorm:"column:signd_sha1;type:varchar(255);not null;default:''" json:"signd_sha1" label:"已签名SHA1"`
	SigndSha256   string    `gorm:"column:signd_sha256;type:varchar(255);not null;default:''" json:"signd_sha256" label:"已签名SHA256"`
	InBlack       bool      `gorm:"column:in_black;not null;default:false" json:"in_black" label:"是否在黑名单中"`
	CreatedAt     time.Time `gorm:"column:create_time;not null" json:"create_time,omitempty" label:"创建时间"`
	UpdatedAt     time.Time `gorm:"column:update_time;not null" json:"update_time,omitempty" label:"更新时间"`
}

// TableName 表名
func (*IntegrationRpmInfo) TableName() string {
	return "slam_integration_rpm"
}

// IntegrationCveRepo 集成单Cve接口
type IntegrationRpmRepo interface {
	BatchInsertIntegrationBuild(rpmInfo []*IntegrationRpmInfo) error
}

type integrationRpmModel struct {
	db *gorm.DB
}

/**
 * @author: lwq <EMAIL>
 * @description: NewIntegrationCveModel 初始化集成单Cve模型
 * @return {*}
 */
func NewIntegrationBuildModel() IntegrationRpmRepo {
	return &integrationRpmModel{
		db: GetDB(),
	}
}

// 实现IntegrationBuildRepo接口
func (i *integrationRpmModel) BatchInsertIntegrationBuild(rpms []*IntegrationRpmInfo) error {
	return i.db.Create(&rpms).Error
}
