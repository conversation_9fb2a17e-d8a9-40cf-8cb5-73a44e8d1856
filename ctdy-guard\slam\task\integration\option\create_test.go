package option

import (
	"slam/global"
	"slam/model"
	"testing"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
)

func TestCreateOptionExecute(t *testing.T) {
	NewCreateOption()
	gin.SetMode(gin.TestMode)

	t.Run("successful creation", func(t *testing.T) {
		// Mocks
		mockIgModel := new(MockIntegrationRepo)
		mockCountModel := new(MockIntegrationCountRepo)

		// Test data
		opUser := &OpUser{
			UserInfo: UserInfo{
				UserId:   1,
				UserName: "testuser",
			},
		}

		// Mock expectations
		now := time.Now().Truncate(time.Second)
		mockIgModel.On("CreateIntegration", mock.AnythingOfType("*model.Integration")).Run(func(args mock.Arguments) {
			ig := args.Get(0).(*model.Integration)
			assert.Contains(t, ig.<PERSON>, "集成单")
			assert.Equal(t, "临时加急单", ig.Remark)
			assert.Equal(t, global.INTEGRATION_URGENT, ig.Urgent)
			assert.Equal(t, 1, ig.CreatorId)
			assert.Equal(t, "testuser", ig.CreatorName)
			assert.Equal(t, now.Format("2006-01-02"), ig.StartAt.Format("2006-01-02"))
			assert.Equal(t, global.INTEGRATION_STATUS_COMPARE, ig.Status)
		}).Return(10, nil)

		mockCountModel.On("CreateIntegrationCount", mock.MatchedBy(func(count *model.IntegrationCount) bool {
			return count.IntegrationId == 10
		})).Return(nil)

		// Create CreateOption instance
		createOption := &CreateOption{
			BaseOption: BaseOption{
				ID:   "create",
				Name: "创建集成单",
			},
			igModel:      mockIgModel,
			igCountModel: mockCountModel,
		}

		// Execute
		ctx, _ := gin.CreateTestContext(nil)
		result, err := createOption.Execute(ctx, opUser, nil)

		// Assertions
		assert.NoError(t, err)
		assert.Equal(t, 10, result)
		mockIgModel.AssertExpectations(t)
		mockCountModel.AssertExpectations(t)
	})
}
