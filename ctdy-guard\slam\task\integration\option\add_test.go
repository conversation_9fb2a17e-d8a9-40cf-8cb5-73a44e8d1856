package option

import (
	"slam/model"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
)

// Test cases
func TestAddOptionExecute(t *testing.T) {
	NewAddOption()
	// Setup
	gin.SetMode(gin.TestMode)

	t.Run("successful execution with bug and cve data", func(t *testing.T) {
		// Mocks
		mockBugModel := new(MockIntegrationRowsRepo)
		mockCveModel := new(MockIntegrationRowsRepo)
		mockXflowModel := new(MockXfllowModelRepo)
		mockCountModel := new(MockIntegrationCountRepo)

		// Test data
		bq := &model.BugCveListReq{
			IgId: 1,
		}

		opUser := &OpUser{
			UserInfo: UserInfo{
				UserId:   1,
				UserName: "testuser",
				RoleId:   1,
				RoleName: "tester",
				Email:    "<EMAIL>",
			},
		}

		// []*model.StockBugCveList, []*model.IntegrationBug, []*model.IntegrationCve, error
		mockXflowModel.On("GetStockBugCveList", bq).Return(
			[]*model.StockBugCveList{{XflowId: 1}},
			[]*model.IntegrationBug{{Id: 1}},
			[]*model.IntegrationCve{{Id: 1}}, nil,
		)
		mockBugModel.On("BatchInsertIntegration", mock.Anything).Return(nil)
		mockCveModel.On("BatchInsertIntegration", mock.Anything).Return(nil)
		mockCountModel.On("UpdateCountInfo", 1, mock.Anything, mock.Anything).Return(nil)

		// Create AddOption with mocks
		addOption := &AddOption{
			BaseOption: BaseOption{
				ID:   "add",
				Name: "添加软件包",
			},
			igBugModel:   mockBugModel,
			igCveModel:   mockCveModel,
			xflowModel:   mockXflowModel,
			igCountModel: mockCountModel,
		}

		// Execute
		ctx, _ := gin.CreateTestContext(nil)
		result, err := addOption.Execute(ctx, opUser, bq)

		// Assertions
		assert.NoError(t, err)
		assert.Nil(t, result)
		mockXflowModel.AssertExpectations(t)
		mockBugModel.AssertExpectations(t)
		mockCveModel.AssertExpectations(t)
		mockCountModel.AssertExpectations(t)
	})
}
