package model

import (
	"errors"
	"github.com/DATA-DOG/go-sqlmock"
	"testing"
)

func TestGetCommitTypeByXflowId(t *testing.T) {
	t.Run("success", func(t *testing.T) {
		rows := sqlmock.NewRows([]string{"commit_type"}).
			AddRow("2")
		mock.ExpectQuery("SELECT \"commit_type\" FROM \"slam_stocklist\" WHERE xflow_id=$1").
			WithArgs(18464).
			WillReturnRows(rows)
		GetCommitTypeByXflowId(18464)
	})

	t.Run("fail", func(t *testing.T) {
		mock.ExpectQuery("SELECT \"commit_type\" FROM \"slam_stocklist\" WHERE xflow_id=$1").
			WithArgs(18464).
			WillReturnError(errors.New("select error"))
		GetCommitTypeByXflowId(18464)
	})

}

func TestGetUserIdAndTypeByXflowId(t *testing.T) {
	t.Run("success", func(t *testing.T) {
		rows := sqlmock.NewRows([]string{"user_id", "type"}).
			AddRow(2, "stock")
		mock.ExpectQuery("SELECT user_id, type FROM \"slam_xflow\" WHERE id = $1").
			WithArgs(18464).
			WillReturnRows(rows)
		GetUserIdAndTypeByXflowId(18464)
	})
}
