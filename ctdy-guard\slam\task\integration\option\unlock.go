/*
 * @Author: lwq <EMAIL>
 * @Date: 2025-07-08
 * @LastEditors: lwq <EMAIL>
 * @LastEditTime: 2025-07-14
 * @FilePath: /ctdy-guard/slam/task/integration/option/unlock.go
 * @Description: 集成操作
 * @Copyright (c) 2025 by 2022.kylinos.cn, All Rights Reserved.
 */
package option

import (
	"slam/global"
	"slam/model"
	"slam/slog"

	"github.com/gin-gonic/gin"
)

// UnlockOption 解锁操作
type UnlockOption struct {
	BaseOption
	igModel model.IntegrationRepo // 集成单模型
}

func NewUnlockOption() *UnlockOption {
	return &UnlockOption{
		BaseOption: BaseOption{
			ID:   "unlock",
			Name: "解锁",
		},
		igModel: model.NewIntegrationModel(),
	}
}

func (o *UnlockOption) Execute(c *gin.Context, opUser *OpUser, params any) (any, error) {
	igInfo := opUser.OpIgInfo
	// 执行流转操作,把当前集成单状态流转到下一个状态，目前可以直接状态值加1
	igInfo.LockStatus = global.INTEGRATION_UNLOCK

	if err := o.igModel.UpdateIntegration(igInfo); err != nil {
		slog.ErrorWithContext(c, "更新集成单状态失败", err)
		return nil, err
	}
	return nil, nil
}
