/*
 * @Author: lwq <EMAIL>
 * @Date: 2025-07-08
 * @LastEditors: lwq <EMAIL>
 * @LastEditTime: 2025-07-24
 * @FilePath: /ctdy-guard/slam/task/integration/option/remove.go
 * @Description: 集成操作
 * @Copyright (c) 2025 by 2022.kylinos.cn, All Rights Reserved.
 */
package option

import (
	"slam/global"
	"slam/model"
	"slam/slog"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type RemoveReq struct {
	IgId int   `json:"igid" binding:"required"`
	Ids  []int `json:"ids" binding:"required"`
	Type int   `json:"type" binding:"required"`
}

// RemoveOption 删除软件包
type RemoveOption struct {
	BaseOption
	igCountModel model.IntegrationCountRepo
	igBugModel   model.IntegrationRowsRepo
	igCveModel   model.IntegrationRowsRepo
}

func NewRemoveOption() *RemoveOption {
	return &RemoveOption{
		BaseOption: BaseOption{
			ID:   "remove",
			Name: "删除软件包",
		},
		igCountModel: model.NewIntegrationCountModel(),
		igBugModel:   model.NewIntegrationBugModel(),
		igCveModel:   model.NewIntegrationCveModel(),
	}
}

func (o *RemoveOption) Execute(c *gin.Context, opUser *OpUser, params any) (any, error) {
	req := params.(*RemoveReq)
	umap := map[string]any{}
	var num int64
	switch req.Type {
	case model.IntegrationCheckTypeBug:
		num, _ = o.igBugModel.RemoveRows(req.Ids)
		umap["total_bug_count"] = gorm.Expr("total_bug_count - ?", num)
	case model.IntegrationCheckTypeCve:
		num, _ = o.igCveModel.RemoveRows(req.Ids)
		umap["total_cve_count"] = gorm.Expr("total_cve_count - ?", num)
	}
	umap["total_count"] = gorm.Expr("total_count - ?", num)
	err := o.igCountModel.UpdateCountInfo(req.IgId, global.INTEGRATION_STATUS_COMPARE, umap)
	if err != nil {
		slog.ErrorWithContext(c, err, umap)
	}
	return nil, nil
}
