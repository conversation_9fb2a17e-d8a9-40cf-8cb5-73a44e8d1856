package model

import (
	"regexp"
	"testing"
	"time"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/stretchr/testify/assert"
)

func TestBatchInsertIntegrationBuild(t *testing.T) {
	gormDB, tmock, err := initSqlmock()
	assert.NoError(t, err)
	// 设置测试用例
	tests := []struct {
		name    string
		rpms    []*IntegrationRpmInfo
		wantErr bool
	}{
		{
			name: "正常插入多条RPM信息",
			rpms: []*IntegrationRpmInfo{
				{
					IntegrationId: 1,
					XflowId:       100,
					TagId:         100,
					SrcName:       "test-package",
					PackName:      "test-package-1.0",
					BuildId:       1001,
					Arch:          "x86_64",
					Nvr:           "test-package-1.0-1",
					UnsigndMd5:    "md5sum1",
					UnsigndSha1:   "sha1sum1",
					UnsigndSha256: "sha256sum1",
					SigndMd5:      "signed_md5sum1",
					SigndSha1:     "signed_sha1sum1",
					SigndSha256:   "signed_sha256sum1",
					CreatedAt:     time.Now(),
					UpdatedAt:     time.Now(),
				},
			},
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 创建模型实例
			model := &integrationRpmModel{db: gormDB}

			// 设置SQL预期
			if len(tt.rpms) > 0 {
				tmock.ExpectBegin()
				tmock.ExpectQuery(regexp.QuoteMeta(`INSERT INTO "slam_integration_rpm" ("integration_id","xflow_id","tag_id","src_name","pack_name","build_id","arch_info","nvr","unsignd_md5","unsignd_sha1","unsignd_sha256","signd_md5","signd_sha1","signd_sha256","in_black","create_time","update_time") VALUES ($1,$2,$3,$4,$5,$6,$7,$8,$9,$10,$11,$12,$13,$14,$15,$16,$17)`)).WillReturnRows(sqlmock.NewRows([]string{"id"}).AddRow(1))
				tmock.ExpectCommit()
			}

			// 执行测试
			err = model.BatchInsertIntegrationBuild(tt.rpms)

			// 验证结果
			if tt.wantErr {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}

			// 确保所有SQL预期都已执行
			err = tmock.ExpectationsWereMet()
			assert.NoError(t, err)
		})
	}
}

func TestBatchInsertIntegrationKoji(t *testing.T) {
	gormDB, tmock, err := initSqlmock()
	assert.NoError(t, err)
	// 设置测试用例
	tests := []struct {
		name    string
		rpms    []*IntegrationKoji
		wantErr bool
	}{
		{
			name: "正常插入多条RPM信息",
			rpms: []*IntegrationKoji{
				{
					IntegrationId: 1,
					IpAddress:     "***********",
					Nvr:           "test-1.0.0",
					PackageName:   "test-1.0.0.src.rpm",
					CreatedAt:     time.Now(),
					UpdatedAt:     time.Now(),
				},
			},
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 创建模型实例
			model := &IntegrationKojiModel{db: gormDB}

			// 设置SQL预期
			if len(tt.rpms) > 0 {
				tmock.ExpectBegin()
				tmock.ExpectQuery(regexp.QuoteMeta(`INSERT INTO "slam_integration_koji" ("integration_id","ip_address","nvr","package_name","create_time","update_time") VALUES ($1,$2,$3,$4,$5,$6)`)).WillReturnRows(sqlmock.NewRows([]string{"id"}).AddRow(1))
				tmock.ExpectCommit()
			}

			// 执行测试
			err = model.BatchCreateKoji(tt.rpms)

			// 验证结果
			if tt.wantErr {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}

			// 确保所有SQL预期都已执行
			err = tmock.ExpectationsWereMet()
			assert.NoError(t, err)
		})
	}
}

func TestBatchInsertIntegrationReplenish(t *testing.T) {
	gormDB, tmock, err := initSqlmock()
	assert.NoError(t, err)
	// 设置测试用例
	tests := []struct {
		name    string
		rpms    []*IntegrationReplenish
		wantErr bool
	}{
		{
			name: "正常插入多条信息",
			rpms: []*IntegrationReplenish{
				{
					SrcName:   "rpm1",
					BugInfo:   "bug1",
					CveInfo:   "cve1",
					CreatedAt: time.Now(),
					UpdatedAt: time.Now(),
				},
			},
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 创建模型实例
			model := &IntegrationReplenishModel{db: gormDB}

			// 设置SQL预期
			if len(tt.rpms) > 0 {
				tmock.ExpectBegin()
				tmock.ExpectQuery(regexp.QuoteMeta(`INSERT INTO "slam_integration_replenish" ("src_name","bug_info","cve_info","create_time","update_time") VALUES ($1,$2,$3,$4,$5)`)).WillReturnRows(sqlmock.NewRows([]string{"id"}).AddRow(1))
				tmock.ExpectCommit()
			}

			// 执行测试
			err = model.BatchInsertReplenish(tt.rpms)

			// 验证结果
			if tt.wantErr {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}

			// 确保所有SQL预期都已执行
			err = tmock.ExpectationsWereMet()
			assert.NoError(t, err)
		})
	}
}

func TestNewModel(t *testing.T) {
	NewIntegrationBuildModel()
	NewIntegrationReplenishModel()
	NewIntegrationKojiModel()
}
