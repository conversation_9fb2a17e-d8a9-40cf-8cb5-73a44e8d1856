package option

import (
	"slam/global"
	"slam/model"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
)

func TestRotateOptionExecute(t *testing.T) {
	NewRotateOption()
	gin.SetMode(gin.TestMode)

	t.Run("successful rotate execution", func(t *testing.T) {
		// Mocks
		mockIgModel := new(MockIntegrationRepo)
		mockIgCountModel := new(MockIntegrationCountRepo)
		mockBugModel := new(MockIntegrationRowsRepo)
		mockCveModel := new(MockIntegrationRowsRepo)

		// Test data
		opUser := &OpUser{
			UserInfo: UserInfo{
				UserId:   1,
				UserName: "testuser",
				Email:    "<EMAIL>",
			},
			OpIgInfo: &model.Integration{
				Id:     10,
				Status: 1,
			},
		}

		params := false

		// Mock expectations
		mockIgCountModel.On("GetIntegrationCount", 10, 1).Return(&model.IntegrationCount{
			TotalBugCount:  5,
			TotalCveCount:  3,
			ReviewBugCount: 5,
			ReviewCveCount: 3,
		}, nil)

		mockIgModel.On("UpdateIntegration", mock.MatchedBy(func(ig *model.Integration) bool {
			return ig.Id == 10 && ig.Status == 2
		})).Return(nil)

		mockCveModel.On("BatchUpdateWithMapByIgid", 10, map[string]any{"check_res": 0}).Return(nil)
		mockBugModel.On("BatchUpdateWithMapByIgid", 10, map[string]any{"check_res": 0}).Return(nil)

		mockIgCountModel.On("CreateIntegrationCount", mock.MatchedBy(func(count *model.IntegrationCount) bool {
			return count.IntegrationId == 10 && count.IgStatus == 2
		})).Return(nil)

		// Create RotateOption instance with mocks
		rotateOption := &RotateOption{
			BaseOption: BaseOption{
				ID:   "rotate",
				Name: "流转集成单",
			},
			igModel:      mockIgModel,
			igCountModel: mockIgCountModel,
			bugModel:     mockBugModel,
			cveModel:     mockCveModel,
		}

		// Execute
		ctx, _ := gin.CreateTestContext(nil)
		result, err := rotateOption.Execute(ctx, opUser, params)

		// Assertions
		assert.NoError(t, err)
		assert.Nil(t, result)
		assert.Equal(t, 2, opUser.OpIgInfo.Status)
		mockIgModel.AssertExpectations(t)
		mockIgCountModel.AssertExpectations(t)
		mockBugModel.AssertExpectations(t)
		mockCveModel.AssertExpectations(t)
	})

	t.Run("rotate execution when integration is published", func(t *testing.T) {
		// Mocks
		mockIgModel := new(MockIntegrationRepo)
		mockIgCountModel := new(MockIntegrationCountRepo)
		mockBugModel := new(MockIntegrationRowsRepo)
		mockCveModel := new(MockIntegrationRowsRepo)

		// Test data
		opUser := &OpUser{
			UserInfo: UserInfo{
				UserId:   1,
				UserName: "testuser",
				Email:    "<EMAIL>",
			},
			OpIgInfo: &model.Integration{
				Id:     10,
				Status: global.INTEGRATION_STATUS_PUBLISHED,
			},
		}

		params := false

		// Create RotateOption instance with mocks
		rotateOption := &RotateOption{
			BaseOption: BaseOption{
				ID:   "rotate",
				Name: "流转集成单",
			},
			igModel:      mockIgModel,
			igCountModel: mockIgCountModel,
			bugModel:     mockBugModel,
			cveModel:     mockCveModel,
		}

		// Execute
		ctx, _ := gin.CreateTestContext(nil)
		result, err := rotateOption.Execute(ctx, opUser, params)

		// Assertions
		assert.Error(t, err)
		assert.Nil(t, result)
		assert.Equal(t, global.INTEGRATION_STATUS_PUBLISHED, opUser.OpIgInfo.Status)
	})
}

func TestRotateOptionCanRotate(t *testing.T) {
	gin.SetMode(gin.TestMode)

	t.Run("cannot rotate when integration is published", func(t *testing.T) {
		// Mocks
		mockIgModel := new(MockIntegrationRepo)
		mockIgCountModel := new(MockIntegrationCountRepo)
		mockBugModel := new(MockIntegrationRowsRepo)
		mockCveModel := new(MockIntegrationRowsRepo)

		// Create RotateOption instance with mocks
		rotateOption := &RotateOption{
			BaseOption: BaseOption{
				ID:   "rotate",
				Name: "流转集成单",
			},
			igModel:      mockIgModel,
			igCountModel: mockIgCountModel,
			bugModel:     mockBugModel,
			cveModel:     mockCveModel,
		}

		// Execute
		ctx, _ := gin.CreateTestContext(nil)
		result, igCount := rotateOption.CanRotate(ctx, 10, global.INTEGRATION_STATUS_PUBLISHED, false)

		// Assertions
		assert.False(t, result)
		assert.Nil(t, igCount)
	})

	t.Run("can rotate when skip is true", func(t *testing.T) {
		// Mocks
		mockIgModel := new(MockIntegrationRepo)
		mockIgCountModel := new(MockIntegrationCountRepo)
		mockBugModel := new(MockIntegrationRowsRepo)
		mockCveModel := new(MockIntegrationRowsRepo)

		// Mock expectations
		mockIgCountModel.On("GetIntegrationCount", 10, 1).Return(&model.IntegrationCount{
			TotalBugCount:  5,
			TotalCveCount:  3,
			ReviewBugCount: 2,
			ReviewCveCount: 1,
		}, nil)

		// Create RotateOption instance with mocks
		rotateOption := &RotateOption{
			BaseOption: BaseOption{
				ID:   "rotate",
				Name: "流转集成单",
			},
			igModel:      mockIgModel,
			igCountModel: mockIgCountModel,
			bugModel:     mockBugModel,
			cveModel:     mockCveModel,
		}

		// Execute
		ctx, _ := gin.CreateTestContext(nil)
		result, igCount := rotateOption.CanRotate(ctx, 10, 1, true) // skip = true

		// Assertions
		assert.True(t, result)
		assert.NotNil(t, igCount)
		mockIgCountModel.AssertExpectations(t)
	})

	t.Run("can rotate when status is compare", func(t *testing.T) {
		// Mocks
		mockIgModel := new(MockIntegrationRepo)
		mockIgCountModel := new(MockIntegrationCountRepo)
		mockBugModel := new(MockIntegrationRowsRepo)
		mockCveModel := new(MockIntegrationRowsRepo)

		// Mock expectations
		mockIgCountModel.On("GetIntegrationCount", 10, global.INTEGRATION_STATUS_COMPARE).Return(&model.IntegrationCount{
			TotalBugCount:  5,
			TotalCveCount:  3,
			ReviewBugCount: 2,
			ReviewCveCount: 1,
		}, nil)

		// Create RotateOption instance with mocks
		rotateOption := &RotateOption{
			BaseOption: BaseOption{
				ID:   "rotate",
				Name: "流转集成单",
			},
			igModel:      mockIgModel,
			igCountModel: mockIgCountModel,
			bugModel:     mockBugModel,
			cveModel:     mockCveModel,
		}

		// Execute
		ctx, _ := gin.CreateTestContext(nil)
		result, igCount := rotateOption.CanRotate(ctx, 10, global.INTEGRATION_STATUS_COMPARE, false)

		// Assertions
		assert.True(t, result)
		assert.NotNil(t, igCount)
		mockIgCountModel.AssertExpectations(t)
	})

	t.Run("cannot rotate when review not completed", func(t *testing.T) {
		// Mocks
		mockIgModel := new(MockIntegrationRepo)
		mockIgCountModel := new(MockIntegrationCountRepo)
		mockBugModel := new(MockIntegrationRowsRepo)
		mockCveModel := new(MockIntegrationRowsRepo)

		// Mock expectations
		mockIgCountModel.On("GetIntegrationCount", 10, 2).Return(&model.IntegrationCount{
			TotalBugCount:  5,
			TotalCveCount:  3,
			ReviewBugCount: 2, // Less than total
			ReviewCveCount: 3,
		}, nil)

		// Create RotateOption instance with mocks
		rotateOption := &RotateOption{
			BaseOption: BaseOption{
				ID:   "rotate",
				Name: "流转集成单",
			},
			igModel:      mockIgModel,
			igCountModel: mockIgCountModel,
			bugModel:     mockBugModel,
			cveModel:     mockCveModel,
		}

		// Execute
		ctx, _ := gin.CreateTestContext(nil)
		result, igCount := rotateOption.CanRotate(ctx, 10, 2, false)

		// Assertions
		assert.False(t, result)
		assert.Nil(t, igCount)
		mockIgCountModel.AssertExpectations(t)
	})

	t.Run("can rotate when review completed", func(t *testing.T) {
		// Mocks
		mockIgModel := new(MockIntegrationRepo)
		mockIgCountModel := new(MockIntegrationCountRepo)
		mockBugModel := new(MockIntegrationRowsRepo)
		mockCveModel := new(MockIntegrationRowsRepo)

		// Mock expectations
		mockIgCountModel.On("GetIntegrationCount", 10, 2).Return(&model.IntegrationCount{
			TotalBugCount:  5,
			TotalCveCount:  3,
			ReviewBugCount: 5,
			ReviewCveCount: 3,
		}, nil)

		// Create RotateOption instance with mocks
		rotateOption := &RotateOption{
			BaseOption: BaseOption{
				ID:   "rotate",
				Name: "流转集成单",
			},
			igModel:      mockIgModel,
			igCountModel: mockIgCountModel,
			bugModel:     mockBugModel,
			cveModel:     mockCveModel,
		}

		// Execute
		ctx, _ := gin.CreateTestContext(nil)
		result, igCount := rotateOption.CanRotate(ctx, 10, 2, false)

		// Assertions
		assert.True(t, result)
		assert.NotNil(t, igCount)
		mockIgCountModel.AssertExpectations(t)
	})
}
