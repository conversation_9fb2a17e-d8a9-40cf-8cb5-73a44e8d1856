[server]
; :8080 表示监听 0.0.0.0:8080
HOST_IP = ***
PORT = ***
HOST_DOMAIN_NAME = ***

[pgsql]
TYPE = ***
DATA_AK = ***
DATA_SK = ***
DATABASE_HOST = ***
DATABASE_PORT = ***
NAME = ***


[log]
LOG_LEVEL = ***
LOG_MAX_SIZE = ***
LOG_MAX_BACKUP = ***
LOG_MAX_AGE = ***
IS_STDOUT = ***
IS_STACK_TRACE = ***

[hook]
COMPATIBILITY_IP = ***
SONAR_TOKEN = ***
LICENSE_ADDRESS = ***
LICENSE_USERNAME = ***
LICENSE_PASSWORD = ***
COMPATIBILITY_REPO_URL = ***
INNER_TEST_URL = ***

[jenkins]
JENKINS_DOMAIN_NAME = ***
JENKINS_PORT = ***
JENKINS_AK = ***
JENKINS_SK = ***

[koji]
KOJI_AK = ***
KOJI_SK = ***
KOJI_BPAK = ***
KOJI_BPSK = ***