/*
 * @Author: lwq <EMAIL>
 * @Date: 2025-07-08
 * @LastEditors: lwq <EMAIL>
 * @LastEditTime: 2025-07-31
 * @FilePath: /ctdy-guard/slam/task/integration/option/download.go
 * @Description: 下载集成单
 * @Copyright (c) 2025 by 2022.kylinos.cn, All Rights Reserved.
 */
package option

import (
	"slam/global"
	"slam/model"
	"slam/utils"

	"github.com/gin-gonic/gin"
)

// DownloadOption 下载操作
type DownloadOption struct {
	BaseOption
	igBugModel   model.IntegrationRowsRepo
	igCveModel   model.IntegrationRowsRepo
	igCheckModel model.IntegrationCheckRepo
}

func NewDownloadOption() *DownloadOption {
	return &DownloadOption{
		BaseOption: BaseOption{
			ID:   "download",
			Name: "下载",
		},
		igBugModel:   model.NewIntegrationBugModel(),
		igCveModel:   model.NewIntegrationCveModel(),
		igCheckModel: model.NewIntegrationCheckModel(),
	}
}

func (o *DownloadOption) Execute(c *gin.Context, opUser *OpUser, params any) (any, error) {
	igid := params.(int)

	// 构建Excel配置
	config := &utils.ExcelConfig{
		SavePath: global.EXECL_DIR_PATH,
		Sheets: []*utils.SheetConfig{
			o.getBugSheetConfig(igid),
			o.getCveSheetConfig(igid),
			o.getCheckRecordSheetConfig(igid, model.IntegrationCheckTypeBug, "BUG审核记录"),
			o.getCheckRecordSheetConfig(igid, model.IntegrationCheckTypeCve, "CVE审核记录"),
		},
	}
	// 创建ExcelExporter实例
	exporter := utils.NewExcelExporter()
	file, _ := exporter.Export(config)
	return file, nil
}

/**
 * @author: lwq <EMAIL>
 * @description: getBugSheetConfig bug sheet下载配置
 * @param {*model.Data_List} dataList
 * @return {*}
 */
func (o *DownloadOption) getBugSheetConfig(igId int) *utils.SheetConfig {
	pager := utils.Pager{
		Index: 1,
		Size:  10000,
	}
	req := &model.PacksListReq{IgId: igId}
	anyBugs, total, _ := o.igBugModel.GetIntegrationList(req, pager)
	bugs := anyBugs.([]*model.IntegrationBug)
	config := &utils.SheetConfig{
		SheetName: "BUG信息",
		Headers: []*utils.ExcelHeader{
			{Title: "编号", Field: "Id", Width: 15},
			{Title: "源码包全名", Field: "SrcName", Width: 15},
			{Title: "关联产品", Field: "ProductName", Width: 10},
			{Title: "涉及架构", Field: "ArchInfo", Width: 10},
			{Title: "任务类型", Field: "TaskType", Width: 10},
			{Title: "禅道编号", Field: "ZentaoNo", Width: 10},
			{Title: "紧急程度", Field: "Level", Width: 10},
			{Title: "修改描述信息-对外", Field: "Describtion", Width: 10},
			{Title: "复现方法", Field: "ReplayMethod", Width: 10},
			{Title: "修复方法", Field: "RepairMethod", Width: 10},
			{Title: "登记日期", Field: "CreatedAt", Width: 10},
			{Title: "责任人", Field: "SubmiterName", Width: 10},
			{Title: "评估人", Field: "EvaluatorsToString", Width: 10},
			{Title: "二进制包信息", Field: "RmpsToString", Width: 30},
		},
		DataCallback: func(rowIndex int) any {
			if rowIndex >= len(bugs) {
				return nil
			}
			return bugs[rowIndex]
		},
		Total: total,
	}

	return config
}

/**
 * @author: lwq <EMAIL>
 * @description: getCveSheetConfig cve sheet下载配置
 * @param {*model.Data_List} dataList
 * @return {*}
 */
func (o *DownloadOption) getCveSheetConfig(igId int) *utils.SheetConfig {
	pager := utils.Pager{
		Index: 1,
		Size:  10000,
	}
	req := &model.PacksListReq{IgId: igId}
	anyCves, total, _ := o.igCveModel.GetIntegrationList(req, pager)
	cves := anyCves.([]*model.IntegrationCve)
	config := &utils.SheetConfig{
		SheetName: "CVE信息",
		Headers: []*utils.ExcelHeader{
			{Title: "编号", Field: "Id", Width: 15},
			{Title: "源码包全名", Field: "SrcName", Width: 15},
			{Title: "关联产品", Field: "ProductName", Width: 10},
			{Title: "涉及架构", Field: "ArchInfo", Width: 10},
			{Title: "任务类型", Field: "TaskType", Width: 10},
			{Title: "风险等级", Field: "Level", Width: 10},
			{Title: "漏洞编号", Field: "CveId", Width: 10},
			{Title: "紧急程度", Field: "Level", Width: 10},
			{Title: "责任人", Field: "SubmiterName", Width: 10},
			{Title: "评估人", Field: "EvaluatorsToString", Width: 10},
			{Title: "登记日期", Field: "CreatedAt", Width: 10},
			{Title: "验证情况", Field: "请测试人员确认通过后填写（通过/不通过）", Width: 10},
			{Title: "修改内容", Field: "FixInfo", Width: 10},
			{Title: "二进制包信息", Field: "RmpsToString", Width: 10},
		},
		DataCallback: func(rowIndex int) any {
			if rowIndex >= len(cves) {
				return nil
			}
			return cves[rowIndex]
		},
		Total: total,
	}

	return config
}

// 写一个导出审核记录的方法
func (o *DownloadOption) getCheckRecordSheetConfig(igId, checkType int, sheetName string) *utils.SheetConfig {
	records, _ := o.igCheckModel.GetCheckListByIgId(igId, checkType)
	config := &utils.SheetConfig{
		SheetName: sheetName,
		Headers: []*utils.ExcelHeader{
			{Title: "编号(bug/cve)", Field: "Identifier", Width: 15},
			{Title: "审核人", Field: "CheckerName", Width: 10},
			{Title: "审核结果", Field: "CheckRes", Width: 10},
			{Title: "审核意见", Field: "CheckAdvice", Width: 10},
			{Title: "审核时间", Field: "CreatedAt", Width: 10},
		},
		DataCallback: func(rowIndex int) any {
			if rowIndex >= len(records) {
				return nil
			}
			return records[rowIndex]
		},
		Total: int64(len(records)),
	}
	return config
}
