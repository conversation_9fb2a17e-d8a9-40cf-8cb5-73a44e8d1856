/*
 * @Author: lwq <EMAIL>
 * @Date: 2025-05-08
 * @LastEditors: lwq <EMAIL>
 * @LastEditTime: 2025-07-22
 * @FilePath: /ctdy-guard/slam/task/integration.go
 * @Description: 集成单相关功能
 * @Copyright (c) 2025 by 2022.kylinos.cn, All Rights Reserved.
 */
package task

import (
	"slam/model"
	"time"
)

const (
	CheckFirst  = 1
	CheckSecond = 2
)

type CheckInfo struct {
	CheckerID   int       `json:"checker_id" label:"审核人id"`
	CheckerName string    `json:"checker_name" label:"审核人姓名"`
	CheckResult int       `json:"check_result" label:"审核结果 1 审核通过 2 审核不通过"`
	CheckAdvice string    `json:"check_advice" label:"审核意见"`
	CheckTimes  int       `json:"check_times" label:"审核次数，1 首次审核 2 二次审核"`
	CreatedAt   time.Time `json:"check_time" label:"创建时间"`
}
type BugList struct {
	*model.IntegrationBugWithRpms
	*IntegrationExtends
}

type CveList struct {
	*model.IntegrationCveWithRpms
	*IntegrationExtends
}

type Permission struct {
	CurrentCheck int  `json:"current_check"`
	CheckFirst   bool `json:"check_first"`
	CheckSecond  bool `json:"check_second"`
}

type IntegrationExtends struct {
	Permission
	ProductName        string `json:"product_name"`
	RmpsToString       string `json:"rpm_info"`
	FirstChecker       string `json:"first_checker"`
	FirstCheckRes      string `json:"first_checke_res"`
	FirstCheckAdvice   string `json:"first_checke_advice"`
	SecondChecker      string `json:"second_checker"`
	SecondCheckRes     string `json:"second_checke_res"`
	SecondCheckAdvice  string `json:"second_checke_advice"`
	EvaluatorsToString string `json:"evaluators"`
}

type CreateIgReq struct {
	StartDate  string
	EndDate    string
	ProductIds []int
}

type CheckReq struct {
	Ids         []int
	IgId        int
	CheckResult int
	CheckAdvice string
	CheckTimes  int
	CheckType   int
}

// ProductModelInterface 创建接口，为了调用现有查询产品列表接口方便mock
type ModelFuncsInterface interface {
	/**
	 * @author: lwq <EMAIL>
	 * @description:ProductList 产品列表
	 * @param {[]int} ids
	 * @return {[]model.ProductList}
	 */
	ProductList(ids []int) []model.ProductList
}

type FuncsModel struct{}

// ProductList 产品列表 实现ProductModelInterface接口
func (d *FuncsModel) ProductList(ids []int) []model.ProductList {
	return model.GetProductListsByIds(ids)
}

type Integration struct {
	downloadMode bool
	xflowModel   model.XfllowModelRepo
	igModel      model.IntegrationRepo
	igBugModel   model.IntegrationRowsRepo
	igCveModel   model.IntegrationRowsRepo
	igCheckModel model.IntegrationCheckRepo
	igRpmModel   model.IntegrationRpmRepo
	funcsModel   ModelFuncsInterface
}

// /**
//  * @author: lwq <EMAIL>
//  * @description:  NewIntegration 初始化集成单
//  * @return {*}
//  */
// func NewIntegration() *Integration {
// 	return &Integration{
// 		downloadMode: false,
// 		xflowModel:   model.NewXfllowModel(),
// 		igModel:      model.NewIntegrationModel(),
// 		igBugModel:   model.NewIntegrationBugModel(),
// 		igCveModel:   model.NewIntegrationCveModel(),
// 		igCheckModel: model.NewIntegrationCheckModel(),
// 		igRpmModel:   model.NewIntegrationBuildModel(),
// 		funcsModel:   &FuncsModel{},
// 	}
// }

// /**
//  * @author: lwq <EMAIL>
//  * @description: CreateIntegration 创建集成单
//  * @param {*CreateIgReq} req
//  * @return {*}
//  */
// func (i *Integration) CreateIntegration(c *gin.Context, req *CreateIgReq) error {
// 	// 模拟数据，日期格式需要确定
// 	user := i.currentUser(c)
// 	if user == nil {
// 		return errors.New("用户信息错误")
// 	}
// 	bq := &model.StockListQuery{
// 		StartDate: req.StartDate,
// 		EndDate:   req.EndDate,
// 	}

// 	//插入Check数据
// 	startDate, _ := time.Parse(global.TIME_FORMAT_SECOUND, bq.StartDate)
// 	endDate, _ := time.Parse(global.TIME_FORMAT_SECOUND, bq.EndDate)
// 	req.ProductIds = []int{} //暂时不支持
// 	ig := &model.Integration{
// 		ProductID:   req.ProductIds,
// 		StartAt:     startDate,
// 		EndAt:       endDate,
// 		CreatorId:   *user.Id,
// 		CreatorName: user.Nickname,
// 	}

// 	igid, err := i.igModel.CreateIntegration(ig)
// 	if err != nil {
// 		return err
// 	}

// 	rows, bugs, cves, err := i.xflowModel.GetStockBugCveList(bq, igid)
// 	if err != nil {
// 		return err
// 	}

// 	if len(rows) > 0 {
// 		cves = append(cves, i.getPacksCve(rows, igid)...)
// 	}

// 	// 将数据插入表中
// 	if len(bugs) > 0 {
// 		err = i.igBugModel.BatchInsertIntegrationBug(bugs)
// 		if err != nil {
// 			return err
// 		}
// 	}
// 	if len(cves) > 0 {
// 		err = i.igCveModel.BatchInsertIntegrationCve(cves)
// 		if err != nil {
// 			return err
// 		}
// 	}

// 	return nil
// }

// /**
//  * @author: lwq <EMAIL>
//  * @description: GetIntegrationList 集成单列表
//  * @param {utils.Pager} pager
//  * @return {*model.Data_List, error}
//  */
// func (i *Integration) GetIntegrationList(pager utils.Pager) (*model.Data_List, error) {
// 	return i.igModel.GetIntegrationList(pager)
// }

// type params struct {
// 	Ctx       *gin.Context
// 	IgId      int
// 	SrcName   string
// 	Pager     utils.Pager
// 	CheckType int
// }

// /**
//  * @author: lwq <EMAIL>
//  * @description: GetIntegrationBugList 集成单bug列表
//  * @param {int} igid
//  * @param {utils.Pager} pager
//  * @return {[]*BugList, int64, error}
//  */
// func (i *Integration) GetIntegrationBugList(c *gin.Context, igid int, srcName string, pager utils.Pager) (*model.Data_List, error) {
// 	return getIntegrationItemList(
// 		&params{
// 			Ctx:       c,
// 			IgId:      igid,
// 			SrcName:   srcName,
// 			Pager:     pager,
// 			CheckType: model.IntegrationCheckTypeBug,
// 		},
// 		i, i.igBugModel.GetIntegrationBugList,
// 		func(bug *model.IntegrationBugWithRpms, extends *IntegrationExtends) *BugList {
// 			return &BugList{
// 				IntegrationBugWithRpms: bug,
// 				IntegrationExtends:     extends,
// 			}
// 		},
// 	)
// }

// /**
//  * @author: lwq <EMAIL>
//  * @description: GetIntegrationCveList 集成单cve列表
//  * @param {int} igid
//  * @param {utils.Pager} pager
//  * @return {*}
//  */
// func (i *Integration) GetIntegrationCveList(c *gin.Context, igid int, srcName string, pager utils.Pager) (*model.Data_List, error) {
// 	return getIntegrationItemList(
// 		&params{
// 			Ctx:       c,
// 			IgId:      igid,
// 			SrcName:   srcName,
// 			Pager:     pager,
// 			CheckType: model.IntegrationCheckTypeCve,
// 		},
// 		i, i.igCveModel.GetIntegrationCveList,
// 		func(cve *model.IntegrationCveWithRpms, extends *IntegrationExtends) *CveList {
// 			return &CveList{
// 				IntegrationCveWithRpms: cve,
// 				IntegrationExtends:     extends,
// 			}
// 		},
// 	)
// }

// /**
//  * @author: lwq <EMAIL>
//  * @description: GetIntegrationById 集成单详情
//  * @param {int} igid
//  * @return {*}
//  */
// func (i *Integration) GetIntegrationById(igid int) (*model.Integration, error) {
// 	return i.igModel.GetIntegrationById(igid)
// }

// /**
//  * @author: lwq <EMAIL>
//  * @description: CheckIntegrationFirst 集成单审核，架构负责人一审
//  * @return {*}
//  */
// func (i *Integration) CheckIntegrationFirst(c *gin.Context, req *CheckReq) error {
// 	user := i.currentUserCanCheck(c)
// 	if user == nil {
// 		return errors.New("您没有审核权限")
// 	}

// 	req.CheckTimes = CheckFirst
// 	i.addCheckInfo(req, user)
// 	return nil
// }

// /**
//  * @author: lwq <EMAIL>
//  * @description: CheckIntegrationSecond 集成单审核，技术负责人二审
//  * @param {*gin.Context} c
//  * @param {[]int} ids
//  * @param {int} ctype
//  * @return {*}
//  */
// func (i *Integration) CheckIntegrationSecond(c *gin.Context, req *CheckReq) error {
// 	user := i.currentUserCanCheck(c)
// 	if user == nil {
// 		return errors.New("您没有审核权限")
// 	}

// 	//判断是否经过一审，

// 	req.CheckTimes = CheckSecond
// 	i.addCheckInfo(req, user)
// 	return nil
// }

// /**
//  * @author: lwq <EMAIL>
//  * @description: ExportIntegration 导出集成单 临时方法
//  * @param {*gin.Context} c
//  * @param {int} igid
//  * @return {*}
//  */
// func (i *Integration) ExportIntegration(c *gin.Context, igid int) (string, error) {
// 	i.downloadMode = true
// 	defer func() {
// 		i.downloadMode = false
// 	}()
// 	if igid <= 0 {
// 		return "", nil
// 	}
// 	bugs, err := i.GetIntegrationBugList(c, igid, "", utils.Pager{Index: 1, Size: 100000})
// 	cves, err1 := i.GetIntegrationCveList(c, igid, "", utils.Pager{Index: 1, Size: 100000})
// 	if err != nil || err1 != nil {
// 		return "", err
// 	}
// 	// 构建Excel配置
// 	config := &utils.ExcelConfig{
// 		SavePath: global.EXECL_DIR_PATH,
// 		Sheets: []*utils.SheetConfig{
// 			i.getBugSheetConfig(bugs),
// 			i.getCveSheetConfig(cves),
// 		},
// 	}
// 	// 创建ExcelExporter实例
// 	exporter := utils.NewExcelExporter()
// 	file, err := exporter.Export(config)
// 	return file, err
// }

// /**
//  * @author: lwq <EMAIL>
//  * @description: GetNotExistBuildRpmIds 获取未存在的rpm信息
//  * @return {[]int, error}
//  */
// func (i *Integration) GetNotExistBuildRpmIds() ([]int, error) {
// 	return i.igModel.GetNotExistBuildRpmIds()
// }

// /**
//  * @author: lwq <EMAIL>
//  * @description: FlushBuildInfo 刷新build信息
//  * @description: 该方法需要循环调用koji接口，基于定时任务，不要求速度，
//  * @description: 为防止对koji接口造成压力，不加并发处理，每次处理5个ID
//  * @param {int} igid
//  * @return {*}
//  */
// func (i *Integration) FlushBuildInfo(igid int) {
// 	// 根据igid查询所有xflowid
// 	xflowIdsForBug, _ := i.igBugModel.GetXflowIdsByIgid(igid)
// 	xflowIdsForCve, _ := i.igCveModel.GetXflowIdsByIgid(igid)
// 	xflowIds := append(xflowIdsForBug, xflowIdsForCve...)

// 	// 将 xflowIds 分块，每次处理5个ID
// 	chunkSize := 5
// 	for n := 0; n < len(xflowIds); n += chunkSize {
// 		end := n + chunkSize
// 		if end > len(xflowIds) {
// 			end = len(xflowIds)
// 		}
// 		chunkIds := xflowIds[n:end]

// 		// 处理当前分块
// 		rpmInfo := i.getBuildRpmInfo(chunkIds, igid)
// 		if rpmInfo != nil {
// 			err := i.igRpmModel.BatchInsertIntegrationBuild(rpmInfo)
// 			if err != nil {
// 				slog.Error.Println("FlushBuildInfo:BatchInsertIntegrationBuild err:", err)
// 				return
// 			}
// 		}
// 	}
// }

// /**
//  * @author: lwq <EMAIL>
//  * @description: 把中心仓的包信息转换为集成单的Cve信息
//  * @param {[]*model.StockBugCveList} rows
//  * @param {int} igid
//  * @return {[]*model.IntegrationCve}
//  */
// func (i *Integration) getPacksCve(rows []*model.StockBugCveList, igid int) []*model.IntegrationCve {
// 	var cves []*model.IntegrationCve
// 	for _, row := range rows {
// 		if len(row.Packs) <= 0 {
// 			continue
// 		}
// 		// 1.1、根据xflowid的packs查询中心仓对应的包信息
// 		packs := i.xflowModel.GetStockCenterPacksByIds(row.Packs)
// 		// 1.2、根据包信息查到对应产品的架构信息
// 		productMap, archs := i.getProductAndArchs(packs)
// 		// 循环packs
// 		for _, pack := range packs {
// 			product, ok := productMap[pack.ProdId]
// 			if !ok {
// 				continue
// 			}
// 			// 1.2.1、循环架构信息构建model.IntegrationBug
// 			for _, archId := range product.ProductArchs {
// 				archName, ok := archs[archId]
// 				if !ok {
// 					continue
// 				}
// 				cve := &model.IntegrationCve{
// 					IntegrationId: igid,
// 					XflowId:       row.XflowId,
// 					ProductId:     pack.ProdId,
// 					ArchInfo:      archName,
// 					SubmiterId:    row.UserId,
// 					SubmiterName:  row.UserName,
// 					SrcName:       pack.PackName,
// 					TaskType:      row.Type,
// 				}
// 				cves = append(cves, cve) // 1.3、append bugs
// 			}
// 		}
// 	}
// 	return cves
// }

// /**
//  * @author: lwq <EMAIL>
//  * @description: getProductAndArchs 获取产品和架构信息
//  * @description: 1、根据packs的prodId查询产品信息
//  * @description: 2、根据产品信息的ProductArchs查询架构信息
//  * @param {[]*model.StockCenterPackList} packs
//  * @return {map[int]*model.ProductList, map[int]string}
//  */
// func (i *Integration) getProductAndArchs(packs []*model.StockCenterPackList) (
// 	map[int]*model.ProductList, map[int]string,
// ) {
// 	productIds := []int{}
// 	for _, pack := range packs {
// 		productIds = append(productIds, pack.ProdId)
// 	}
// 	// 去重productIds
// 	productIds = utils.RemoveRepeatElement(productIds)
// 	productMap := i.getProductList(productIds)
// 	// 提取 ProductArchs
// 	archIds := []int{}
// 	for _, product := range productMap {
// 		archIds = append(archIds, product.ProductArchs...)
// 	}
// 	// 去重archIds
// 	archIds = utils.RemoveRepeatElement(archIds)
// 	archs := i.xflowModel.GetArchsByIds(archIds)
// 	return productMap, archs
// }

// /**
//  * @author: lwq <EMAIL>
//  * @description: getBuildRpmInfo 获取build rpm信息
//  * @param {[]int} xflowIds
//  * @param {int} igid
//  * @return {[]*model.IntegrationRpmInfo}
//  */
// func (i *Integration) getBuildRpmInfo(xflowIds []int, igid int) []*model.IntegrationRpmInfo {
// 	if len(xflowIds) <= 0 {
// 		return nil
// 	}
// 	// 根据xflow_id查询所有的build_id
// 	buildMap, err := i.xflowModel.GetBuildInfosByXflowIds(xflowIds)
// 	if err != nil {
// 		slog.Error.Println("FlushBuildInfo:buildMap is empty")
// 		return nil
// 	}

// 	// 调用koji接口获取build信息
// 	rpmInfo, err := i.getKojiBuildInfo(buildMap, igid)
// 	if err != nil {
// 		slog.Error.Println("FlushBuildInfo:getKojiBuildInfoerr:", err)
// 		return nil
// 	}
// 	return rpmInfo
// }

// /**
//  * @author: lwq <EMAIL>
//  * @description: getKojiBuildInfo 调用koji接口获取build信息
//  * @param {[]*model.BuildMapping} buildInfos
//  * @param {int} igid
//  * @return {[]*model.IntegrationRpmInfo, error}
//  */
// func (i *Integration) getKojiBuildInfo(buildInfos []*model.BuildMapping, igid int) (
// 	[]*model.IntegrationRpmInfo, error,
// ) {
// 	var rpmInfos []*model.IntegrationRpmInfo
// 	for _, buildMapping := range buildInfos {
// 		rpms, kojiClient, buildId, err := i.getKojiRpms(buildMapping)
// 		if err != nil {
// 			return nil, err
// 		}
// 		for _, rpm := range rpms {
// 			if !i.checkNeedUsed(&rpm) {
// 				continue
// 			}
// 			rpmInfo := &model.IntegrationRpmInfo{
// 				IntegrationId: igid,
// 				XflowId:       buildMapping.XflowId,
// 				SrcName:       buildMapping.SrcName,
// 				BuildId:       buildId,
// 				Arch:          rpm.Arch,
// 				Nvr:           rpm.Nvr,
// 				PackName:      rpm.Name,
// 			}
// 			//查询 koji 当前 rpmid 对应的签名数据
// 			err = i.setSignInfo(kojiClient, rpmInfo, rpm.Id)
// 			if err != nil {
// 				continue
// 			}
// 			rpmInfos = append(rpmInfos, rpmInfo)
// 		}
// 	}

// 	return rpmInfos, nil
// }

// /**
//  * @author: lwq <EMAIL>
//  * @description:setSignInfo 获取签名数据
//  * @param {*koji.Client} kojiClient
//  * @param {*model.IntegrationRpmInfo} rpmInfo
//  * @param {int} rpmId
//  * @return {*}
//  */
// func (i *Integration) setSignInfo(kojiClient *koji.Client, rpmInfo *model.IntegrationRpmInfo, rpmId int) error {
// 	signInfos, err := kojiClient.GetRPMChecksums(koji.ParamGetRPMChecksums{
// 		RpmId: koji.GetPointerInt(rpmId),
// 	})

// 	if err != nil {
// 		return err
// 	}
// 	for _, signInfo := range signInfos {
// 		//key 为空的时候是未签名
// 		if signInfo.Key == "" {
// 			rpmInfo.UnsigndMd5 = signInfo.Md5
// 			rpmInfo.UnsigndSha1 = signInfo.Sha1
// 			rpmInfo.UnsigndSha256 = signInfo.Sha256
// 		} else {
// 			rpmInfo.SigndMd5 = signInfo.Md5
// 			rpmInfo.SigndSha1 = signInfo.Sha1
// 			rpmInfo.SigndSha256 = signInfo.Sha256
// 		}
// 	}
// 	return nil
// }

// /**
//  * @author: lwq <EMAIL>
//  * @description: checkNeedUsed 检查是否需要使用
//  * @param {*model.BuildMapping} buildMapping
//  * @return {bool}
//  */
// func (i *Integration) checkNeedUsed(rpm *koji.ListRpmsRpmInfo) bool {
// 	// 判断 rpm.Arch 是否为 "src"
// 	if rpm.Arch == "src" {
// 		return false
// 	}
// 	// 判断 rpm.Nvr 是否包含 "kylin"
// 	if strings.Contains(rpm.Nvr, "debugsource") {
// 		return false
// 	}

// 	return true
// }

// /**
//  * @author: lwq <EMAIL>
//  * @description: getKojiRpms 查询Koji平台编译的软件包列表
//  * @param {*model.BuildMapping} buildMapping
//  * @return {[]koji.ListRpmsRpmInfo, *koji.Client, int, error}
//  */
// func (i *Integration) getKojiRpms(buildMapping *model.BuildMapping) (
// 	[]koji.ListRpmsRpmInfo, *koji.Client, int, error,
// ) {
// 	// buildid 查询 koji 的构建包列表
// 	fmt.Println("正在查询Koji构建Rpms:", buildMapping.IpAddress)
// 	// 根据kojilist中的ip字段初始化kojiClient
// 	kojiClient := koji.NewClient(
// 		koji.WithUrlBase(buildMapping.IpAddress),
// 	)
// 	// 解析源码包
// 	packInfo := utils.ParsePackageName(
// 		buildMapping.SrcName, global.SOURCE_NAME_SUBFIX, false,
// 	)
// 	// 根据nvr获取Koji的build信息
// 	buildInfo, _ := kojiClient.GetBuild(koji.ParamGetBuild{
// 		Nvr: koji.GetPointerString(strings.Join([]string{
// 			packInfo.Name, packInfo.Version, packInfo.Release,
// 		}, "-")),
// 	})
// 	if buildInfo.BuildId == 0 {
// 		slog.Error.Println("getKojiBuildInfo:未获取到构建信息")
// 		return nil, nil, 0, errors.New("未获取到构建信息")
// 	}

// 	// 根据buildid 查询当前构建的rmp包列表
// 	rpms, err := kojiClient.ListRpms(koji.ParamListRpms{
// 		BuildId: koji.GetPointerString(fmt.Sprintf("%d", buildInfo.BuildId)),
// 	})

// 	if err != nil {
// 		slog.Error.Println("getKojiBuildInfo:未获取到软件包列表")
// 		return nil, nil, 0, err
// 	}

// 	return rpms, kojiClient, buildInfo.BuildId, nil
// }

// /**
//  * @author: lwq <EMAIL>
//  * @description: getProductList 获取产品列表
//  * @param {[]int} ids
//  * @return {map[int]*model.ProductList}
//  */
// func (i *Integration) getProductList(ids []int) map[int]*model.ProductList {
// 	products := i.funcsModel.ProductList(ids)
// 	productMap := make(map[int]*model.ProductList)
// 	for i := range products {
// 		product := products[i] // 创建新的变量
// 		productMap[*product.Id] = &product
// 	}
// 	return productMap
// }

// /**
//  * @author: lwq <EMAIL>
//  * @description: currentUserCanCheck 当前用户是否有权限审核
//  * @description: 判断是技术负责人或者架构负责人,需要修改，需要根据产品绑定用户来判断而不是角色
//  * @param {*gin.Context} c
//  * @return {*}
//  */
// func (i *Integration) currentUserCanCheck(c *gin.Context) *model.User {
// 	//获取用户信息，判断是否是技术负责人
// 	userItem := i.currentUser(c)
// 	if userItem == nil {
// 		return nil
// 	}

// 	return userItem
// }

// func (i *Integration) currentUser(c *gin.Context) *model.User {
// 	//获取用户信息，判断是否是技术负责人
// 	_, _, userItem := GetUserInfo(c)
// 	return &userItem
// }

// /**
//  * @author: lwq <EMAIL>
//  * @description: 添加审核信息
//  * @param {*CheckReq} req
//  * @param {*model.User} user
//  * @return {*}
//  */
// func (i *Integration) addCheckInfo(req *CheckReq, user *model.User) {
// 	igCheckInfos := []*model.IntegrationCheck{}
// 	for _, id := range req.Ids {
// 		igCheckInfo := &model.IntegrationCheck{
// 			Identifier:    id,
// 			CheckerID:     *user.Id,
// 			CheckerName:   user.Nickname,
// 			IntegrationId: req.IgId,
// 			CheckResult:   req.CheckResult,
// 			CheckAdvice:   req.CheckAdvice,
// 			CheckTimes:    req.CheckTimes,
// 			CheckType:     req.CheckType,
// 		}
// 		igCheckInfos = append(igCheckInfos, igCheckInfo)
// 	}
// 	if len(igCheckInfos) > 0 {
// 		i.igCheckModel.CreateIntegrationCheck(igCheckInfos)
// 	}
// }

// /**
//  * @author: lwq <EMAIL>
//  * @description: getBugSheetConfig bug sheet下载配置
//  * @param {*model.Data_List} dataList
//  * @return {*}
//  */
// func (i *Integration) getBugSheetConfig(dataList *model.Data_List) *utils.SheetConfig {
// 	config := &utils.SheetConfig{
// 		SheetName: "BUG信息",
// 		Headers: []*utils.ExcelHeader{
// 			{Title: "源码包全名", Field: "SrcName", Width: 15},
// 			{Title: "关联产品", Field: "ProductName", Width: 10},
// 			{Title: "一审人员", Field: "FirstChecker", Width: 10},
// 			{Title: "一审审核结果", Field: "FirstCheckRes", Width: 10},
// 			{Title: "一审审核意见", Field: "FirstCheckAdvice", Width: 10},
// 			{Title: "二审人员", Field: "SecondChecker", Width: 10},
// 			{Title: "二审审核结果", Field: "SecondCheckRes", Width: 10},
// 			{Title: "二审审核意见", Field: "SecondCheckAdvice", Width: 10},
// 			{Title: "涉及架构", Field: "ArchInfo", Width: 10},
// 			{Title: "任务类型", Field: "TaskType", Width: 10},
// 			{Title: "禅道编号", Field: "ZentaoNo", Width: 10},
// 			{Title: "紧急程度", Field: "Level", Width: 10},
// 			{Title: "修改描述信息-对外", Field: "Describtion", Width: 10},
// 			{Title: "复现方法", Field: "ReplayMethod", Width: 10},
// 			{Title: "修复方法", Field: "RepairMethod", Width: 10},
// 			{Title: "登记日期", Field: "CreatedAt", Width: 10},
// 			{Title: "责任人", Field: "SubmiterName", Width: 10},
// 			{Title: "评估人", Field: "EvaluatorsToString", Width: 10},
// 			{Title: "二进制包信息", Field: "RmpsToString", Width: 30},
// 		},
// 		DataCallback: func(rowIndex int) interface{} {
// 			list := dataList.List.([]*BugList)
// 			if rowIndex >= len(list) {
// 				return nil
// 			}
// 			return list[rowIndex]
// 		},
// 		Total: dataList.Page.Total,
// 	}

// 	return config
// }

// /**
//  * @author: lwq <EMAIL>
//  * @description: getCveSheetConfig cve sheet下载配置
//  * @param {*model.Data_List} dataList
//  * @return {*}
//  */
// func (i *Integration) getCveSheetConfig(dataList *model.Data_List) *utils.SheetConfig {
// 	config := &utils.SheetConfig{
// 		SheetName: "CVE信息",
// 		Headers: []*utils.ExcelHeader{
// 			{Title: "源码包全名", Field: "SrcName", Width: 15},
// 			{Title: "关联产品", Field: "ProductName", Width: 10},
// 			{Title: "一审人员", Field: "FirstChecker", Width: 10},
// 			{Title: "一审审核结果", Field: "FirstCheckRes", Width: 10},
// 			{Title: "一审审核意见", Field: "FirstCheckAdvice", Width: 10},
// 			{Title: "二审人员", Field: "SecondChecker", Width: 10},
// 			{Title: "二审审核结果", Field: "SecondCheckRes", Width: 10},
// 			{Title: "二审审核意见", Field: "SecondCheckAdvice", Width: 10},
// 			{Title: "涉及架构", Field: "ArchInfo", Width: 10},
// 			{Title: "任务类型", Field: "TaskType", Width: 10},
// 			{Title: "风险等级", Field: "Level", Width: 10},
// 			{Title: "漏洞编号", Field: "CveId", Width: 10},
// 			{Title: "紧急程度", Field: "Level", Width: 10},
// 			{Title: "责任人", Field: "SubmiterName", Width: 10},
// 			{Title: "评估人", Field: "EvaluatorsToString", Width: 10},
// 			{Title: "登记日期", Field: "CreatedAt", Width: 10},
// 			{Title: "验证情况", Field: "请测试人员确认通过后填写（通过/不通过）", Width: 10},
// 			{Title: "修改内容", Field: "FixInfo", Width: 10},
// 			{Title: "二进制包信息", Field: "RmpsToString", Width: 10},
// 		},
// 		DataCallback: func(rowIndex int) interface{} {
// 			list := dataList.List.([]*CveList)
// 			if rowIndex >= len(list) {
// 				return nil
// 			}
// 			return list[rowIndex]
// 		},
// 		Total: dataList.Page.Total,
// 	}

// 	return config
// }

// // processCheckInfo 处理审核信息
// func processCheckInfo(item model.IntegrationItem, checks []*model.IntegrationCheck) *IntegrationExtends {
// 	iExtends := &IntegrationExtends{}
// 	for _, check := range checks {
// 		if item.GetID() == check.Identifier {
// 			switch check.CheckTimes {
// 			case CheckFirst:
// 				iExtends.FirstChecker = check.CheckerName
// 				iExtends.FirstCheckRes = model.IntegrationCheckDescMap[check.CheckResult]
// 				iExtends.FirstCheckAdvice = check.CheckAdvice
// 			case CheckSecond:
// 				iExtends.SecondChecker = check.CheckerName
// 				iExtends.SecondCheckRes = model.IntegrationCheckDescMap[check.CheckResult]
// 				iExtends.SecondCheckAdvice = check.CheckAdvice
// 			}
// 		}
// 	}
// 	return iExtends
// }

// // processPermissionAndProduct 处理权限和产品信息
// func processPermissionAndProduct(iExtends *IntegrationExtends, product *model.ProductList, checkTimes int, currentUser *model.User) {
// 	iExtends.Permission = Permission{
// 		CurrentCheck: checkTimes,
// 		CheckFirst:   checkTimes == CheckFirst && product.ArchitectureManager == *currentUser.Id,
// 		CheckSecond:  checkTimes == CheckSecond && product.TechnicalManager == *currentUser.Id,
// 	}
// 	iExtends.ProductName = product.ProductName
// }

// // processRpmAndEvaluatorsInfo 处理RPM信息和人员信息，只有下载模式下使用
// func processRpmAndEvaluatorsInfo(iExtends *IntegrationExtends, item model.IntegrationItem, evaluatorsMap map[int][]*model.Evaluator) {
// 	if len(item.GetRpms()) > 0 {
// 		var rpmStrings []string
// 		for _, rpm := range item.GetRpms() {
// 			rpmStrings = append(rpmStrings, fmt.Sprintf("软件包：%s[sha256:%s]", rpm.SrcName, rpm.Sha256))
// 		}
// 		iExtends.RmpsToString = strings.Join(rpmStrings, "\n")
// 	}
// 	if evaluators, ok := evaluatorsMap[item.GetXflowID()]; ok {
// 		var evaluatorStrings []string
// 		for _, evaluator := range evaluators {
// 			evaluatorStrings = append(evaluatorStrings, fmt.Sprintf("评估人：%s[%s]", evaluator.Nickname, evaluator.Email))
// 		}
// 		iExtends.EvaluatorsToString = strings.Join(evaluatorStrings, "\n")
// 	}
// }

// /**
//  * @author: lwq <EMAIL>
//  * @description: getIntegrationItemList 利用泛型特性，提取相同逻辑作为模版
//  * @return {*}
//  */
// func getIntegrationItemList[T model.IntegrationItem, L any](
// 	params *params, integration *Integration,
// 	getList func(int, string, utils.Pager) ([]T, int64, error),
// 	wrapItem func(T, *IntegrationExtends) L,
// ) (*model.Data_List, error) {
// 	currentUser := integration.currentUser(params.Ctx)
// 	if currentUser.Id == nil {
// 		return nil, errors.New("用户信息获取失败")
// 	}
// 	var itemList []L
// 	returnRes := &model.Data_List{
// 		List: itemList,
// 		Page: model.PageRes{
// 			Page_index: params.Pager.Index,
// 			Page_size:  params.Pager.Size,
// 			Total:      0,
// 		},
// 	}
// 	items, total, err := getList(params.IgId, params.SrcName, params.Pager)
// 	if err != nil || total == 0 {
// 		return returnRes, nil
// 	}

// 	// 获取审核信息
// 	checks, _ := integration.igCheckModel.GetIntegrationCheckList(params.CheckType, params.IgId)
// 	checkTimes := len(checks)

// 	// 获取产品信息
// 	pids := make([]int, len(items))
// 	xflowIds := make([]int, len(items))
// 	for idx, item := range items {
// 		pids[idx] = item.GetProductID()
// 		xflowIds[idx] = item.GetXflowID()
// 	}
// 	products := integration.getProductList(utils.RemoveRepeatElement(pids))
// 	var evaluatorsMap map[int][]*model.Evaluator
// 	if integration.downloadMode {
// 		//查询各种人员信息
// 		evaluatorsMap = integration.xflowModel.GetEvaluatorsByXflowIds(xflowIds)
// 	}
// 	// 处理每个项目
// 	for _, item := range items {
// 		// 处理审核信息
// 		iExtends := processCheckInfo(item, checks)

// 		// 处理权限和产品信息
// 		if product, ok := products[item.GetProductID()]; ok {
// 			processPermissionAndProduct(iExtends, product, checkTimes, currentUser)
// 		}

// 		// 处理RPM信息和人员信息
// 		if integration.downloadMode {
// 			processRpmAndEvaluatorsInfo(iExtends, item, evaluatorsMap)
// 		}

// 		// 包装并添加到列表
// 		itemList = append(itemList, wrapItem(item, iExtends))
// 	}

// 	returnRes.List = itemList
// 	returnRes.Page.Total = total
// 	return returnRes, nil
// }
