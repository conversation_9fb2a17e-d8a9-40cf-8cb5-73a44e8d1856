/*
 * @Author: lwq <EMAIL>
 * @Date: 2025-07-08
 * @LastEditors: lwq <EMAIL>
 * @LastEditTime: 2025-07-24
 * @FilePath: /ctdy-guard/slam/task/integration/option/add.go
 * @Description: 集成操作
 * @Copyright (c) 2025 by 2022.kylinos.cn, All Rights Reserved.
 */
package option

import (
	"slam/global"
	"slam/model"
	"slam/slog"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// AddOption 添加软件包
type AddOption struct {
	BaseOption
	igBugModel   model.IntegrationRowsRepo
	igCveModel   model.IntegrationRowsRepo
	xflowModel   model.XfllowModelRepo
	igCountModel model.IntegrationCountRepo
}

func NewAddOption() *AddOption {
	return &AddOption{
		BaseOption: BaseOption{
			ID:   "add",
			Name: "添加软件包",
		},
		igBugModel:   model.NewIntegrationBugModel(),
		igCveModel:   model.NewIntegrationCveModel(),
		xflowModel:   model.NewXfllowModel(),
		igCountModel: model.NewIntegrationCountModel(),
	}
}

func (o *AddOption) Execute(c *gin.Context, opUser *OpUser, params any) (any, error) {
	bq := params.(*model.BugCveListReq)
	bugNum, cveNum := o.addBugsAndCves(bq)

	// 更新统计数据
	if bugNum > 0 || cveNum > 0 {
		umap := map[string]any{
			"total_count":     gorm.Expr("total_count + ?", bugNum+cveNum),
			"total_bug_count": gorm.Expr("total_bug_count + ?", bugNum),
			"total_cve_count": gorm.Expr("total_cve_count + ?", cveNum),
		}
		err := o.igCountModel.UpdateCountInfo(bq.IgId, global.INTEGRATION_STATUS_COMPARE, umap)
		if err != nil {
			slog.ErrorWithContext(c, err, umap)
		}
	}
	return nil, nil
}

func (i *AddOption) addBugsAndCves(bq *model.BugCveListReq) (int, int) {
	rows, bugs, cves, err := i.xflowModel.GetStockBugCveList(bq)
	if err != nil {
		return 0, 0
	}

	if len(rows) > 0 {
		//中心仓的先暂时不插入,后续处理
	}

	// 将数据插入表中
	if len(bugs) > 0 {
		err = i.igBugModel.BatchInsertIntegration(bugs)
		if err != nil {
			return 0, 0
		}
	}
	if len(cves) > 0 {
		err = i.igCveModel.BatchInsertIntegration(cves)
		if err != nil {
			return 0, 0
		}
	}
	return len(bugs), len(cves)
}
