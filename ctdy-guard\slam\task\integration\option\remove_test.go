package option

import (
	"slam/global"
	"slam/model"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"gorm.io/gorm"
)

const testUserEmail = "<EMAIL>"

func TestRemoveOptionExecute(t *testing.T) {
	NewRemoveOption()
	gin.SetMode(gin.TestMode)

	t.Run("successful bug removal", func(t *testing.T) {
		// Mocks
		mockCountModel := new(MockIntegrationCountRepo)
		mockBugModel := new(MockIntegrationRowsRepo)
		mockCveModel := new(MockIntegrationRowsRepo)

		// Test data
		req := &RemoveReq{
			IgId: 10,
			Ids:  []int{1, 2, 3},
			Type: model.IntegrationCheckTypeBug,
		}

		opUser := &OpUser{
			UserInfo: UserInfo{
				UserId:   1,
				UserName: "testuser",
				Email:    testUserEmail,
			},
		}

		// Mock expectations
		mockBugModel.On("RemoveRows", []int{1, 2, 3}).Return(int64(3), nil)
		mockCountModel.On("UpdateCountInfo", 10, global.INTEGRATION_STATUS_COMPARE, map[string]any{
			"total_bug_count": gorm.Expr("total_bug_count - ?", int64(3)),
			"total_count":     gorm.Expr("total_count - ?", int64(3)),
		}).Return(nil)

		// Create RemoveOption instance with mocks
		removeOption := &RemoveOption{
			BaseOption: BaseOption{
				ID:   "remove",
				Name: "删除软件包",
			},
			igCountModel: mockCountModel,
			igBugModel:   mockBugModel,
			igCveModel:   mockCveModel,
		}

		// Execute
		ctx, _ := gin.CreateTestContext(nil)
		result, err := removeOption.Execute(ctx, opUser, req)

		// Assertions
		assert.NoError(t, err)
		assert.Nil(t, result)
		mockBugModel.AssertExpectations(t)
		mockCountModel.AssertExpectations(t)
	})

	t.Run("successful cve removal", func(t *testing.T) {
		// Mocks
		mockCountModel := new(MockIntegrationCountRepo)
		mockBugModel := new(MockIntegrationRowsRepo)
		mockCveModel := new(MockIntegrationRowsRepo)

		// Test data
		req := &RemoveReq{
			IgId: 10,
			Ids:  []int{1, 2},
			Type: model.IntegrationCheckTypeCve,
		}

		opUser := &OpUser{
			UserInfo: UserInfo{
				UserId:   1,
				UserName: "testuser",
				Email:    testUserEmail,
			},
		}

		// Mock expectations
		mockCveModel.On("RemoveRows", []int{1, 2}).Return(int64(2), nil)
		mockCountModel.On("UpdateCountInfo", 10, global.INTEGRATION_STATUS_COMPARE, map[string]any{
			"total_cve_count": gorm.Expr("total_cve_count - ?", int64(2)),
			"total_count":     gorm.Expr("total_count - ?", int64(2)),
		}).Return(nil)

		// Create RemoveOption instance with mocks
		removeOption := &RemoveOption{
			BaseOption: BaseOption{
				ID:   "remove",
				Name: "删除软件包",
			},
			igCountModel: mockCountModel,
			igBugModel:   mockBugModel,
			igCveModel:   mockCveModel,
		}

		// Execute
		ctx, _ := gin.CreateTestContext(nil)
		result, err := removeOption.Execute(ctx, opUser, req)

		// Assertions
		assert.NoError(t, err)
		assert.Nil(t, result)
		mockCveModel.AssertExpectations(t)
		mockCountModel.AssertExpectations(t)
	})
}
