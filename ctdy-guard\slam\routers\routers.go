package routers

import (
	v1 "slam/api/v1"
	"slam/middleware"

	"github.com/gin-contrib/sessions"
	"github.com/gin-gonic/gin"
)

func InitRouter() *gin.Engine {
	r := gin.Default()

	r.Use(sessions.Sessions("slam_session", middleware.Store))
	r.Use(middleware.Cors)
	r.GET("/", func(c *gin.Context) {
		c.JSON(200, gin.H{
			"message": "welcome come to jianing demo project",
		})
	})

	//CodeInfo查询
	apiCodeInfo := r.Group("/codeinfo")
	apiCodeInfo.GET("/query", v1.HandleGetCodeInfoQuery)
	apiCodeInfo.GET("/gerritinfo", v1.HandleGetGerritInfo)

	//Product表
	apiProduct := r.Group("/product")
	apiProduct.GET("/list", v1.HandleGetProductOpenList)
	apiProduct.GET("/packs", v1.HandleGetProductPacks)
	apiProduct.GET("/latestbuild", v1.HandleGetProductPackLatestBuild)

	//CVE查询信息表
	apiCvePlat := r.Group("/cveplat")
	apiCvePlat.GET("/kojitask", v1.HandleGetCvePlatKojiTask)

	//vendor模块接口
	apiVendor := r.Group("/vendor")
	apiVendor.Use(middleware.MidOidc())
	vendorCon := &v1.VendorController{}
	apiVendor.GET("/list", vendorCon.HandleGetAllVendor)
	apiVendor.GET("/frontlist", vendorCon.HandleGetVendorList)
	apiVendor.POST("/add", vendorCon.HandlePostVendorAdd)
	apiVendor.GET("/cernamelist", vendorCon.HandleGetCernamelist)
	apiVendor.GET("/cer_download", vendorCon.HandleGetVendorCerts)
	apiVendor.GET("/release_search", vendorCon.HandleGetReleaseListByKeyword)
	apiVendor.GET("/casc_list", vendorCon.HandleGetVendorCasc)

	apiVendor.GET("/releaselist", vendorCon.HandleGetVendorReleaseList)
	apiVendor.GET("/releasedetail/:id", vendorCon.HandleGetVendorReleaseDetail)

	//架构模块接口
	apiFramework := r.Group("/framework")
	apiFramework.GET("/list", v1.HandleGetFramework)
	apiFramework.GET("/sync", v1.HandleSyncFramework)

	//认证回调函数
	r.GET("/oidc/callback", middleware.GetCallBack())

	//license模块
	apiLicense := r.Group("/license")
	apiLicense.Use(middleware.MidOidc())
	apiLicense.GET("/list", v1.HandleGetLicense)
	apiLicense.GET("/info", v1.HandleGetLicenseInfo)

	apiTest := r.Group("/test")
	//apiTest.Use(middleware.MidOidc())
	apiTest.GET("/", v1.GetTest)
	apiTest.POST("/", v1.PostTest)
	apiTest.GET("/gettagsarray", v1.HandleGetTagsArray)
	apiTest.GET("/lectsync", v1.HandleLectoInfoSync)
	apiTest.GET("/tagsync", v1.HandleGetTagListSync)
	apiTest.GET("/recycl", v1.HandleGetTempFilesRecycl)
	apiTest.GET("/buildfailedcheck", v1.HandleGetFailedBuildCheck)
	apiTest.GET("/cleargmap", v1.HandleGetClearGlobalMap)
	apiTest.GET("/initcert", v1.HandleGetInitCert)

	//Codebase
	apiCodeBase := r.Group("/codebase")
	apiCodeBase.Use(middleware.MidOidc())
	apiCodeBase.GET("/mnglist", v1.HandleGetCodeBaseMngList)         //源码仓管理流程列表
	apiCodeBase.POST("/add", v1.HandlePostCodeBaseAdd)               //源码仓新增申请
	apiCodeBase.GET("/mngdetail/:id", v1.HandleGetCodeBaseMngDetail) //源码仓管理流程详细信息
	apiCodeBase.GET("/evaluatelist", v1.HandleGetCodeBaseMngEvaluateList)
	apiCodeBase.GET("/withdraw", v1.HandleCodeBaseWithDraw)
	apiCodeBase.GET("/branchlist", v1.HandleGetBranchList)
	apiCodeBase.GET("/checkbranch", v1.HandleCheckBranch)              //查看分支是否存在
	apiCodeBase.GET("/cibuildinfo", v1.HandleGetPkgDefaultCIBuildInfo) // 源码仓根据产品和分支获取默认Koji平台和架构信息

	//Products表(内部使用)
	apiProducts := r.Group("/products")
	apiProducts.Use(middleware.MidOidc())
	apiProducts.GET("/detail/:id", v1.HandleGetProductListId)
	apiProducts.GET("/sumlist", v1.HandleGetProductSummaryList) //产品概要信息列表，用于产品信息展示页面
	apiProducts.GET("/mnglist", v1.HandleGetProductMngList)     //产品流程信息列表，用于产品管理页面流程展示
	apiProducts.POST("/add", v1.HandlePostProductAdd)
	apiProducts.POST("/update", v1.HandlePostProductUpdate)
	apiProducts.GET("/mngdetail/:id", v1.HandleGetProductMngDetail) //产品流程详细信息，用于产品管理详细页面
	apiProducts.GET("/evaluatelist", v1.HandleGetProductMngEvaluateList)
	apiProducts.GET("/withdraw", v1.HandleProductWithDraw)

	//tag模块相关接口
	apiTag := r.Group("/tag")
	apiTag.Use(middleware.MidOidc())
	apiTag.GET("/list", v1.HandleGetTagList)
	apiTag.GET("/mnglist", v1.HandleGetTagMngList)
	apiTag.POST("/add", v1.HandlePostTagAdd)
	apiTag.GET("/mngdetail/:id", v1.HandleGetTagMngDetail)
	apiTag.GET("/evaluatelist", v1.HandleGetTagMngEvaluateList)
	apiTag.GET("/sync", v1.HandleGetTagListSync)
	apiTag.GET("/withdraw", v1.HandleTagWithDraw)

	//个人中心相关接口
	apiMy := r.Group("/my")
	apiMy.Use(middleware.MidOidc())
	myCon := &v1.MyController{}
	apiMy.GET("/stock", myCon.HandleGetMyStock)
	apiMy.GET("/lectotype", myCon.HandleGetMyLectotype)
	apiMy.GET("/lectobiz", myCon.HandleGetMyLectoBiz)
	apiMy.GET("/center_stock", myCon.HandleGetMySCenter)
	apiMy.GET("/biz_stock", myCon.HandleGetMyStockBiz)
	apiMy.GET("/tag_stock", myCon.HandleGetMySTag)
	apiMy.GET("/product", myCon.HandleGetMyProduct)
	apiMy.GET("/tag", myCon.HandleGetMyTag)
	apiMy.GET("/codebase", myCon.HandleGetMyCodeBase)

	//订阅相关接口
	apiSubs := r.Group("/subs")
	apiSubs.Use(middleware.MidOidc())
	apiSubs.GET("/list", v1.HandleGetSubsList)
	apiSubs.GET("/datail", v1.HandleGetSubsDetail)
	apiSubs.POST("/update", v1.HandlePostSubsUpdate)
	apiSubs.DELETE("/delete", v1.HandleSubsDelete)

	//BCT表
	apiBct := r.Group("/bct")
	apiBct.Use(middleware.MidOidc())
	apiBct.GET("/list_old", v1.HandleGetBctOld)
	apiBct.GET("/list", v1.HandleGetBct)
	apiBct.GET("/sync", v1.HandleGetBctSync)
	apiBct.GET("/packinfo", v1.HandleGetBctPackInfo)

	//User表
	apiUser := r.Group("/user")
	apiUser.Use(middleware.MidOidc())
	apiUser.GET("/me", v1.HandleGetUserMe)
	apiUser.GET("/power_menu", v1.HandleGetUserPowerMenu)
	apiUser.GET("/list", v1.HandleGetUserList)

	//Common接口
	apiCommon := r.Group("/common")
	apiCommon.Use(middleware.MidOidc())
	commonCon := &v1.CommonController{}
	apiCommon.POST("/file_upload", commonCon.HandlePostFileUpload)
	apiCommon.GET("/file_download", commonCon.HandleGetFileDownload)
	apiCommon.GET("/zantao_info", commonCon.HandleGetZentao)
	apiCommon.GET("/zantao_url", commonCon.HandleGetZentaoUrl)
	apiCommon.GET("/prod_framework", commonCon.HandleGetProdFramework)
	apiCommon.GET("/searchpack", commonCon.HandleGetPackage)
	apiCommon.GET("/koji_plat", commonCon.HandleGetKojiPlat)
	apiCommon.GET("/helptext", commonCon.HandleGetHelpText)
	apiCommon.GET("/verify_report", commonCon.HandleGetVerifyReport)
	apiCommon.GET("/biz_form", commonCon.HandleGetBizForm)
	apiCommon.GET("/biz_type", commonCon.HandleGetBizType)

	//产品模块接口
	apiProd := r.Group("/prod")
	apiProd.Use(middleware.MidOidc())
	apiProd.GET("/list", v1.HandleGetProd)
	apiProd.GET("/related_tag", v1.HandleGetRelatedTag)
	apiProd.GET("/package", v1.HandleGetProductPackageList)
	apiProd.GET("/prod_tag", v1.HandleProductTagInfo)
	apiProd.GET("/type", v1.HandleGetProductTypeList)

	//入库申请模块接口
	apiStock := r.Group("/stock")
	apiStock.Use(middleware.MidOidc())
	apiStock.GET("/koji_info", v1.HandleGetKojiList)
	apiStock.GET("/gerrit_info", v1.HandleGetGerritList)
	apiStock.GET("/source_pack", v1.HandleGetSourcePack)
	apiStock.POST("/add", v1.HandlePostStockAdd)
	apiStock.GET("/appendix_download", v1.HandleGetAppendix)
	apiStock.GET("/withdraw", v1.HandleStockWithDraw)
	apiStock.GET("/detail", v1.HandleGetStockDetail)
	apiStock.GET("/history_koji", v1.HandleGetKojiHistory)
	apiStock.GET("/list", v1.HandleGetStockList)
	apiStock.GET("/evaluatelist", v1.HandleGetStockEvaluatelist)
	apiStock.GET("/retry", v1.HandleGetStockRetry)
	apiStock.GET("/rebuild", v1.HandleGetStockRebuild)
	apiStock.POST("/edit", v1.HandlePostStockEdit)

	// 中心仓入库与文件入库
	stockCenterCon := &v1.StockCenterController{}
	apiStock.POST("/center_add", stockCenterCon.HandleStockCenterAdd)
	apiStock.GET("/center_detail", stockCenterCon.HandleStockCenterDetail)
	apiStock.GET("/center_list", stockCenterCon.HandleGetCenterList)
	apiStock.GET("/center_evaluatelist", stockCenterCon.HandleGetCenterEvaluateList)
	apiStock.POST("/batch_fileupload", stockCenterCon.HandleStockBatchUpload)
	apiStock.GET("/batch_template", stockCenterCon.DownlowadTemp)
	apiStock.POST("/batch_add", stockCenterCon.BatchAdd)
	apiStock.GET("/batch_detail", stockCenterCon.HandleGetBatchDetail)
	apiStock.GET("/center_appendix", stockCenterCon.HandleGetSCAppendix)
	apiStock.GET("/center_withdraw", stockCenterCon.HandleSCWithDraw)
	apiStock.GET("/center_retry", stockCenterCon.HandleSCRetry)
	apiStock.POST("/center_stock_verify", stockCenterCon.HandleVerifySCenterPack)
	apiStock.GET("/center_rebuild", stockCenterCon.HandleGetSCRebuild)
	//商业软件入库
	apiStock.POST("/biz_add", stockCenterCon.HandleStockBizAdd)
	apiStock.GET("/biz_list", stockCenterCon.HandleGetBizList)
	apiStock.GET("/biz_detail", stockCenterCon.HandleStockCenterDetail)
	apiStock.GET("/biz_evaluatelist", stockCenterCon.HandleGetBizEvaluateList)
	apiStock.GET("/biz_withdraw", stockCenterCon.HandleSCWithDraw)
	apiStock.GET("/biz_retry", stockCenterCon.HandleSCRetry)

	// Tag增减模块相关接口
	stockTagCon := &v1.StockTagController{}
	apiStock.POST("/tag_add", stockTagCon.HandleStockTagAdd)
	apiStock.GET("/tag_detail", stockTagCon.HandleStockTagDetail)
	apiStock.GET("/tag_list", stockTagCon.HandleGetStockTagList)
	apiStock.GET("/tag_evaluatelist", stockTagCon.HandleGetSTagEvaluatSeList)
	apiStock.GET("/tag_appendix", stockTagCon.HandleGetSTagAppendix)
	apiStock.GET("/search_tag", stockTagCon.HandleSearchTagList)
	apiStock.GET("/tag_withdraw", stockTagCon.HandleSTagWithDraw)
	apiStock.POST("/tag_edit", stockTagCon.HandleStockTagEdit)

	//入库软件包视图查询接口
	apiPackage := r.Group("/package")
	apiPackage.Use(middleware.MidOidc())
	apiPackage.GET("/list", v1.HandleGetPackageList)

	// 检测结果相关接口
	apiHook := r.Group("/hook")
	apiHook.Use(middleware.MidOidc())
	apiHook.GET("/history", v1.HandleGetHistory)
	apiHook.POST("/hookRetry", v1.RetryHook)
	apiHook.GET("/hookResult", v1.GetHookResult)
	apiHook.POST("/ChangeXflowHookRel", v1.ChangeHookRelation)
	apiHook.GET("/blacklist", v1.GetBlackList)
	apiHook.POST("/addBlacklist", v1.AddBlackList)
	apiHook.DELETE("/delBlackList", v1.DelBlackList)
	apiHook.GET("/retryAll", v1.HandleRetryHookAll)
	apiHook.POST("/remark", v1.HandleAddHookRemark)
	apiHook.GET("/fortifyDownload", v1.HandleFortifyRepoDownload)
	apiHook.GET("/xflowHookRel", v1.HandleHookXflowHookRelList)
	apiHook.GET("/compatibility/:id/:repo", v1.HandleCompatibilityRepoDownload)
	apiHook.GET("/license/:id/:repo", v1.HandleLicenseRepoDownload)

	//评估与审核模块相关接口
	apiEvaluate := r.Group("/evaluate")
	apiEvaluate.Use(middleware.MidOidc())
	apiEvaluate.GET("/detail", v1.HandleGetEvaluateList)
	apiEvaluate.POST("/evaluate", v1.HandlePostEvaluate)
	apiEvaluate.POST("/review", v1.HandlePostReview)
	apiEvaluate.GET("/evaluator", v1.HandleGetEvaluator)

	//Lectotype模块相关接口
	apiLectotype := r.Group("/lectotype")
	apiLectotype.Use(middleware.MidOidc())
	lectotypeCenterCon := &v1.LectotypeController{}
	apiLectotype.POST("/batchadd", lectotypeCenterCon.HandlePostLectBatchadd)
	apiLectotype.POST("/urladd", lectotypeCenterCon.HandlePostLectUrladd)
	apiLectotype.GET("/previewurl", lectotypeCenterCon.HandleGetLectPreviewurl)
	apiLectotype.GET("/withdraw", lectotypeCenterCon.HandleGetLectWithdraw)
	apiLectotype.GET("/singledetail", lectotypeCenterCon.HandleGetLectSingledetail)
	apiLectotype.GET("/batchdetail", lectotypeCenterCon.HandleGetLectBatchdetail)
	apiLectotype.GET("/detaillist", lectotypeCenterCon.HandleGetLectDetailList)
	apiLectotype.GET("/urldetail", lectotypeCenterCon.HandleGetLectUrldetail)
	apiLectotype.GET("/list", lectotypeCenterCon.HandleGetLectList)
	apiLectotype.GET("/evaluatelist", lectotypeCenterCon.HandleGetLectEvaluatelist)
	apiLectotype.GET("/releaselist", lectotypeCenterCon.HandleGetLectReleaselist)
	apiLectotype.GET("/vendorlist", lectotypeCenterCon.HandleGetLectVendorlist)
	// 类型选型列表
	apiLectotype.GET("/categorylist", lectotypeCenterCon.HandleGetLectCategorylist)
	apiLectotype.GET("/hashsum", lectotypeCenterCon.HandleGetLectHashsum)
	apiLectotype.GET("/download", lectotypeCenterCon.HandleGetLectoAppendix)
	apiLectotype.GET("/packlist", lectotypeCenterCon.HandleSearchPacks)
	apiLectotype.GET("/check", lectotypeCenterCon.HandleGetCheckadd)
	apiLectotype.POST("/filetrans", lectotypeCenterCon.HandlePostLectBatchFile)
	apiLectotype.GET("/sourcelist", lectotypeCenterCon.HandleGetLectoSourceList)
	apiLectotype.GET("/sourcedetail", lectotypeCenterCon.HandleGetLectoSourceDetail)
	apiLectotype.GET("/license", lectotypeCenterCon.HandleGetLectoLicense)
	apiLectotype.GET("/prodrealease", lectotypeCenterCon.HandleGetLectoProdRealease)
	apiLectotype.GET("/sync", v1.HandleLectoInfoSync)
	//商业软件选型
	apiLectotype.POST("/bizadd", v1.HandlePostLectBizAdd)
	apiLectotype.GET("/bizlist", v1.HandleGetLectBizList)
	apiLectotype.GET("/bizdetail", v1.HandleGetLectBizDetail)
	apiLectotype.GET("/bizevaluatelist", v1.HandleGetLectBizEvaluateList)
	apiLectotype.GET("/bizwithdraw", v1.HandleLectBizWithDraw)

	//信息中心
	apiSystem := r.Group("/system")
	apiSystem.Use(middleware.MidOidc())
	systemCenterCon := &v1.SystemController{}
	apiSystem.GET("/export_bct", systemCenterCon.HandleExportBCT)
	apiSystem.GET("/export_license", systemCenterCon.HandleExportLicense)

	apiManage := r.Group("/manage")
	apiManage.Use(middleware.MidOidc())
	manageCenterCon := &v1.ManageController{}
	apiManage.GET("/transfercveinfo", manageCenterCon.HandleTransCVEInfo)
	apiManage.GET("/reviewcveinfo", manageCenterCon.HandleReviewCVEInfo)
	apiManage.GET("/syncBug", manageCenterCon.HandleSyncBugHistory)

	//debtotype 模块相关接口
	debtotype := r.Group("/debtotype")
	debtotype.Use(middleware.MidOidc())
	debtotypeCenterCon := &v1.DebtotypeController{}
	debtotype.GET("/list", debtotypeCenterCon.HandleGetDebtotypeList)
	debtotype.GET("/packlist", debtotypeCenterCon.HandleSearchPacks)

	// 诊脉平台CVE查询接口调用
	zhmCve := r.Group("/zhmcheck")
	zhmCve.GET("/cve", v1.HandleGetZhmCveInfo)
	zhmCve.POST("/cve", v1.HandlePostZhmCveInfo)
	zhmCve.GET("/cve_update", v1.HandlePostZhmCveUpdate)

	// 集成单相关接口
	integration := r.Group("/integration")
	// integration.Use(middleware.MidOidc())
	integrationCenter := v1.NewIntegrationController()
	integration.GET("/list", integrationCenter.HandleIntegrationList)
	integration.GET("/detail/:igid", integrationCenter.HandleIntegrationDetail)
	integration.GET("/list/bug/:igid", integrationCenter.HandleIntegrationBugList)
	integration.GET("/list/cve/:igid", integrationCenter.HandleIntegrationCveList)
	integration.GET("/download/:igid", integrationCenter.HandleDownloadIntegration)
	integration.GET("/check/logs", integrationCenter.HandleCheckLogs)
	integration.POST("/create", integrationCenter.HandleCreateIntegration)
	integration.POST("/edit", integrationCenter.HandleEditIntegration)
	integration.GET("/packs", integrationCenter.HandlePackageList)
	integration.POST("/add", integrationCenter.HandleAddPackage)
	integration.POST("/remove", integrationCenter.HandleRemovePackage)
	integration.POST("/review", integrationCenter.HandleReviewIntegration)
	integration.GET("/replenish/:igid", integrationCenter.HandleReplenishPackages)
	integration.POST("/replenish", integrationCenter.HandleReplenishData)
	integration.POST("/rotate/:igid", integrationCenter.HandleRotateIntegration)
	integration.POST("/check", integrationCenter.HandleCheckIntegration)
	integration.POST("/recheck", integrationCenter.HandleRecheckIntegration)
	integration.POST("/test", integrationCenter.HandleAddTestResult)
	integration.POST("/lock", integrationCenter.HandleLockIntegration)
	integration.POST("/flag", integrationCenter.HandleFlagIntegration)
	integration.POST("/confirm/:igid", integrationCenter.HandleConfirmIntegration)

	zentao := r.Group("/zentao")
	integration.Use(middleware.MidOidc())
	zentao.GET("/task/status", v1.HandleVerifyZentaoStatus)
	zentao.GET("/task/info", v1.HandleGetZentaoTaskInfo)

	return r
}
