/*
 * @Author: lwq <EMAIL>
 * @Date: 2025-07-08
 * @LastEditors: lwq <EMAIL>
 * @LastEditTime: 2025-07-30
 * @FilePath: /ctdy-guard/slam/task/integration/option/base_test.go
 * @Description: 集成操作
 * @Copyright (c) 2025 by 2022.kylinos.cn, All Rights Reserved.
 */
package option

import (
	"testing"
)

func TestGetOptions(t *testing.T) {
	GetOptions()
}

func TestBaseOption(t *testing.T) {
	op := BaseOption{
		ID:   "base",
		Name: "基础操作",
	}
	t.Log(op.GetID(), op.GetName())
}
