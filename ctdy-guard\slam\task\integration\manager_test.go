package integration

import (
	"slam/global"
	"slam/model"
	"slam/task/integration/node"
	"slam/task/integration/option"
	"slam/task/integration/role"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
)

func TestProcessManagerExecuteOptionCreate(t *testing.T) {
	gin.SetMode(gin.TestMode)

	// Setup mocks
	mockOption := new(MockOption)
	mockIntegrationModel := new(MockIntegrationModel)

	// Create ProcessManager with mocks
	pm := &ProcessManager{
		igModel: mockIntegrationModel,
		nodes:   node.GetNodes(),
		roles:   role.GetRoles(),
		options: map[int]option.OptionInterface{
			option.CreateOptionID: mockOption,
		},
	}

	t.Run("successful create execution", func(t *testing.T) {
		// Test data
		userOp := &Option{
			IgID:     0, // 创建时不需要ID
			NodeID:   0, // 会被覆盖为 global.INTEGRATION_STATUS_COMPARE
			OptionID: option.CreateOptionID,
		}

		params := "test params"

		// Mock expectations
		mockOption.On("Execute", mock.Anything, mock.Anything, params).Return("result", nil)
		mockOption.On("GetName").Return("创建集成单")

		// Mock context with user info
		ctx := getContext(ReposManagerRoleId)

		// Execute
		result, err := pm.ExecuteOption(ctx, userOp, params)

		// Assertions
		assert.NoError(t, err)
		assert.Equal(t, "result", result)
		assert.Equal(t, global.INTEGRATION_STATUS_COMPARE, userOp.NodeID)
		mockOption.AssertExpectations(t)
	})
}

func TestProcessManagerExecuteOptionWithIntegration(t *testing.T) {
	gin.SetMode(gin.TestMode)
	params := "test params"

	t.Run("successful execution with integration", func(t *testing.T) {
		// Test data
		userOp := &Option{
			IgID:     10,
			NodeID:   0, // 会从integration获取
			OptionID: option.UpdateOptionID,
		}

		integration := &model.Integration{
			Id:         10,
			Status:     1,
			LockStatus: global.INTEGRATION_UNLOCK,
		}
		// Setup mocks
		mockOption := new(MockOption)
		mockIntegrationModel := new(MockIntegrationModel)

		// Create ProcessManager with mocks
		pm := &ProcessManager{
			igModel: mockIntegrationModel,
			nodes:   node.GetNodes(),
			roles:   role.GetRoles(),
			options: map[int]option.OptionInterface{
				option.UpdateOptionID: mockOption,
			},
		}

		// Mock expectations
		mockIntegrationModel.On("GetIntegrationById", 10).Return(integration, nil)
		mockOption.On("Execute", mock.Anything, mock.Anything, params).Return("result", nil)
		mockOption.On("GetName").Return("更新集成单")

		// Mock context with user info
		ctx := getContext(ReposManagerRoleId)
		// Execute
		result, err := pm.ExecuteOption(ctx, userOp, params)

		// Assertions
		assert.NoError(t, err)
		assert.Equal(t, "result", result)
		assert.Equal(t, 1, userOp.NodeID) // Should match integration status
		mockIntegrationModel.AssertExpectations(t)
		mockOption.AssertExpectations(t)
	})

	t.Run("locked integration non-admin access denied", func(t *testing.T) {
		// Test data
		userOp := &Option{
			IgID:     10,
			NodeID:   0,
			OptionID: option.UpdateOptionID,
		}

		integration := &model.Integration{
			Id:         10,
			Status:     1,
			LockStatus: global.INTEGRATION_LOCKED,
		}
		// Setup mocks
		mockOption := new(MockOption)
		mockIntegrationModel := new(MockIntegrationModel)

		// Create ProcessManager with mocks
		pm := &ProcessManager{
			igModel: mockIntegrationModel,
			nodes:   node.GetNodes(),
			roles:   role.GetRoles(),
			options: map[int]option.OptionInterface{
				option.UpdateOptionID: mockOption,
			},
		}

		// Mock expectations
		mockIntegrationModel.On("GetIntegrationById", 10).Return(integration, nil)

		// Mock context with non-admin user info
		ctx := getContext(TechnicalManagerRoleId)
		// Execute
		result, err := pm.ExecuteOption(ctx, userOp, "params")

		// Assertions
		assert.Error(t, err)
		assert.Nil(t, result)
		assert.Contains(t, err.Error(), "集成单已锁定，无法操作")
		mockIntegrationModel.AssertExpectations(t)
	})

	t.Run("locked integration admin access allowed", func(t *testing.T) {
		// Test data
		userOp := &Option{
			IgID:     10,
			NodeID:   0,
			OptionID: option.UpdateOptionID,
		}

		integration := &model.Integration{
			Id:         10,
			Status:     1,
			LockStatus: global.INTEGRATION_LOCKED,
		}
		// Setup mocks
		mockOption := new(MockOption)
		mockIntegrationModel := new(MockIntegrationModel)

		// Create ProcessManager with mocks
		pm := &ProcessManager{
			igModel: mockIntegrationModel,
			nodes:   node.GetNodes(),
			roles:   role.GetRoles(),
			options: map[int]option.OptionInterface{
				option.UpdateOptionID: mockOption,
			},
		}

		// Mock expectations
		mockIntegrationModel.On("GetIntegrationById", 10).Return(integration, nil)
		mockOption.On("Execute", mock.Anything, mock.Anything, params).Return("result", nil)
		mockOption.On("GetName").Return("更新集成单")

		// Mock context with admin user info
		ctx := getContext(ReposManagerRoleId)

		// Execute
		result, err := pm.ExecuteOption(ctx, userOp, params)

		// Assertions
		assert.NoError(t, err)
		assert.Equal(t, "result", result)
		mockIntegrationModel.AssertExpectations(t)
		mockOption.AssertExpectations(t)
	})
}

func TestProcessManagerCheckAccess(t *testing.T) {
	pm := NewProcessManager()

	t.Run("skip access check", func(t *testing.T) {
		user := &option.OpUser{
			UserInfo: option.UserInfo{
				UserId:   1,
				UserName: "testuser",
				RoleId:   role.RepoManagerRoleID,
			},
		}

		userOp := &Option{
			NodeID:   SkipCheckAccess, // -1 表示跳过检查
			OptionID: option.CreateOptionID,
		}

		err := pm.CheckAccess(user, userOp)
		assert.NoError(t, err)
	})

	t.Run("valid access for role and node", func(t *testing.T) {
		user := &option.OpUser{
			UserInfo: option.UserInfo{
				UserId:   1,
				UserName: "testuser",
				RoleId:   role.RepoManagerRoleID,
			},
		}

		userOp := &Option{
			NodeID:   global.INTEGRATION_STATUS_COMPARE,
			OptionID: option.CreateOptionID,
		}

		err := pm.CheckAccess(user, userOp)
		assert.NoError(t, err)
	})

	t.Run("invalid role", func(t *testing.T) {
		user := &option.OpUser{
			UserInfo: option.UserInfo{
				UserId:   1,
				UserName: "testuser",
				RoleId:   999, // 不存在的角色
			},
		}

		userOp := &Option{
			NodeID:   global.INTEGRATION_STATUS_COMPARE,
			OptionID: option.CreateOptionID,
		}

		err := pm.CheckAccess(user, userOp)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "角色 999 不存在")
	})

	t.Run("invalid option", func(t *testing.T) {
		user := &option.OpUser{
			UserInfo: option.UserInfo{
				UserId:   1,
				UserName: "testuser",
				RoleId:   role.RepoManagerRoleID,
			},
		}

		userOp := &Option{
			NodeID:   global.INTEGRATION_STATUS_COMPARE,
			OptionID: 999, // 不存在的操作
		}

		err := pm.CheckAccess(user, userOp)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "无效的操作")
	})

	t.Run("role without access to option", func(t *testing.T) {
		user := &option.OpUser{
			UserInfo: option.UserInfo{
				UserId:   1,
				UserName: "testuser",
				RoleId:   role.ArchitectRoleID,
			},
		}

		// 架构师角色无权执行加锁操作
		userOp := &Option{
			NodeID:   global.INTEGRATION_STATUS_COMPARE,
			OptionID: option.LockOptionID,
		}

		err := pm.CheckAccess(user, userOp)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "无权执行操作")
	})

	t.Run("invalid node", func(t *testing.T) {
		user := &option.OpUser{
			UserInfo: option.UserInfo{
				UserId:   1,
				UserName: "testuser",
				RoleId:   role.RepoManagerRoleID,
			},
		}

		userOp := &Option{
			NodeID:   999, // 不存在的节点
			OptionID: option.CreateOptionID,
		}

		err := pm.CheckAccess(user, userOp)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "无效的节点 999")
	})

	t.Run("node without access to option", func(t *testing.T) {
		user := &option.OpUser{
			UserInfo: option.UserInfo{
				UserId:   1,
				UserName: "testuser",
				RoleId:   role.RepoManagerRoleID,
			},
		}

		// 假设某个节点不允许执行创建操作
		userOp := &Option{
			NodeID:   global.INTEGRATION_STATUS_PUBLISHED, // 发布状态不允许创建
			OptionID: option.CreateOptionID,
		}

		err := pm.CheckAccess(user, userOp)
		assert.Error(t, err)
	})
}

func TestProcessManagerGetOpUser(t *testing.T) {
	gin.SetMode(gin.TestMode)

	pm := NewProcessManager()

	t.Run("successful user info retrieval", func(t *testing.T) {
		ctx := getContext(ReposManagerRoleId)
		user, err := pm.getOpUser(ctx)

		assert.NoError(t, err)
		assert.NotNil(t, user)
	})

	t.Run("failed user info retrieval", func(t *testing.T) {
		ctx, _ := gin.CreateTestContext(nil)
		// 不设置用户信息，模拟获取失败
		user, err := pm.getOpUser(ctx)

		assert.Error(t, err)
		assert.Nil(t, user)
		assert.Contains(t, err.Error(), "获取用户信息失败")
	})

	t.Run("different role mappings", func(t *testing.T) {
		testCases := []struct {
			sysRoleId      int
			expectedRoleId int
			roleName       string
		}{
			{ReposManagerRoleId, role.RepoManagerRoleID, "仓库管理员"},
			{ProjectManagerRoleId, role.ProjectManagerRoleID, "项目经理"},
			{TechnicalManagerRoleId, role.TechManagerRoleID, "技术经理"},
			{ArchitectureManagerRoleId, role.ArchitectRoleID, "架构负责人"},
		}

		for _, tc := range testCases {
			ctx := getContext(tc.sysRoleId)
			user, err := pm.getOpUser(ctx)

			assert.NoError(t, err)
			assert.NotNil(t, user)
			assert.Equal(t, tc.expectedRoleId, user.RoleId)
		}
	})
}

func TestProcessManagerCanDoExcute(t *testing.T) {
	pm := NewProcessManager()

	t.Run("no product users map", func(t *testing.T) {
		opUser := &option.OpUser{
			UserInfo: option.UserInfo{
				UserId: 1,
				RoleId: role.ArchitectRoleID,
			},
		}
		op := &Option{
			Pusers: nil,
		}
		err := pm.canDoExcute(opUser, op)
		assert.NoError(t, err)
	})

	t.Run("user matches product role", func(t *testing.T) {
		opUser := &option.OpUser{
			UserInfo: option.UserInfo{
				UserId: 1,
				RoleId: role.ArchitectRoleID,
			},
		}
		op := &Option{
			Pusers: PuserMap{
				role.ArchitectRoleID: 1,
			},
		}
		err := pm.canDoExcute(opUser, op)
		assert.NoError(t, err)
	})

	t.Run("user does not match product role", func(t *testing.T) {
		opUser := &option.OpUser{
			UserInfo: option.UserInfo{
				UserId: 2, // 用户ID不匹配
				RoleId: role.ArchitectRoleID,
			},
		}

		op := &Option{
			Pusers: PuserMap{
				role.ArchitectRoleID: 1,
			},
		}
		err := pm.canDoExcute(opUser, op)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "您不是产品相关角色，无法操作")
	})
}

func TestNewProcessManager(t *testing.T) {
	pm := NewProcessManager()

	assert.NotNil(t, pm)
	assert.NotNil(t, pm.igModel)
	assert.NotNil(t, pm.nodes)
	assert.NotNil(t, pm.roles)
	assert.NotNil(t, pm.options)
}
