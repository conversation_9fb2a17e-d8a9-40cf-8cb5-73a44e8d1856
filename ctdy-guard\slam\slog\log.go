package slog

import (
	"crypto/rand"
	"encoding/hex"
	"log"
	"os"

	"github.com/gin-gonic/gin"
)

var (
	F     *os.File
	Info  *log.Logger
	Debug *log.Logger
	Warn  *log.Logger
	Error *log.Logger
)

func init() {
	SetUp()
}

// 初始化
func SetUp() {
	filepathVal := getLogFileFullPath()
	// 打开文件
	F = openLogFile(filepathVal)

	Info = log.New(F, "INFO: ", log.Ldate|log.Ltime|log.Lshortfile)
	Warn = log.New(F, "WARNING: ", log.Ldate|log.Ltime|log.Lshortfile)
	Error = log.New(F, "ERROR: ", log.Ldate|log.Ltime|log.Lshortfile)
	Debug = log.New(F, "DEBUG: ", log.Ldate|log.Ltime|log.Lshortfile)
}

func InfoWithContext(c *gin.Context, v ...any) {
	Info.Println("[traceid:"+getTraceIdFromCtx(c)+"]", v)
}

func ErrorWithContext(c *gin.Context, v ...any) {
	Error.Println("[traceid:"+getTraceIdFromCtx(c)+"]", v)
}

func getTraceIdFromCtx(c *gin.Context) string {
	traceID, ex := c.Get("traceid")

	if ex {
		return traceID.(string)
	} else {
		return GenerateTraceID()
	}
}

// 应该是个utils方法，但是utils包含了log，导致循环依赖，所以先放在这里
func GenerateTraceID() string {
	b := make([]byte, 16)
	_, err := rand.Read(b)
	if err != nil {
		return ""
	}

	return hex.EncodeToString(b)
}
