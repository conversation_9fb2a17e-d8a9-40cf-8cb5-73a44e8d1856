/*
 * @Author: lwq <EMAIL>
 * @Date: 2025-04-30
 * @LastEditors: lwq <EMAIL>
 * @LastEditTime: 2025-07-31
 * @FilePath: /ctdy-guard/slam/model/integration_check.go
 * @Description: 集成单审核结果模型
 * @Copyright (c) 2025 by 2022.kylinos.cn, All Rights Reserved.
 */
package model

import (
	"time"

	"gorm.io/gorm"
)

// 审核状态
const (
	IntegrationCheckPass   int = 1
	IntegrationCheckUnPass int = 2

	IntegrationCheckTypeBug int = 1
	IntegrationCheckTypeCve int = 2
)

// 定义审核状态描述映射变量
var IntegrationCheckDescMap = map[int]string{
	IntegrationCheckPass:   "可发布",
	IntegrationCheckUnPass: "不可发布",
}

// 定义审核类型映射变量
var IntegrationCheckTypeMap = map[int]string{
	IntegrationCheckTypeBug: "Bug",
	IntegrationCheckTypeCve: "Cve",
}

// IntegrationCheck 集成单审核结果模型
type IntegrationCheck struct {
	Id            int       `gorm:"column:id;primaryKey;autoIncrement:true;type:serial4;not null" json:"id,omitempty" label:"序号，主键"`
	Identifier    int       `gorm:"column:identifier;not null;default:0;uniqueIndex:uidx_iiic" json:"identifier" label:"唯一标识(对应表ID)"`
	IntegrationId int       `gorm:"column:integration_id;not null;default:0;uniqueIndex:uidx_iiic" json:"integration_id" label:"集成单id"`
	IgStatus      int       `gorm:"column:ig_status;not null;default:0;uniqueIndex:uidx_iiic" json:"ig_status" label:"集成单状态"`
	CheckType     int       `gorm:"column:check_type;not null;default:0;uniqueIndex:uidx_iiic" json:"check_type" label:"审核类型 1 bug 2 cve"`
	CheckerID     int       `gorm:"column:checker_id;not null;default:0" json:"checker_id" label:"审核用户id"`
	CheckerName   string    `gorm:"column:checker_name;type:varchar(50);not null;default:0" json:"checker_name" label:"审核用户姓名"`
	CheckerEmail  string    `gorm:"column:checker_email;type:varchar(100);not null;default:''" json:"checker_email" label:"审核用户邮箱"`
	CheckResult   int       `gorm:"column:check_result;not null;default:0" json:"check_result" label:"审核结果 1 审核通过 2 审核不通过"`
	CheckAdvice   string    `gorm:"column:check_advice;not null;default:''" json:"check_advice" label:"审核意见"`
	CreatedAt     time.Time `gorm:"column:create_time;not null" json:"create_time,omitempty" label:"创建时间"`
	UpdatedAt     time.Time `gorm:"column:update_time;not null" json:"update_time,omitempty" label:"更新时间"`
}

// TableName 表名
func (*IntegrationCheck) TableName() string {
	return "slam_integration_check"
}

// integrationCheckModel 集成单审核模型
type integrationCheckModel struct {
	db *gorm.DB
}

// IntegrationCheckRepo 集成单审核接口
type IntegrationCheckRepo interface {
	/**
	 * @author: lwq <EMAIL>
	 * @description: CreateIntegrationCheck 创建集成单审核
	 * @param {[]*IntegrationCheck} integrationChecks
	 * @return {error}
	 */
	CreateIntegrationCheck(integrationChecks []*IntegrationCheck) error
	/**
	 * @author: lwq <EMAIL>
	 * @description: GetIntegrationCheckList 查询某个cve或者bug的审核信息列表
	 * @param {int} checkType 1 bug 2 cve
	 * @param {int} integrationId 集成单id
	 * @return {[]*IntegrationCheck, error}
	 */
	GetCheckList(id, checkType int) ([]*IntegrationCheck, error)

	/**
	 * @author: lwq <EMAIL>
	 * @description: GetCheckListByIgId 查询某个集成单的审核信息列表
	 * @param {int} integrationId 集成单id
	 * @param {int} checkType 1 bug 2 cve
	 * @return {*}
	 */
	GetCheckListByIgId(id, checkType int) ([]*IntegrationCheck, error)
}

// NewIntegrationCheckModel 初始化集成单审核模型
func NewIntegrationCheckModel() IntegrationCheckRepo {
	return &integrationCheckModel{
		db: GetDB(),
	}
}

// CreateIntegrationCheck 创建集成单审核
func (i *integrationCheckModel) CreateIntegrationCheck(integrationChecks []*IntegrationCheck) error {
	return i.db.Create(integrationChecks).Error
}

// GetCheckList 根据check_type和integrationId查询审核信息列表
func (i *integrationCheckModel) GetCheckList(id, checkType int) ([]*IntegrationCheck, error) {
	var integrationChecks []*IntegrationCheck
	err := i.db.
		Where("identifier = ? AND check_type = ?", id, checkType).
		Find(&integrationChecks).Error
	return integrationChecks, err
}

func (i *integrationCheckModel) GetCheckListByIgId(id, checkType int) ([]*IntegrationCheck, error) {
	var integrationChecks []*IntegrationCheck
	err := i.db.
		Where("integration_id = ? AND check_type = ?", id, checkType).
		Find(&integrationChecks).Error
	return integrationChecks, err
}
