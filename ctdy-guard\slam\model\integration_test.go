package model

// import (
// 	"regexp"
// 	"slam/global"
// 	"slam/utils"
// 	"testing"
// 	"time"

// 	"github.com/DATA-DOG/go-sqlmock"
// 	"github.com/stretchr/testify/assert"
// 	"gorm.io/gorm"
// )

// func TestCreateIntegration(t *testing.T) {
// 	assert := assert.New(t)

// 	// 设置测试数据
// 	testTime := time.Now()
// 	testIntegration := &Integration{
// 		ProductID:   []int{1, 2},
// 		CreatorId:   100,
// 		CreatorName: "测试用户",
// 		CreatedAt:   testTime,
// 		UpdatedAt:   testTime,
// 	}

// 	// 初始化测试数据库
// 	gormDB, tmock, err := initSqlmock()
// 	assert.NoError(err)

// 	// 设置SQL预期
// 	tmock.ExpectBegin()
// 	tmock.ExpectQuery(regexp.QuoteMeta(`INSERT INTO "slam_integration" (,"creator_id","creator_name","create_time","update_time","product_id") VALUES ($1,$2,$3,$4,$5,$6,$7) RETURNING "id","product_id"`)).
// 		WithArgs(
// 			testIntegration.CreatorId, testIntegration.CreatorName, testIntegration.CreatedAt,
// 			testIntegration.UpdatedAt, `[1,2]`).
// 		WillReturnRows(sqlmock.NewRows([]string{"id", "product_id"}).AddRow(1, `[1,2]`))
// 	tmock.ExpectCommit()

// 	// 执行测试
// 	model := &integrationModel{db: gormDB}
// 	id, err := model.CreateIntegration(testIntegration)

// 	// 验证结果
// 	assert.NoError(err)
// 	assert.Equal(1, id)
// 	assert.NoError(tmock.ExpectationsWereMet())
// }

// func TestGetIntegrationList(t *testing.T) {
// 	assert := assert.New(t)

// 	// 设置测试数据
// 	testTime := time.Now()
// 	expectedIntegrations := []*Integration{
// 		{
// 			Id:          1,
// 			CreatorId:   100,
// 			CreatorName: "测试用户",
// 			CreatedAt:   testTime,
// 			UpdatedAt:   testTime,
// 		},
// 	}

// 	// 初始化测试数据库
// 	gormDB, tmock, err := initSqlmock()
// 	assert.NoError(err)

// 	// 设置分页参数
// 	pager := utils.NewPager(1, 10)

// 	// 设置SQL预期
// 	tmock.ExpectQuery(regexp.QuoteMeta(`SELECT * FROM "slam_integration" LIMIT 10`)).
// 		WillReturnRows(
// 			sqlmock.NewRows([]string{
// 				"id", "product_id", "start_time", "end_time",
// 				"creator_id", "creator_name", "create_time", "update_time",
// 			}).
// 				AddRow(
// 					1, `[1,2]`, testTime, testTime.Add(24*time.Hour),
// 					100, "测试用户", testTime, testTime,
// 				))

// 	tmock.ExpectQuery(regexp.QuoteMeta(`SELECT count(*) FROM "slam_integration"`)).
// 		WillReturnRows(sqlmock.NewRows([]string{"count"}).AddRow(1))

// 	// 执行测试
// 	model := &integrationModel{db: gormDB}
// 	result, err := model.GetIntegrationList(pager)

// 	// 验证结果
// 	assert.NoError(err)
// 	assert.Equal(int64(1), result.Page.Total)
// 	assert.Equal(1, len(result.List.([]*Integration)))
// 	assert.Equal(expectedIntegrations[0].Id, result.List.([]*Integration)[0].Id)
// 	assert.Equal(expectedIntegrations[0].ProductID, result.List.([]*Integration)[0].ProductID)
// 	assert.NoError(tmock.ExpectationsWereMet())
// }

// func TestGetIntegrationById(t *testing.T) {
// 	assert := assert.New(t)

// 	// 设置测试数据
// 	testTime := time.Now()
// 	expectedIntegration := &Integration{
// 		Id:        1,
// 		ProductID: []int{1, 2},
// 	}

// 	// 初始化测试数据库
// 	gormDB, tmock, err := initSqlmock()
// 	assert.NoError(err)

// 	// 设置SQL预期
// 	tmock.ExpectQuery(regexp.QuoteMeta(`SELECT * FROM "slam_integration" WHERE id = $1 ORDER BY "slam_integration"."id" LIMIT 1`)).
// 		WithArgs(1).
// 		WillReturnRows(
// 			sqlmock.NewRows([]string{
// 				"id", "product_id", "start_time", "end_time",
// 				"creator_id", "creator_name", "create_time", "update_time",
// 			}).
// 				AddRow(
// 					1, `[1,2]`, testTime, testTime.Add(24*time.Hour),
// 					100, "测试用户", testTime, testTime,
// 				))

// 	// 执行测试
// 	model := &integrationModel{db: gormDB}
// 	result, err := model.GetIntegrationById(1)

// 	// 验证结果
// 	assert.NoError(err)
// 	assert.Equal(expectedIntegration.Id, result.Id)
// 	assert.Equal(expectedIntegration.ProductID, result.ProductID)
// 	assert.NoError(tmock.ExpectationsWereMet())
// }

// func TestGetBuildInfosByXflowIds(t *testing.T) {
// 	assert := assert.New(t)

// 	// 测试场景1：正常数据
// 	t.Run("success with data", func(t *testing.T) {
// 		// 初始化测试数据库
// 		gormDB, tmock, err := initSqlmock()
// 		assert.NoError(err)

// 		// 设置SQL预期
// 		tmock.ExpectQuery(regexp.QuoteMeta(`SELECT xflow_id, ip, src_name FROM "slam_kojilist" WHERE xflow_id IN ($1,$2) AND type = $3`)).
// 			WithArgs(1, 2, global.KOJI_TYPE_BUILD).
// 			WillReturnRows(
// 				sqlmock.NewRows([]string{"xflow_id", "ip", "src_name"}).
// 					AddRow(1, "***********", "test-pkg-1").
// 					AddRow(2, "***********", "test-pkg-2"))

// 		// 执行测试
// 		model := &xfllowModel{db: gormDB}
// 		result, err := model.GetBuildInfosByXflowIds([]int{1, 2})

// 		// 验证结果
// 		assert.NoError(err)
// 		assert.Equal(2, len(result))
// 		assert.Equal(1, result[0].XflowId)
// 		assert.Equal("***********", result[0].IpAddress)
// 		assert.Equal("test-pkg-1", result[0].SrcName)
// 		assert.NoError(tmock.ExpectationsWereMet())
// 	})

// 	// 测试场景2：数据库错误
// 	t.Run("database error", func(t *testing.T) {
// 		// 初始化测试数据库
// 		gormDB, tmock, err := initSqlmock()
// 		assert.NoError(err)

// 		// 设置SQL预期
// 		tmock.ExpectQuery(regexp.QuoteMeta(`SELECT xflow_id, ip, src_name FROM "slam_kojilist" WHERE xflow_id IN ($1) AND type = $2`)).
// 			WithArgs(1, global.KOJI_TYPE_BUILD).
// 			WillReturnError(gorm.ErrInvalidDB)

// 		// 执行测试
// 		model := &xfllowModel{db: gormDB}
// 		result, err := model.GetBuildInfosByXflowIds([]int{1})

// 		// 验证结果
// 		assert.Error(err)
// 		assert.Nil(result)
// 		assert.NoError(tmock.ExpectationsWereMet())
// 	})
// }

// func TestGetStockCenterPacksByIds(t *testing.T) {
// 	assert := assert.New(t)

// 	// 测试场景1：正常数据
// 	t.Run("success with data", func(t *testing.T) {
// 		// 初始化测试数据库
// 		gormDB, tmock, err := initSqlmock()
// 		assert.NoError(err)

// 		// 设置SQL预期
// 		tmock.ExpectQuery(regexp.QuoteMeta(`SELECT * FROM "slam_stockcenterlist" WHERE xflow_id in ($1,$2)`)).
// 			WithArgs(1, 2).
// 			WillReturnRows(
// 				sqlmock.NewRows([]string{
// 					"id", "xflow_id", "pack_name", "developer", "prod_release",
// 					"bizsoft_form", "bizsoft_type", "prod_id", "cmd_option", "kojis",
// 					"lecto_id", "create_time", "update_time",
// 				}).
// 					AddRow(
// 						nil, 1, "test-pack-1", "测试开发者1", "release-1",
// 						"form-1", "type-1", 1, `["option1","option2"]`, `[1,2]`,
// 						1001, time.Now(), time.Now(),
// 					).
// 					AddRow(
// 						nil, 2, "test-pack-2", "测试开发者2", "release-2",
// 						"form-2", "type-2", 2, `["option3","option4"]`, `[3,4]`,
// 						1002, time.Now(), time.Now(),
// 					))

// 		// 执行测试
// 		model := &xfllowModel{db: gormDB}
// 		result := model.GetStockCenterPacksByIds([]int{1, 2})

// 		// 验证结果
// 		expectedPacks := []StockCenterPackList{
// 			{
// 				Id:          nil,
// 				Xflow_id:    1,
// 				PackName:    "test-pack-1",
// 				Developer:   "测试开发者1",
// 				ProdRelease: "release-1",
// 				BizSoftForm: "form-1",
// 				BizSoftType: "type-1",
// 				ProdId:      1,
// 				CmdOption:   []string{"option1", "option2"},
// 				Kojis:       []int{1, 2},
// 				LectoId:     1001,
// 				CreatedAt:   time.Now(),
// 				UpdatedAt:   time.Now(),
// 			},
// 			{
// 				Id:          nil,
// 				Xflow_id:    2,
// 				PackName:    "test-pack-2",
// 				Developer:   "测试开发者2",
// 				ProdRelease: "release-2",
// 				BizSoftForm: "form-2",
// 				BizSoftType: "type-2",
// 				ProdId:      2,
// 				CmdOption:   []string{"option3", "option4"},
// 				Kojis:       []int{3, 4},
// 				LectoId:     1002,
// 				CreatedAt:   time.Now(),
// 				UpdatedAt:   time.Now(),
// 			},
// 		}
// 		for i, pack := range result {
// 			assert.Equal(expectedPacks[i].Id, pack.Id)
// 			assert.Equal(expectedPacks[i].Xflow_id, pack.Xflow_id)
// 		}
// 	})
// }

// func TestGetArchsByIds(t *testing.T) {
// 	assert := assert.New(t)

// 	// 测试场景1：正常数据s
// 	t.Run("success with data", func(t *testing.T) {
// 		// 初始化测试数据库
// 		gormDB, tmock, err := initSqlmock()
// 		assert.NoError(err)

// 		// 设置SQL预期
// 		tmock.ExpectQuery(regexp.QuoteMeta(`SELECT id, name FROM "slam_framework" WHERE id IN ($1,$2)`)).
// 			WithArgs(1, 2).
// 			WillReturnRows(
// 				sqlmock.NewRows([]string{"id", "name"}).
// 					AddRow(1, "x86_64").
// 					AddRow(2, "aarch64"))

// 		// 执行测试
// 		model := &xfllowModel{db: gormDB}
// 		result := model.GetArchsByIds([]int{1, 2})

// 		// 验证结果
// 		expectedMap := map[int]string{
// 			1: "x86_64",
// 			2: "aarch64",
// 		}
// 		assert.Equal(expectedMap, result)
// 		assert.NoError(tmock.ExpectationsWereMet())
// 	})

// 	// 测试场景2：空列表
// 	t.Run("empty list", func(t *testing.T) {
// 		// 初始化测试数据库
// 		gormDB, tmock, err := initSqlmock()
// 		assert.NoError(err)

// 		// 设置SQL预期
// 		tmock.ExpectQuery(regexp.QuoteMeta(`SELECT id, name FROM "slam_framework" WHERE id IN ($1)`)).
// 			WithArgs(1).
// 			WillReturnRows(sqlmock.NewRows([]string{"id", "name"}))

// 		// 执行测试
// 		model := &xfllowModel{db: gormDB}
// 		result := model.GetArchsByIds([]int{1})

// 		// 验证结果
// 		assert.Empty(result)
// 		assert.NoError(tmock.ExpectationsWereMet())
// 	})
// }

// func TestGetStockBugCveList(t *testing.T) {
// 	assert := assert.New(t)

// 	// 设置测试数据
// 	testTime := time.Now()
// 	bq := &StockListQuery{
// 		StartDate: testTime.AddDate(0, 0, -7).Format("2006-01-02 15:04:05"),
// 		EndDate:   testTime.Format("2006-01-02 15:04:05"),
// 	}

// 	// 测试场景1：正常数据
// 	t.Run("success with data", func(t *testing.T) {
// 		// 初始化测试数据库
// 		gormDB, tmock, err := initSqlmock()
// 		assert.NoError(err)

// 		// 设置SQL预期
// 		tmock.ExpectQuery(regexp.QuoteMeta(`SELECT slam_xflow.id as xflow_id, slam_xflow.user_id, "user".nickname, slam_xflow.create_time, COALESCE(slam_stocklist.bug_info, slam_stocktaglist.bug_info, '[]') as bug_info, COALESCE(slam_stocklist.cve_info, slam_stocktaglist.cve_info, '[]') as cve_info, COALESCE(slam_stockcenterlist.packs, '[]') as packs, slam_xflow.type, slam_stocklist.test_info FROM "slam_xflow" left join slam_stocklist on slam_stocklist.xflow_id = slam_xflow.id left join slam_stockcenterlist on slam_stockcenterlist.xflow_id = slam_xflow.id left join slam_stocktaglist ON slam_stocktaglist.xflow_id = slam_xflow.id left join "user" on "user".id = slam_xflow.user_id WHERE slam_xflow.create_time >= $1 AND slam_xflow.create_time <= $2 AND "slam_xflow"."status" = $3 AND slam_xflow."type" in ($4,$5,$6,$7,$8)`)).WithArgs(
// 			bq.StartDate, bq.EndDate, global.XFLOW_STATUS_FINISHED,
// 			global.XFLOW_TYPE_STOCK, global.XFLOW_TYPE_STOCK_CENTER,
// 			global.XFLOW_TYPE_STOCK_BIZ, global.XFLOW_TYPE_STOCK_TAG,
// 			global.XFLOW_TYPE_STOCK_UNTAG,
// 		).WillReturnRows(
// 			sqlmock.NewRows([]string{
// 				"id", "user_id", "nickname", "create_time", "type",
// 				"bug_info", "cve_info", "test_info",
// 			}).AddRow(
// 				1, 100, "测试用户", testTime, "stock",
// 				`[{"zid":"1001","bug_detail":"测试bug","replay_method":"测试复现步骤","repair_method":"测试修复方法","emergency":"high","srpm_relation":[{"package_srpm_name":"test-pkg","product_id":"1","framework":"x86_64"}]}]`,
// 				`[{"cve_id":"CVE-2023-1001","package_name":"test-pkg","product_id":"1","arch":"x86_64","op_fix_info":"测试修复信息"}]`,
// 				nil,
// 			))

// 		// 执行测试
// 		model := &xfllowModel{db: gormDB}
// 		bugs, cves, _, err := model.GetStockBugCveList(bq, 1)

// 		// 验证结果
// 		assert.NoError(err)
// 		assert.NotNil(bugs)
// 		assert.NotNil(cves)
// 		assert.Equal(1, len(bugs))
// 		assert.Equal(1, len(cves))

// 		// 验证 bug 数据
// 		assert.NoError(tmock.ExpectationsWereMet())
// 	})

// }
// func TestGetEvaluatorsByXflowIds(t *testing.T) {
// 	assert := assert.New(t)

// 	// 测试场景1：正常数据
// 	t.Run("success with data", func(t *testing.T) {
// 		// 初始化测试数据库
// 		gormDB, tmock, err := initSqlmock()
// 		assert.NoError(err)

// 		tmock.ExpectQuery((`SELECT e.id, e.xflow_id, e.evaluate_type, u.nickname, u.e_mail FROM slam_evaluatedetail as e left join "user" as u on u.id = e."userId" WHERE e.xflow_id in ($1)`)).
// 			WithArgs(1).WillReturnRows(
// 			sqlmock.NewRows(
// 				[]string{"id", "xflow_id", "evaluate_type", "nickname", "e_mail"}).
// 				AddRow(1, 1, "type1", "测试用户1", "<EMAIL>"),
// 		)

// 		// 执行测试
// 		model := &xfllowModel{db: gormDB}
// 		// 测试xflowids为空的情况
// 		model.GetEvaluatorsByXflowIds([]int{})
// 		model.GetEvaluatorsByXflowIds([]int{1})

// 		// 验证结果
// 		assert.NoError(err)
// 	})
// }
