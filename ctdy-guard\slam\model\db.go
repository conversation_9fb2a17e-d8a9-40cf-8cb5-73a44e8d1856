package model

import (
	"fmt"
	"os"
	"slam/slog"
	"slam/utils"
	"strings"
	"time"

	"gorm.io/driver/mysql"
	"gorm.io/driver/postgres"
	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

var (
	db *gorm.DB
)

func InitDb() *gorm.DB {
	var dialector gorm.Dialector
	switch strings.ToLower(utils.Type) {
	case "mysql":
		dns := fmt.Sprintf("%s:%s@tcp(%s:%s)/%s?charset=utf8&parseTime=True&loc=Local",
			utils.DataAk,
			utils.DataSk,
			utils.DBHost,
			utils.DBPort,
			utils.DBName)
		dialector = mysql.Open(dns)
	case "postgres":
		dns := fmt.Sprintf("host=%s user=%s password=%s dbname=%s port=%s sslmode=disable TimeZone=Asia/Shanghai",
			utils.DBHost,
			utils.DataAk,
			utils.DataSk,
			utils.DBName,
			utils.DBPort)
		dialector = postgres.Open(dns)
	default:
		dialector = sqlite.Open("gorm.db")
	}

	mydb, err := gorm.Open(dialector, &gorm.Config{
		Logger: logger.Default.LogMode(logger.Error), // 设置日志模式为静默
	})
	if err != nil {
		slog.Error.Println("连接数据库失败，请检查连接参数：", err)
		os.Exit(1)
	}
	db = mydb

	initTables()
	InitRepo(mydb)

	sqlDB, _ := db.DB()
	// SetMaxIdleConns 设置空闲连接池中连接的最大数量
	sqlDB.SetMaxIdleConns(10)
	// SetMaxOpenConns 设置打开数据库连接的最大数量。
	sqlDB.SetMaxOpenConns(100)
	// SetConnMaxLifetime 设置了连接可复用的最大时间。
	sqlDB.SetConnMaxLifetime(time.Hour)

	return mydb
}

func InitRepo(db *gorm.DB) {
	Lectotype = NewLectotypeModel(db)
	OIDC = NewOIDCModel(db)
	Koji = NewkojiModel(db)
}

func initTables() {
	// 数据表结构迁移
	// db.AutoMigrate(LectotypePackageList{})
	// db.AutoMigrate(EvaluateList{})
	// db.AutoMigrate(GerritList{})
	// db.AutoMigrate(KojiList{})
	// db.AutoMigrate(Framework{})
	db.AutoMigrate(StockList{})
	// db.AutoMigrate(StockContext{})
	// db.AutoMigrate(Xflow{})
	// db.AutoMigrate(TagList{})
	// db.AutoMigrate(TagMngList{})
	// db.AutoMigrate(TagMngContext{})
	// db.AutoMigrate(LectotypeList{})
	// db.AutoMigrate(LectotypeContext{})
	// db.AutoMigrate(VendorList{})
	// db.AutoMigrate(StockCenterList{})
	// db.AutoMigrate(StockCenterContext{})
	// db.AutoMigrate(StockCenterPackList{})
	// db.AutoMigrate(ProductList{})
	// db.AutoMigrate(ProductMngList{})
	// db.AutoMigrate(ProductMngContext{})
	// db.AutoMigrate(KojiPlatform{})
	// db.AutoMigrate(StockTagList{})
	// db.AutoMigrate(StockTagContext{})
	// db.AutoMigrate(StockTagPackageList{})
	// db.AutoMigrate(BctList{})
	// db.AutoMigrate(IsoList{})
	// db.AutoMigrate(HookTaskList{})
	// db.AutoMigrate(LicenseCompatible{})
	// db.AutoMigrate(PackageLicense{})
	// db.AutoMigrate(BlackList{})
	// db.AutoMigrate(XflowHookRelationList{})
	// db.AutoMigrate(HookItemInfoList{})
	// db.AutoMigrate(GerritBranchCfgList{})
	// db.AutoMigrate(GerritPackageCfgList{})
	// db.AutoMigrate(SubscribeList{})
	//db.AutoMigrate(ZhmCvePackList{})
	db.AutoMigrate(StockFeatureList{})

	//db.AutoMigrate(CodeBaseMngList{})
	//db.AutoMigrate(CodeBaseMngContext{})
	//db.AutoMigrate(CodeBaseMngRepoList{})
	//db.AutoMigrate(&LXUserInfo{})
	//db.AutoMigrate(&LicenseList{})
	//db.AutoMigrate(&LicenseTagList{})

	// db.AutoMigrate(&SubscribeRule{})
	// db.AutoMigrate(&TemplateInfo{})

	// // 集成单相关表
	// db.AutoMigrate(&Integration{})
	// db.AutoMigrate(&IntegrationBug{})
	// db.AutoMigrate(&IntegrationCve{})
	// db.AutoMigrate(&IntegrationCheck{})
	// db.AutoMigrate(&IntegrationRpmInfo{})
	// db.AutoMigrate(&IntegrationCount{})
	// db.AutoMigrate(&IntegrationReplenish{})
	// db.AutoMigrate(&IntegrationKoji{})
}

func GetDB() *gorm.DB {
	return db
}

func SetDB(dbSet *gorm.DB) {
	db = dbSet
	InitRepo(dbSet)
}

func CloseDb() {
	db := GetDB()
	sqlDB, err := db.DB()
	if err != nil {
		slog.Error.Printf("failed to get database object: %v", err)
	}
	err = sqlDB.Close()
	if err != nil {
		slog.Error.Printf("error closing database: %v\n", err)
	}
}
