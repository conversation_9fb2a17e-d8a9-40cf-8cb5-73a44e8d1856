/*
 * @Author: lwq <EMAIL>
 * @Date: 2025-07-08
 * @LastEditors: lwq <EMAIL>
 * @LastEditTime: 2025-07-29
 * @FilePath: /ctdy-guard/slam/task/integration/node/base.go
 * @Description: 节点接口定义 集合各个节点
 * @Copyright (c) 2025 by 2022.kylinos.cn, All Rights Reserved.
 */
package node

import (
	"slam/global"
	"slam/utils"
)

// NodeInterface 定义流程节点接口
type NodeInterface interface {
	/**
	 * @author: lwq <EMAIL>
	 * @description: GetID 获取节点ID
	 * @return {*}
	 */
	GetID() int

	/**
	 * @author: lwq <EMAIL>
	 * @description: GetName 获取节点名称
	 * @return {*}
	 */
	GetName() string

	/**
	 * @author: lwq <EMAIL>
	 * @description: CheckAccess 当前节点是否可执行操作
	 * @return {*}
	 */
	CheckAccess(optionID int) bool
}

// BaseNode 基础节点实现
type BaseNode struct {
	ID             int    // 节点ID 对应集成单各个状态值
	Name           string // 节点名称
	AllowedOptions []int  // 允许的操作列表
}

func (n *BaseNode) GetID() int {
	return n.ID
}

func (n *BaseNode) GetName() string {
	return n.Name
}

func (n *BaseNode) CheckAccess(optionID int) bool {
	return utils.ExistSliceInt(optionID, n.AllowedOptions)
}

/**
 * @author: lwq <EMAIL>
 * @description: GetNodes 初始化节点
 * @return {*}
 */
func GetNodes() map[int]NodeInterface {
	nodes := make(map[int]NodeInterface)
	nodes[global.INTEGRATION_STATUS_COMPARE] = NewNodeComparing()
	nodes[global.INTEGRATION_STATUS_CREATING] = NewNodeCreating()
	nodes[global.INTEGRATION_STATUS_CHECKING] = NewNodeChecking()
	nodes[global.INTEGRATION_STATUS_TESTING] = NewNodeTesting()
	nodes[global.INTEGRATION_STATUS_RECHECKING] = NewNodeRechecking()
	nodes[global.INTEGRATION_STATUS_RELEASING] = NewNodeReleasing()
	nodes[global.INTEGRATION_STATUS_PUBLISHED] = NewNodePublished()

	return nodes
}
