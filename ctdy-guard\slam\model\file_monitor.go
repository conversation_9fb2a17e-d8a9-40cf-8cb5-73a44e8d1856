/*
 * @Author: lwq <EMAIL>
 * @Date: 2025-03-20
 * @LastEditors: lwq <EMAIL>
<<<<<<< HEAD
 * @LastEditTime: 2025-04-25
=======
 * @LastEditTime: 2025-04-29
>>>>>>> origin/slam
 * @FilePath: /ctdy-guard/slam/model/file_monitor.go
 * @Description: 文件监控信息表
 * @Copyright (c) 2025 by 2022.kylinos.cn, All Rights Reserved.
 */
package model

import (
	"slam/utils"
	"time"

	"gorm.io/gorm"
)

// FileMonitor 文件监控信息表
type FileMonitor struct {
	Id              *int      `gorm:"column:id;primaryKey;autoIncrement:true;type:integer;not null" json:"id,string,omitempty" label:"序号，主键"`
	FilePath        string    `gorm:"column:file_path;type:text;not null;default:''" json:"file_path" label:"文件路径"`
	Developer       string    `gorm:"column:developer;type:varchar(100);not null;default:''" json:"developer" label:"开发者"`
	ProductRelease  string    `gorm:"column:product_release;type:varchar(100);not null;default:''" json:"product_release" label:"产品版本"`
	PackageName     string    `gorm:"column:package_name;type:varchar(255);not null;default:''" json:"package_name" label:"包名"`
	PackageFullName string    `gorm:"column:package_full_name;type:varchar(255);not null;default:''" json:"package_full_name" label:"包全名"`
	PackageVersion  string    `gorm:"column:package_version;type:varchar(255);not null;default:''" json:"package_version" label:"包版本信息"` //
	PackageRelease  string    `gorm:"column:package_release;type:varchar(255);not null;default:''" json:"package_release" label:"包发布版本"` //
	LastModified    string    `gorm:"column:last_modified;type:varchar(100);not null;default:''" json:"last_modified" label:"最后修改时间"`
	ETag            string    `gorm:"column:etag;type:varchar(100);not null;default:''" json:"etag" label:"ETag标识"`
	HasStocked      bool      `gorm:"column:has_stocked;type:bool;not null;default:false" json:"has_stocked" label:"是否入库"`
	XflowId         string    `gorm:"column:xflow_id;type:varchar(100);not null;default:''" json:"xflow_id" label:"ETag标识"`
	CreatedAt       time.Time `gorm:"column:create_time" json:"create_time,omitempty" label:"创建时间"`
	UpdatedAt       time.Time `gorm:"column:update_time" json:"update_time,omitempty" label:"更新时间"`
}

func (*FileMonitor) TableName() string {
	return "slam_file_monitor"
}

type fileMonitorModel struct {
	db *gorm.DB
}

type FileMonitorRepo interface {
	/**
	 * @author: lwq <EMAIL>
	 * @description: GetFileMonitorsByEtgs 根据ETag获取文件监控记录
	 * @param {[]string} etags
	 * @return {*}
	 */
	GetFileMonitorsByEtgs(etags []string) map[string]*FileMonitor
	/**
	 * @author: lwq <EMAIL>
	 * @description: CreateFileMonitor 创建文件监控记录
	 * @param {[]*FileMonitor} monitor
	 * @return {bool}
	 */
	CreateFileMonitor(monitor []*FileMonitor) bool

	/**
	 * @author: lwq <EMAIL>
	 * @description: GetNeedStockPacks
	 * @description: 分页查询需要入库的软件包
	 * @param {utils.Pager} pager
	 * @return {*}
	 */
	GetNeedStockPacks(pager utils.Pager) []*FileMonitor
	/**
	 * @author: lwq <EMAIL>
	 * @description:UpdateFileMonitor 更新文件监控记录
	 * @param {*FileMonitor} fm
	 * @return {bool}
	 */
	UpdateFileMonitor(fm *FileMonitor) bool

	/**
	 * @author: lwq <EMAIL>
	 * @description: CountStasticsDeveloper 按开发者统计今日数据
	 * @return {[]*DeveloperStastics}
	 */
	CountStasticsDeveloper() []*DeveloperStastics
}

/**
 * @author: lwq <EMAIL>
 * @description: 实例化文件监控记录
 * @param {*gorm.DB} db
 * @return {FileMonitorRepo}
 */
func NewFileMonitor() FileMonitorRepo {
	return &fileMonitorModel{
		db: GetDB(),
	}
}

/**
 * @author: lwq <EMAIL>
 * @description:GetFileMonitorsByEtgs 根据ETag获取文件监控记录
 * @param {[]string} etags
 * @return {map[string(Etag)]*FileMonitor}
 */
func (f *fileMonitorModel) GetFileMonitorsByEtgs(etags []string) map[string]*FileMonitor {
	var monitor []*FileMonitor
	err := f.db.Where("etag IN ?", etags).Find(&monitor).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil
		}
		return nil
	}

	monitorMap := make(map[string]*FileMonitor)
	for _, m := range monitor {
		monitorMap[m.ETag] = m
	}

	return monitorMap
}

/**
 * @author: lwq <EMAIL>
 * @description: CreateFileMonitor 创建文件监控记录
 * @param {[]*FileMonitor} monitor
 * @return {bool}
 */
func (f *fileMonitorModel) CreateFileMonitor(monitor []*FileMonitor) bool {
	err := f.db.Create(monitor).Error
	return err == nil
}

/**
 * @author: lwq <EMAIL>
 * @description: GetNeedStockPacks
 * @description: 分页查询需要入库的软件包
 * @param {utils.Pager} pager
 * @return {*}
 */
func (f *fileMonitorModel) GetNeedStockPacks(pager utils.Pager) []*FileMonitor {
	var monitor []*FileMonitor
	fm := (&FileMonitor{}).TableName()
	lp := (&LectotypePackageList{}).TableName()

	subQuery := f.db.Table(lp + " as lp").Select("1").
		Where("lp.pack_fullname = fm.package_full_name")

	err := f.db.Table(fm+" as fm").
		Where("has_stocked = ?", false).
		Where("NOT EXISTS (?)", subQuery).
		Order("id").Limit(pager.Size).Offset(pager.Offset).
		Find(&monitor).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil
		}
	}
	return monitor
}

/**
 * @author: lwq <EMAIL>
 * @description:UpdateFileMonitor 更新文件监控记录
 * @param {*FileMonitor} fm
 * @return {bool}
 */
func (f *fileMonitorModel) UpdateFileMonitor(fm *FileMonitor) bool {
	err := f.db.Model(&FileMonitor{}).Where("id = ?", fm.Id).Updates(fm).Error
	return err == nil
}

// DeveloperStastics 开发者统计数据结构
type DeveloperStastics struct {
	Developer      string `json:"developer" label:"开发者"`
	ProductRelease string `json:"product_release" label:"产品版本"`
	NewCount       int    `json:"new_count" label:"今日新增"`
	UpdCount       int    `json:"upd_count" label:"今日更新"`
	StockCount     int    `json:"stock_count" label:"今日入库"`
}

/**
 * @author: lwq <EMAIL>
 * @description: CountStasticsDeveloper 按开发者统计今日数据
 * @return {[]*DeveloperStastics}
 */
func (f *fileMonitorModel) CountStasticsDeveloper() []*DeveloperStastics {
	var result []*DeveloperStastics

	// 获取今天的开始时间和结束时间
	now := time.Now()
	todayStart := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, now.Location())
	todayEnd := todayStart.Add(24 * time.Hour)

	// 按开发者分组查询统计数据
	err := f.db.Model(&FileMonitor{}).
		Select(
			"developer,product_release,"+
				"COUNT(CASE WHEN create_time >= ? AND create_time < ? THEN 1 END) as new_count,"+
				"COUNT(CASE WHEN update_time >= ? AND update_time < ? AND create_time < ? THEN 1 END) as upd_count,"+
				"COUNT(CASE WHEN update_time >= ? AND update_time < ? AND has_stocked = true THEN 1 END) as stock_count",
			todayStart, todayEnd, todayStart, todayEnd, todayStart, todayStart, todayEnd,
		).
		Group("developer").Group("product_release").
		Having("COUNT(CASE WHEN create_time >= ? AND create_time < ? THEN 1 END) > 0 OR "+
			"COUNT(CASE WHEN update_time >= ? AND update_time < ? AND create_time < ? THEN 1 END) > 0 OR "+
			"COUNT(CASE WHEN update_time >= ? AND update_time < ? AND has_stocked = true THEN 1 END) > 0",
			todayStart, todayEnd, todayStart, todayEnd, todayStart, todayStart, todayEnd).
		Find(&result).Error

	if err != nil {
		return nil
	}

	return result
}
