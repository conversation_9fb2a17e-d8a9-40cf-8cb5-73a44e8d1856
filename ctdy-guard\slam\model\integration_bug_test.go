package model

import (
	"regexp"
	"slam/global"
	"slam/utils"
	"testing"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/stretchr/testify/assert"
)

func TestIntegrationBugModelBatchInsertIntegration(t *testing.T) {
	assert := assert.New(t)

	// 初始化测试数据库
	gormDB, tmock, err := initSqlmock()
	assert.NoError(err)

	model := &integrationBugModel{db: gormDB}

	t.Run("success case with one record", func(t *testing.T) {
		// 设置SQL预期
		tmock.ExpectBegin()
		tmock.ExpectQuery(regexp.QuoteMeta(`INSERT INTO "slam_integration_bug" ("integration_id","xflow_id","submiter_id","submiter_name","src_name","product_id","arch_info","task_type","test_info","zentao_no","bug_type","level","description","replay_method","repair_method","check_res","is_publish","source","is_del","create_time","update_time") VALUES ($1,$2,$3,$4,$5,$6,$7,$8,$9,$10,$11,$12,$13,$14,$15,$16,$17,$18,$19,$20,$21) RETURNING "id"`)).
			WithArgs(
				1, 101, 1001, "submiter1", "package1", 1, "x86_64", "bugfix", "manual", "BUG#123", "type1", "high", "desc1", "replay1", "repair1", 0, 1, 1, 0, sqlmock.AnyArg(), sqlmock.AnyArg()).
			WillReturnRows(sqlmock.NewRows([]string{"id"}).AddRow(1))
		tmock.ExpectCommit()

		// 执行测试
		bugs := []*IntegrationBug{
			{
				IntegrationId: 1,
				XflowId:       101,
				SubmiterId:    1001,
				SubmiterName:  "submiter1",
				SrcName:       "package1",
				ProductId:     1,
				ArchInfo:      "x86_64",
				TaskType:      "bugfix",
				TestInfo:      "manual",
				ZentaoNo:      "BUG#123",
				BugType:       "type1",
				Level:         "high",
				Describtion:   "desc1",
				ReplayMethod:  "replay1",
				RepairMethod:  "repair1",
			},
		}
		err := model.BatchInsertIntegration(bugs)

		// 验证结果
		assert.NoError(err)
		assert.NoError(tmock.ExpectationsWereMet())
	})

	t.Run("wrong type case", func(t *testing.T) {
		err := model.BatchInsertIntegration("not a slice")
		assert.Error(err)
		assert.Equal("rows类型断言失败", err.Error())
	})
}

func TestIntegrationBugModelGetIntegrationList(t *testing.T) {
	assert := assert.New(t)

	// 初始化测试数据库
	gormDB, tmock, err := initSqlmock() // 修复变量名
	assert.NoError(err)

	model := &integrationBugModel{db: gormDB}

	// 测试用例1: 基本查询
	t.Run("BasicQuery", func(t *testing.T) {
		req := &PacksListReq{
			IgId: 1,
		}
		pager := utils.Pager{
			Index: 1,
			Size:  10,
		}

		// 期望的计数查询 - 修复预期查询SQL，去掉表名的引号
		tmock.ExpectQuery(regexp.QuoteMeta(`SELECT count(*) FROM slam_integration_bug as ib LEFT JOIN slam_integration_rpm as ir ON ib.xflow_id = ir.xflow_id AND ib.src_name = ir.src_name AND ib.arch_info = ir.arch_info LEFT JOIN slam_stockcenterlist as cl ON ib.xflow_id = cl.xflow_id WHERE ib.integration_id = $1 AND ib.is_del = $2`)).
			WithArgs(1, 0).
			WillReturnRows(tmock.NewRows([]string{"count"}).AddRow(1))

		// 期望的SQL查询 - 修正为与实际查询匹配的SQL，并确保包含LIMIT和OFFSET
		tmock.ExpectQuery(regexp.QuoteMeta(`SELECT ib.*,COALESCE(cl.stock_type, '') as stock_type, json_agg(CASE WHEN ir.nvr IS NOT NULL THEN json_build_object('src_name', concat_ws('.', ir.nvr, ir.arch_info, 'rpm'), 'signd_sha256', ir.signd_sha256) ELSE NULL END) FILTER (WHERE ir.nvr IS NOT NULL) as rpms FROM slam_integration_bug as ib LEFT JOIN slam_integration_rpm as ir ON ib.xflow_id = ir.xflow_id AND ib.src_name = ir.src_name AND ib.arch_info = ir.arch_info LEFT JOIN slam_stockcenterlist as cl ON ib.xflow_id = cl.xflow_id WHERE ib.integration_id = $1 AND ib.is_del = $2 GROUP BY ib.id, ib.src_name, cl.stock_type ORDER BY ib.id desc LIMIT 10`)).
			WithArgs(1, 0).
			WillReturnRows(tmock.NewRows([]string{"id", "integration_id", "xflow_id", "src_name", "stock_type"}).
				AddRow(1, 1, 1001, "test-package", "stock"))

		result, total, err := model.GetIntegrationList(req, pager)

		assert.NoError(err)
		assert.Equal(int64(1), total) // 现在应该能正确获取总数
		assert.NotNil(result)
		assert.NoError(tmock.ExpectationsWereMet()) // 修复了变量名
	})
}

func TestIntegrationBugModelGetXflowIdsByIgid(t *testing.T) {
	assert := assert.New(t)

	// 初始化测试数据库
	gormDB, tmock, err := initSqlmock()
	assert.NoError(err)

	model := &integrationBugModel{db: gormDB}

	t.Run("GetXflowIdsByIgid_Success", func(t *testing.T) {
		// 设置SQL预期
		tmock.ExpectQuery(regexp.QuoteMeta(`SELECT DISTINCT "xflow_id" FROM "slam_integration_bug" WHERE integration_id = $1`)).
			WithArgs(1).
			WillReturnRows(sqlmock.NewRows([]string{"xflow_id"}).
				AddRow(1001).
				AddRow(1002).
				AddRow(1003))

		// 执行测试
		xflowIds, err := model.GetXflowIdsByIgid(1)

		// 验证结果
		assert.NoError(err)
		assert.Equal([]int{1001, 1002, 1003}, xflowIds)
		assert.NoError(tmock.ExpectationsWereMet())
	})

	t.Run("empty result case", func(t *testing.T) {
		// 设置SQL预期 - 无结果
		tmock.ExpectQuery(regexp.QuoteMeta(`SELECT DISTINCT "xflow_id" FROM "slam_integration_bug" WHERE integration_id = $1`)).
			WithArgs(2).
			WillReturnRows(sqlmock.NewRows([]string{"xflow_id"}))

		// 执行测试
		xflowIds, err := model.GetXflowIdsByIgid(2)

		// 验证结果
		assert.NoError(err)
		assert.Empty(xflowIds)
		assert.NoError(tmock.ExpectationsWereMet())
	})
}

func TestIntegrationBugModelBatchUpdateWithMapByIgid(t *testing.T) {
	assert := assert.New(t)

	// 初始化测试数据库
	gormDB, tmock, err := initSqlmock()
	assert.NoError(err)

	model := &integrationBugModel{db: gormDB}

	t.Run("success case2", func(t *testing.T) {
		// 设置SQL预期
		tmock.ExpectBegin()
		tmock.ExpectExec(regexp.QuoteMeta(`UPDATE "slam_integration_bug" SET "is_publish"=$1 WHERE integration_id = $2`)).
			WithArgs(2, 1).
			WillReturnResult(sqlmock.NewResult(0, 2))
		tmock.ExpectCommit()

		// 执行测试
		umap := map[string]any{
			"is_publish": 2,
		}
		err := model.BatchUpdateWithMapByIgid(1, umap)

		// 验证结果
		assert.NoError(err)
		assert.NoError(tmock.ExpectationsWereMet())
	})
}

func TestIntegrationBugModelUpdateWithMap(t *testing.T) {
	assert := assert.New(t)

	// 初始化测试数据库
	gormDB, tmock, err := initSqlmock()
	assert.NoError(err)

	model := &integrationBugModel{db: gormDB}

	t.Run("success case1", func(t *testing.T) {
		// 设置SQL预期
		tmock.ExpectBegin()
		tmock.ExpectExec(regexp.QuoteMeta(`UPDATE "slam_integration_bug" SET "check_res"=$1 WHERE id = $2 AND is_del = $3`)).
			WithArgs(1, 1, global.NUMBER_FALSE).
			WillReturnResult(sqlmock.NewResult(0, 1))
		tmock.ExpectCommit()

		// 执行测试
		umap := map[string]any{
			"check_res": 1,
		}
		rowsAffected, err := model.UpdateWithMap(1, umap)

		// 验证结果
		assert.NoError(err)
		assert.Equal(1, rowsAffected)
		assert.NoError(tmock.ExpectationsWereMet())
	})
}

func TestIntegrationBugModelRemoveRows(t *testing.T) {
	assert := assert.New(t)

	// 初始化测试数据库
	gormDB, tmock, err := initSqlmock()
	assert.NoError(err)

	model := &integrationBugModel{db: gormDB}

	t.Run("success case3", func(t *testing.T) {
		// 设置SQL预期
		tmock.ExpectBegin()
		tmock.ExpectExec(regexp.QuoteMeta(`UPDATE "slam_integration_bug" SET "is_del"=$1,"update_time"=$2 WHERE id in ($3,$4) AND source = $5 AND "is_del" = $6`)).
			WithArgs(global.NUMBER_TRUE, sqlmock.AnyArg(), 1, 2, global.INTEGRATION_SOURCE_MANUAL, global.NUMBER_FALSE).
			WillReturnResult(sqlmock.NewResult(0, 2))
		tmock.ExpectCommit()

		// 执行测试
		rowsAffected, err := model.RemoveRows([]int{1, 2})

		// 验证结果
		assert.NoError(err)
		assert.Equal(int64(2), rowsAffected)
		assert.NoError(tmock.ExpectationsWereMet())
	})
}

func TestIntegrationBugModelGetProductListByIgid(t *testing.T) {
	assert := assert.New(t)

	// 初始化测试数据库
	gormDB, tmock, err := initSqlmock()
	assert.NoError(err)

	model := &integrationBugModel{db: gormDB}

	t.Run("success case4", func(t *testing.T) {
		// 设置SQL预期
		tmock.ExpectQuery(regexp.QuoteMeta(`SELECT id, product_id FROM "slam_integration_bug" WHERE integration_id = $1`)).
			WithArgs(1).
			WillReturnRows(sqlmock.NewRows([]string{"id", "product_id"}).
				AddRow(1, 101).
				AddRow(2, 102))

		// 执行测试
		result, err := model.GetProductListByIgid(1)

		// 验证结果
		assert.NoError(err)
		assert.Equal(map[int]int{1: 101, 2: 102}, result)
		assert.NoError(tmock.ExpectationsWereMet())
	})
}

func TestIntegrationBugModelUpdateFromHistoryToNewIg(t *testing.T) {
	assert := assert.New(t)

	// 初始化测试数据库
	gormDB, tmock, err := initSqlmock()
	assert.NoError(err)

	model := &integrationBugModel{db: gormDB}

	t.Run("success case5", func(t *testing.T) {
		// 设置SQL预期
		tmock.ExpectBegin()
		tmock.ExpectExec(regexp.QuoteMeta(`UPDATE "slam_integration_bug" SET "check_res"=$1,"integration_id"=$2,"is_publish"=$3,"source"=$4 WHERE is_publish = $5 AND is_del = $6`)).
			WithArgs(global.NUMBER_FALSE, 10, IntegrationCheckPass, global.INTEGRATION_SOURCE_HISTORY, IntegrationCheckUnPass, global.NUMBER_FALSE).
			WillReturnResult(sqlmock.NewResult(0, 3))
		tmock.ExpectCommit()

		// 执行测试
		count, err := model.UpdateFromHistoryToNewIg(10)

		// 验证结果
		assert.NoError(err)
		assert.Equal(3, count)
		assert.NoError(tmock.ExpectationsWereMet())
	})
}

func TestIntegrationBugWithRpms(t *testing.T) {
	assert := assert.New(t)

	bug := IntegrationBugWithRpms{
		IntegrationBug: IntegrationBug{
			Id:        1,
			ProductId: 100,
			XflowId:   200,
		},
		Rpms: []*Rpm{
			{
				SrcName: "test.rpm",
				Sha256:  "abc123",
			},
		},
	}

	assert.Equal(1, bug.GetID())
	assert.Equal(100, bug.GetProductID())
	assert.Equal(200, bug.GetXflowID())
	assert.Equal([]*Rpm{{SrcName: "test.rpm", Sha256: "abc123"}}, bug.GetRpms())
}
