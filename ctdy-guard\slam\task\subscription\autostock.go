/*
 * @Author: lwq <EMAIL>
 * @Date: 2025-04-22
 * @LastEditors: lwq <EMAIL>
<<<<<<< HEAD
 * @LastEditTime: 2025-04-25
=======
 * @LastEditTime: 2025-04-29
>>>>>>> origin/slam
 * @FilePath: /ctdy-guard/slam/task/subscription/autostock.go
 * @Description: 通知类型：自动入库通知
 * @Copyright (c) 2025 by 2022.kylinos.cn, All Rights Reserved.
 */
package subscription

import (
	"fmt"
	"slam/model"
	. "slam/task/subscription/kernel"
	"time"
)

const (
	NoticeTypeAutoStock NotificationType = "autostock"
)

// AutoStockNotice 自动入库通知 负责初始化数据
type AutoStockNotice struct {
	NotificationType NotificationType
	monitorModel     model.FileMonitorRepo
}

// NewAutoStockNotice 创建自动入库通知
func NewAutoStockNotice() *AutoStockNotice {
	return &AutoStockNotice{
		NotificationType: NoticeTypeAutoStock,
		monitorModel:     model.NewFileMonitor(),
	}
}

// GetType 获取通知类型
func (n *AutoStockNotice) GetType() NotificationType {
	return n.NotificationType
}

/**
 * @author: lwq <EMAIL>
 * @description: SetTemplateData 设置模板数据，只负责模版数据部分，公共参数在通道中设置
 * @return {nil}
 */
func (n *AutoStockNotice) SetTemplateData(template *Template) *Template {
	template.Data = map[string]interface{}{
		"head":     "门禁系统：自动入库通知",
		"title":    fmt.Sprintf("自动入库基础统计【%s】", time.Now().Format("2006-01-02")),
		"subTitle": "请查看",
		"staffId":  n.NotificationType,
		"link":     "",
		"pcLink":   "",
	}

	stastics := n.monitorModel.CountStasticsDeveloper()

	type field struct {
		Key   string
		Value interface{}
	}
	// 预分配容量，每个统计项产生3个字段
	fields := make([]field, 0, len(stastics)*3)
	for _, s := range stastics {
		fields = append(fields,
			field{Key: s.ProductRelease + ":今日新增", Value: s.NewCount},
			field{Key: s.ProductRelease + ":今日更新", Value: s.UpdCount},
			field{Key: s.ProductRelease + ":今日入库", Value: s.StockCount},
		)
	}

	if len(fields) == 0 {
		fields = []field{
			{Key: "欧拉UPDATE仓库自动入库结果", Value: ""},
			{Key: "今日无新增更新", Value: 0},
		}
	}

	template.Data["fields"] = fields
	return template
}
