/*
 * @Author: lwq <EMAIL>
 * @Date: 2025-05-08
 * @LastEditors: lwq <EMAIL>
 * @LastEditTime: 2025-08-01
 * @FilePath: /ctdy-guard/slam/task/integration/integration.go
 * @Description: 集成单相关功能，负责处理api调用、数据初始化查询等
 * @Copyright (c) 2025 by 2022.kylinos.cn, All Rights Reserved.
 */
package integration

import (
	"errors"
	"fmt"
	"slam/global"
	"slam/model"
	"slam/slog"
	"slam/task"
	"slam/task/integration/option"
	"slam/task/integration/role"
	"slam/utils"
	"slam/utils/koji"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// 用集成单定义的角色ID来赋值对应的产品挂载的角色用户ID
type PuserMap map[int]int

type CheckReq struct {
	Ids         []int
	IgId        int
	CheckResult int
	CheckAdvice string
	CheckType   int
}

type PacksListReq struct {
	IgId      int `json:"igid"`
	XflowId   int
	Uid       int
	SrcName   string `json:"src_name"`
	StartDate string `json:"start_date"`
	EndDate   string `json:"end_date"`
	Urgent    int    `json:"urgent"`
	Status    int    `json:"status"`
}

type BatchCheckRes struct {
	SuccessNum int `json:"success_num"`
	FailNum    int `json:"fail_num"`
}
type IgDetailInfo struct {
	*model.Integration
	Count *model.IntegrationCount
}

// ProductModelInterface 创建接口，为了调用现有查询产品列表接口方便mock
type ModelFuncsInterface interface {
	/**
	 * @author: lwq <EMAIL>
	 * @description:ProductList 产品列表
	 * @param {[]int} ids
	 * @return {[]model.ProductList}
	 */
	ProductList(ids []int) []model.ProductList
}

type KojiPlatformInterface interface {
	GetKojiPlatforms() []model.KojiPlatform
}

type FuncsModel struct{}

// ProductList 产品列表 实现ProductModelInterface接口
func (d *FuncsModel) ProductList(ids []int) []model.ProductList {
	return model.GetProductListsByIds(ids)
}

type IntegrationInterface interface {
	/**
	 * @author: lwq <EMAIL>
	 * @description: GetIntegrationList 集成单列表
	 * @param {utils.Pager} pager
	 * @return {*model.Data_List, error}
	 */
	GetIntegrationList(pager utils.Pager, req PacksListReq) (*model.Data_List, error)
	/**
	 * @author: lwq <EMAIL>
	 * @description: GetIntegrationById 集成单详情
	 * @param {int} igid
	 * @return {*}
	 */
	GetIntegrationById(igid int) (*IgDetailInfo, error)

	/**
	 * @author: lwq <EMAIL>
	 * @description: GetIntegrationBugList 集成单bug列表
	 * @param {int} igid
	 * @param {string} srcName
	 * @param {utils.Pager} pager
	 * @return {[]*BugList, int64, error}
	 */
	GetIntegrationBugList(c *gin.Context, req PacksListReq, pager utils.Pager) (*model.Data_List, error)

	/**
	 * @author: lwq <EMAIL>
	 * @description: GetIntegrationCveList 集成单cve列表
	 * @param {int} igid
	 * @param {string} srcName
	 * @param {utils.Pager} pager
	 * @return {*}
	 */
	GetIntegrationCveList(c *gin.Context, req PacksListReq, pager utils.Pager) (*model.Data_List, error)
	/**
	 * @author: lwq <EMAIL>
	 * @description: CreateIntegration 手动创建集成单
	 * @param {*gin.Context} c
	 * @return {*}
	 */
	CreateIntegration(c *gin.Context) (any, error)
	/**
	 * @author: lwq <EMAIL>
	 * @description: 修改集成单别名和备注
	 * @param {*gin.Context} c
	 * @param {EditIgReq} req
	 * @return {*}
	 */
	EditIntegration(c *gin.Context, req *option.EditIgReq) (any, error)

	/**
	 * @author: lwq <EMAIL>
	 * @description: 手动触发集成单状态流转，只能由仓库管理员触发
	 * @param {*gin.Context} c
	 * @param {int} igId
	 * @return {*}
	 */
	RotateStatus(c *gin.Context, igId int) (any, error)

	/**
	 * @author: lwq <EMAIL>
	 * @description: 锁定和解锁集成单，方法暂时不用
	 * @param {*gin.Context} c
	 * @param {*} igId
	 * @param {int} lockStatus
	 * @return {*}
	 */
	LockIntegration(c *gin.Context, igId, lockStatus int) (any, error)

	/**
	 * @author: lwq <EMAIL>
	 * @description: 需要补录软甲包列表，用门禁列表和koji列表匹配
	 * @param {*gin.Context} c
	 * @param {int} igid
	 * @param {string} packageName
	 * @return {*}
	 */
	ReplenishPackages(c *gin.Context, igid int, packageName string) []string

	/**
	 * @author: lwq <EMAIL>
	 * @description: 补录修改软件包数据
	 * @param {*gin.Context} c
	 * @return {*}
	 */
	ReplenishData(c *gin.Context, req *option.ReplenishReq) (any, error)

	/**
	 * @author: lwq <EMAIL>
	 * @description: 初审软件包，审核支持批量
	 * @description: 目前初审完成是自动流转，
	 * @param {*gin.Context} c
	 * @param {EditIgReq} req
	 * @return {*}
	 */
	ReviewIntegration(c *gin.Context, req *CheckReq) (*BatchCheckRes, error)

	/**
	 * @author: lwq <EMAIL>
	 * @description: 技术经理审核
	 * @param {*gin.Context} c
	 * @param {EditIgReq} req
	 * @return {*}
	 */
	CheckIntegration(c *gin.Context, req *CheckReq) (*BatchCheckRes, error)

	/**
	 * @author: lwq <EMAIL>
	 * @description: 添加软件包测试信息
	 * @return {*}
	 */
	AddTestResult(c *gin.Context, req *CheckReq) (*BatchCheckRes, error)

	/**
	 * @author: lwq <EMAIL>
	 * @description: 复核软件包
	 * @param {*gin.Context} c
	 * @param {*CheckReq} req
	 * @return {*}
	 */
	RecheckIntegration(c *gin.Context, req *CheckReq) (*BatchCheckRes, error)

	/**
	 * @author: lwq <EMAIL>
	 * @description: 标记软件包发布状态
	 * @description: 高于审核状态，审核通过或复核通过后执行
	 * @param {*gin.Context} c
	 * @param {interface{}} req
	 * @return {*}
	 */
	FlagPublishStatus(c *gin.Context, req *option.CheckReq) (any, error)

	/**
	 * @author: lwq <EMAIL>
	 * @description: 整体确认集成单的操作，只有项目经理能操作，不用区分是否挂载产品
	 * @param {*gin.Context} c
	 * @param {any} req
	 * @return {*}
	 */
	ConfirmIntegration(c *gin.Context, igid int) (any, error)

	/**
	 * @author: lwq <EMAIL>
	 * @description: ExportIntegration 导出集成单 临时方法
	 * @description: 同时下载cve和bug及其相关审核信息，四个sheet
	 * @param {*gin.Context} c
	 * @param {int} igid
	 * @return {string, error}
	 */
	ExportIntegration(c *gin.Context, igid int) (string, error)

	/**
	 * @author: lwq <EMAIL>
	 * @description: 查询审核记录列表，全部无分页
	 * @param {*gin.Context} c
	 * @param {int} igid
	 * @param {int} checkType
	 * @return {*}
	 */
	CheckList(c *gin.Context, id, checkType int) ([]*model.IntegrationCheck, error)

	/**
	 * @author: lwq <EMAIL>
	 * @description: 查询可增加的软件包
	 * @param {*gin.Context} c
	 * @param {utils.Pager} pager
	 * @param {string} srcName
	 * @return {*}
	 */
	GetPackageList(c *gin.Context, pager utils.Pager, req PacksListReq) (*model.Data_List, error)

	/**
	 * @author: lwq <EMAIL>
	 * @description:¬
	 * @param {*gin.Context} c
	 * @param {int} igid
	 * @return {*}
	 */
	AddPackage(c *gin.Context, igid, xflowId int) (any, error)

	/**
	 * @author: lwq <EMAIL>
	 * @description: 批量删除软件包
	 * @param {*gin.Context} c
	 * @param {*option.RemoveReq} bq
	 * @return {*}
	 */
	RemovePackage(c *gin.Context, bq *option.RemoveReq) error
}

// 集成单结构体
type Integration struct {
	pm           PmInterface
	igModel      model.IntegrationRepo
	igBugModel   model.IntegrationRowsRepo
	igCveModel   model.IntegrationRowsRepo
	funcsModel   ModelFuncsInterface
	igCheckModel model.IntegrationCheckRepo
	xflowModel   model.XfllowModelRepo
	igCountModel model.IntegrationCountRepo
	kojiModel    KojiPlatformInterface
	igKojiModel  model.IntegrationKojiRepo
	igRpmModel   model.IntegrationRpmRepo
}

/**
 * @author: lwq <EMAIL>
 * @description:  NewIntegration 初始化集成单
 * @return {*}
 */
func NewIntegration() *Integration {
	return &Integration{
		pm:           NewProcessManager(),
		igModel:      model.NewIntegrationModel(),
		igBugModel:   model.NewIntegrationBugModel(),
		igCveModel:   model.NewIntegrationCveModel(),
		funcsModel:   &FuncsModel{},
		igCheckModel: model.NewIntegrationCheckModel(),
		xflowModel:   model.NewXfllowModel(),
		igCountModel: model.NewIntegrationCountModel(),
		kojiModel:    model.NewkojiModel(model.GetDB()),
		igKojiModel:  model.NewIntegrationKojiModel(),
		igRpmModel:   model.NewIntegrationBuildModel(),
	}
}

// GetIntegrationList 集成单列表
func (i *Integration) GetIntegrationList(pager utils.Pager, req PacksListReq) (*model.Data_List, error) {
	mReq := model.PacksListReq(req)
	return i.igModel.GetIntegrationList(pager, &mReq)
}

// GetIntegrationById 集成单详情
func (i *Integration) GetIntegrationById(igid int) (*IgDetailInfo, error) {
	ig, err := i.igModel.GetIntegrationById(igid)
	if err != nil {
		return nil, err
	}

	countInfo, _ := i.igCountModel.GetIntegrationCount(igid, ig.Status)
	return &IgDetailInfo{
		Integration: ig,
		Count:       countInfo,
	}, nil
}

// GetIntegrationBugList 集成单bug列表
func (i *Integration) GetIntegrationBugList(
	c *gin.Context, req PacksListReq, pager utils.Pager,
) (*model.Data_List, error) {
	mReq := model.PacksListReq(req)
	if mReq.Uid > 0 {
		_, _, userItem := task.GetUserInfo(c)
		mReq.Uid = *userItem.Id
	}
	res, total, err := i.igBugModel.GetIntegrationList(&mReq, pager)
	if err != nil {
		return nil, err
	}
	// 需要判断软件包是否被untag
	return &model.Data_List{
		List: res,
		Page: model.PageRes{
			Page_index: pager.Index,
			Page_size:  pager.Size,
			Total:      total,
		},
	}, nil
}

// GetIntegrationCveList 集成单cve列表
func (i *Integration) GetIntegrationCveList(
	c *gin.Context, req PacksListReq, pager utils.Pager,
) (*model.Data_List, error) {
	mReq := model.PacksListReq(req)
	if mReq.Uid > 0 {
		_, _, userItem := task.GetUserInfo(c)
		mReq.Uid = *userItem.Id
	}
	res, total, err := i.igCveModel.GetIntegrationList(&mReq, pager)
	if err != nil {
		return nil, err
	}
	return &model.Data_List{
		List: res,
		Page: model.PageRes{
			Page_index: pager.Index,
			Page_size:  pager.Size,
			Total:      total,
		},
	}, nil
}

// CreateIntegration 手动创建集成单
func (i *Integration) CreateIntegration(c *gin.Context) (any, error) {
	op := &Option{
		OptionID: option.CreateOptionID, // 更新操作
	}

	return i.pm.ExecuteOption(c, op, nil)
}

// EditIntegration 修改集成单别名和备注
func (i *Integration) EditIntegration(c *gin.Context, req *option.EditIgReq) (any, error) {
	op := &Option{
		IgID:     req.IgId,
		OptionID: option.UpdateOptionID,
		NodeID:   SkipCheckAccess,
	}

	return i.pm.ExecuteOption(c, op, req)
}

// RotateStatus 手动触发集成单状态流转，只能由仓库管理员触发
func (i *Integration) RotateStatus(c *gin.Context, igId int) (any, error) {
	op := &Option{
		IgID:     igId,
		OptionID: option.RotateOptionID,
	}
	return i.pm.ExecuteOption(c, op, nil)
}

// LockIntegration 锁定和解锁集成单，方法暂时不用
func (i *Integration) LockIntegration(c *gin.Context, igId, lockStatus int) (any, error) {
	op := &Option{
		IgID:     igId,
		OptionID: option.LockOptionID,
		NodeID:   SkipCheckAccess, // 暂时跳过
	}

	if lockStatus != global.INTEGRATION_LOCKED {
		op.OptionID = option.UnlockOptionID
	}

	return i.pm.ExecuteOption(c, op, igId)
}

// 需要补录软甲包列表，用门禁列表和koji列表匹配
func (i *Integration) ReplenishPackages(c *gin.Context, igid int, packageName string) []string {
	xflowIds := i.getXflowIds(igid)
	res, _ := i.xflowModel.GetNeedReplenishPackages(igid, xflowIds, packageName)
	return res
}

// 补录修改软件包数据
func (i *Integration) ReplenishData(c *gin.Context, req *option.ReplenishReq) (any, error) {
	op := &Option{
		IgID:     req.IgId,
		OptionID: option.ReplenishOptionID,
	}

	return i.pm.ExecuteOption(c, op, req)
}

// 初审软件包，审核支持批量,目前初审完成是自动流转，
func (i *Integration) ReviewIntegration(c *gin.Context, req *CheckReq) (*BatchCheckRes, error) {
	op := &Option{
		IgID:     req.IgId,
		OptionID: option.ReviewOptionID,
	}

	// 执行批量审核,审核成功需要调用流转操作，跳过节点权限校验
	res, err := i.batchExecuteCheck(c, op, req)
	if res.SuccessNum > 0 {
		op.NodeID = SkipCheckAccess
		op.OptionID = option.RotateOptionID
		i.pm.ExecuteOption(c, op, nil)
	}
	return res, err
}

// 技术经理审核
func (i *Integration) CheckIntegration(c *gin.Context, req *CheckReq) (*BatchCheckRes, error) {
	op := &Option{
		IgID:     req.IgId,
		OptionID: option.CheckOptionID,
	}

	// 执行批量审核,审核成功需要调用流转操作，跳过节点权限校验
	res, err := i.batchExecuteCheck(c, op, req)
	if res.SuccessNum > 0 {
		op.NodeID = SkipCheckAccess
		op.OptionID = option.RotateOptionID
		i.pm.ExecuteOption(c, op, nil)
	}
	return res, err
}

// 添加软件包测试信息
func (i *Integration) AddTestResult(c *gin.Context, req *CheckReq) (*BatchCheckRes, error) {
	op := &Option{
		IgID:     req.IgId,
		OptionID: option.TestResultOptionID,
	}

	// 执行批量审核,审核成功需要调用流转操作，跳过节点权限校验
	res, err := i.batchExecuteCheck(c, op, req)
	if res.SuccessNum > 0 {
		op.NodeID = SkipCheckAccess
		op.OptionID = option.RotateOptionID
		i.pm.ExecuteOption(c, op, nil)
	}
	return res, err
}

// 复核软件包
func (i *Integration) RecheckIntegration(c *gin.Context, req *CheckReq) (*BatchCheckRes, error) {
	op := &Option{
		IgID:     req.IgId,
		OptionID: option.ReCheckOptionID,
	}

	// 执行批量复核，复核不需要自动流转，等待库管操作
	return i.batchExecuteCheck(c, op, req)
}

// 标记软件包发布状态,高于审核状态，审核通过或复核通过后执行
func (i *Integration) FlagPublishStatus(c *gin.Context, req *option.CheckReq) (any, error) {
	op := &Option{
		IgID:     req.IgId,
		OptionID: option.FlagOptionID,
	}

	return i.pm.ExecuteOption(c, op, req)
}

// 整体确认集成单的操作，只有项目经理能操作，不用区分是否挂载产品
func (i *Integration) ConfirmIntegration(c *gin.Context, igid int) (any, error) {
	op := &Option{
		IgID:     igid,
		OptionID: option.ConfirmOptionID,
	}
	res, err := i.pm.ExecuteOption(c, op, igid)
	// 触发流转
	if err == nil {
		op.NodeID = SkipCheckAccess
		op.OptionID = option.RotateOptionID
		i.pm.ExecuteOption(c, op, true)
	}
	return res, err
}

// ExportIntegration 导出集成单 临时方法
func (i *Integration) ExportIntegration(c *gin.Context, igid int) (string, error) {
	op := &Option{
		IgID:     igid,
		OptionID: option.DownloadOptionID,
	}

	filePath, err := i.pm.ExecuteOption(c, op, igid)
	return filePath.(string), err
}

// 查询审核记录列表，全部无分页
func (i *Integration) CheckList(c *gin.Context, id, checkType int) ([]*model.IntegrationCheck, error) {
	return i.igCheckModel.GetCheckList(id, checkType)
}

// 查询可增加的软件包
func (i *Integration) GetPackageList(c *gin.Context, pager utils.Pager, req PacksListReq) (*model.Data_List, error) {
	mreq := model.PacksListReq(req)
	return i.xflowModel.GetPackageList(pager, mreq)
}

// AddPackage 添加软件包
func (i *Integration) AddPackage(c *gin.Context, igid, xflowId int) (any, error) {
	bq := &model.BugCveListReq{
		IgId:    igid,
		XflowId: xflowId,
		Source:  global.INTEGRATION_SOURCE_MANUAL,
	}

	op := &Option{
		IgID:     igid,
		OptionID: option.AddOptionId,
	}

	return i.pm.ExecuteOption(c, op, bq)
}

// 批量删除软件包
func (i *Integration) RemovePackage(c *gin.Context, bq *option.RemoveReq) error {
	op := &Option{
		IgID:     bq.IgId,
		OptionID: option.RemoveOptionId,
	}

	_, err := i.pm.ExecuteOption(c, op, bq)

	return err
}

/**
 * @author: lwq <EMAIL>
 * @description: 每一条数据都要校验是否有权限，所以只能循环执行
 * @param {*gin.Context} c
 * @param {*Option} op
 * @param {*CheckReq} req
 * @return {*}
 */
func (i *Integration) batchExecuteCheck(c *gin.Context, op *Option, req *CheckReq) (*BatchCheckRes, error) {
	op.IgID = req.IgId
	// 这里不是产品ID而是bug或者cve记录ID，需要根据ID查询产品ID
	var m model.IntegrationRowsRepo
	switch req.CheckType {
	case model.IntegrationCheckTypeBug:
		m = i.igBugModel
	case model.IntegrationCheckTypeCve:
		m = i.igCveModel
	}
	pids, _ := m.GetProductListByIgid(req.IgId)
	products := i.getProductUsers(pids)
	resultMap := &BatchCheckRes{
		SuccessNum: 0,
		FailNum:    0,
	}
	for _, id := range req.Ids {
		params := &option.CheckReq{
			Id:          id,
			IgId:        req.IgId,
			CheckResult: req.CheckResult,
			CheckAdvice: req.CheckAdvice,
			CheckType:   req.CheckType,
		}

		op.Pusers = products[id]
		_, err := i.pm.ExecuteOption(c, op, params)
		if err != nil {
			resultMap.FailNum++
		} else {
			resultMap.SuccessNum++
		}
	}
	return resultMap, nil
}

/**
 * @author: lwq <EMAIL>
 * @description: getProductUsers 获取产品表上挂载的各个角色ID
 * @description: 用于具体校验是否能操作某一条数据
 * @param {[]int} ids
 * @return {map[int]*ProductUsers}
 */
func (i *Integration) getProductUsers(idsMap map[int]int) map[int]PuserMap {
	pids := []int{}
	for _, pid := range idsMap {
		pids = append(pids, pid)
	}
	products := i.funcsModel.ProductList(pids)
	// 建立 pid 到 product 的映射
	productByPid := make(map[int]*model.ProductList)
	for _, product := range products {
		if product.Id != nil {
			productByPid[*product.Id] = &product
		}
	}
	productMap := make(map[int]PuserMap)
	for id, pid := range idsMap {
		if product, ok := productByPid[pid]; ok {
			productMap[id] = PuserMap{
				role.ArchitectRoleID:      product.ArchitectureManager,
				role.TechManagerRoleID:    product.TechnicalManager,
				role.ProjectManagerRoleID: product.ProjectManager,
			}
		}
	}
	return productMap
}

/**
 * @author: lwq <EMAIL>
 * @description: 自动创建集成单
 * @description: 取当前时间前七天的数据
 * @param {*CreateIgReq} req
 * @return {*}
 */
func (i *Integration) AutoCreateIntegration() {
	// 取当前时间为开始时间，往前推7天未结束时间
	StartDate := time.Now().Add(-1 * 7 * 24 * time.Hour)
	EndDate := time.Now()
	ig := &model.Integration{
		AliasName: "周期集成单",
		Remark: "自动创建集成单：" +
			StartDate.Format(global.DATE_FORMAT) + " 至 " + EndDate.Format(global.DATE_FORMAT),
		CreatorId: 0, CreatorName: "system",
		StartAt: StartDate, EnddAt: EndDate,
	}
	c := &gin.Context{}
	c.Set("traceid", ig.AliasName)
	// 创建集成单
	// ig.StartAt, _ = time.Parse(global.DATE_FORMAT, "2025-07-18")
	// ig.EnddAt, _ = time.Parse(global.DATE_FORMAT, "2025-07-19")
	cop := option.NewCreateOption()
	igid, err := cop.CreateIntegration(ig)
	if err != nil {
		slog.ErrorWithContext(c, err)
		return
	}
	ig.Id = igid
	// 添加本期的包操作
	aop := option.NewAddOption()
	bq := &model.BugCveListReq{
		IgId:      igid,
		StartDate: StartDate.Format(global.DATE_FORMAT),
		EndDate:   EndDate.Format(global.DATE_FORMAT),
		Source:    global.INTEGRATION_SOURCE_XFLOW,
	}
	_, err = aop.Execute(c, nil, bq)
	if err != nil {
		slog.ErrorWithContext(c, err)
		return
	}
	// 还需要添加历史遗留的包
	i.AddPackageFromHistory(igid)
	// 获取所有构建信息
	i.GetAllBuildsFromKoji(ig)
}

/**
 * @author: lwq <EMAIL>
 * @description: 将历史遗留的软件包转移到指定集成单中
 * @param {int} igid
 * @return {nil}
 */
func (i *Integration) AddPackageFromHistory(igid int) {
	bugNum, _ := i.igBugModel.UpdateFromHistoryToNewIg(igid)
	cveNum, _ := i.igCveModel.UpdateFromHistoryToNewIg(igid)
	// 如果有历史遗留的数据，则更新统计信息
	if cveNum > 0 || bugNum > 0 {
		umap := map[string]any{}
		umap["total_bug_count"] = gorm.Expr("total_bug_count + ?", bugNum)
		umap["total_cve_count"] = gorm.Expr("total_cve_count + ?", cveNum)
		i.igCountModel.UpdateCountInfo(igid, global.INTEGRATION_STATUS_COMPARE, umap)
	}
}

/**
 * @author: lwq <EMAIL>
 * @description: GetAllBuildsFromKoji 初始化当前集成单区间koji的所有构建的nvr
 * @description: 后期改成协程处理
 * @param {*model.Integration} ig
 * @return {*}
 */
func (i *Integration) GetAllBuildsFromKoji(ig *model.Integration) {
	// slam_koji_platform 拿到所有koji机器地址
	platforms := i.kojiModel.GetKojiPlatforms()
	// 遍历所有机器，拿到对应时间段内所有的nvr
	for _, platform := range platforms {
		// 根据kojilist中的ip字段初始化kojiClient
		kojiClient := koji.NewClient(
			koji.WithUrlBase(platform.Addr),
		)
		// 列出所有构建信息
		builds, _ := kojiClient.ListBuilds(koji.ParamListBuilds{
			CreatedAfter:  &ig.StartAt,
			CreatedBefore: &ig.EnddAt,
		})
		var kojis []*model.IntegrationKoji
		for _, build := range builds {
			kojis = append(kojis, &model.IntegrationKoji{
				IntegrationId: ig.Id,
				IpAddress:     platform.Addr,
				Nvr:           build.Nvr,
				PackageName:   build.Nvr + global.SRC_PACKAGE_SUFFIX,
			})
		}
		if len(kojis) > 0 {
			// 保存信息
			i.igKojiModel.BatchCreateKoji(kojis)
		}
	}
}

/**
 * @author: lwq <EMAIL>
 * @description: FlushBuildInfo 刷新build信息
 * @description: 该方法需要循环调用koji接口，基于定时任务，不要求速度，
 * @description: 为防止对koji接口造成压力，不加并发处理，每次处理5个ID
 * @param {int} igid
 * @return {*}
 */
func (i *Integration) FlushBuildInfo(igid int) {
	// 根据igid查询所有xflowid
	xflowIds := i.getXflowIds(igid)
	// 将 xflowIds 分块，每次处理5个ID
	chunkSize := 5
	for n := 0; n < len(xflowIds); n += chunkSize {
		end := n + chunkSize
		if end > len(xflowIds) {
			end = len(xflowIds)
		}
		chunkIds := xflowIds[n:end]

		// 处理当前分块
		rpmInfo := i.getBuildRpmInfo(chunkIds, igid)
		if rpmInfo != nil {
			err := i.igRpmModel.BatchInsertIntegrationBuild(rpmInfo)
			if err != nil {
				slog.Error.Println("FlushBuildInfo:BatchInsertIntegrationBuild err:", err)
				return
			}
		}
	}
}

/**
 * @author: lwq <EMAIL>
 * @description: 获取当前集成单中的xflow_id
 * @param {int} igid
 * @return {*}
 */
func (i *Integration) getXflowIds(igid int) []int {
	// 根据igid查询所有xflowid
	xflowIdsForBug, _ := i.igBugModel.GetXflowIdsByIgid(igid)
	xflowIdsForCve, _ := i.igCveModel.GetXflowIdsByIgid(igid)
	xflowIds := append(xflowIdsForBug, xflowIdsForCve...)
	xflowIds = utils.IntSliceDeduplication(xflowIds)
	return xflowIds
}

/**kylinos.cn
 * @description: getBuildRpmInfo 获取build rpm信息
 * @param {[]int} xflowIds
 * @param {int} igid
 * @return {[]*model.IntegrationRpmInfo}
 */
func (i *Integration) getBuildRpmInfo(xflowIds []int, igid int) []*model.IntegrationRpmInfo {
	if len(xflowIds) <= 0 {
		return nil
	}
	// 根据xflow_id查询所有的build_id
	buildMap, err := i.xflowModel.GetBuildInfosByXflowIds(xflowIds)
	if err != nil {
		slog.Error.Println("FlushBuildInfo:buildMap is empty")
		return nil
	}

	// 调用koji接口获取build信息
	rpmInfo, err := i.getKojiBuildInfo(buildMap, igid)
	if err != nil {
		slog.Error.Println("FlushBuildInfo:getKojiBuildInfoerr:", err)
		return nil
	}
	return rpmInfo
}

/**
 * @author: lwq <EMAIL>
 * @description: getKojiBuildInfo 调用koji接口获取build信息
 * @param {[]*model.BuildMapping} buildInfos
 * @param {int} igid
 * @return {[]*model.IntegrationRpmInfo, error}
 */
func (i *Integration) getKojiBuildInfo(buildInfos []*model.BuildMapping, igid int) (
	[]*model.IntegrationRpmInfo, error,
) {
	var rpmInfos []*model.IntegrationRpmInfo
	for _, buildMapping := range buildInfos {
		rpms, kojiClient, buildId, err := i.getKojiRpms(buildMapping)
		if err != nil {
			return nil, err
		}
		for _, rpm := range rpms {
			if !i.checkNeedUsed(&rpm) {
				continue
			}
			rpmInfo := &model.IntegrationRpmInfo{
				IntegrationId: igid,
				XflowId:       buildMapping.XflowId,
				SrcName:       buildMapping.SrcName,
				BuildId:       buildId,
				Arch:          rpm.Arch,
				Nvr:           rpm.Nvr,
				PackName:      rpm.Name,
			}
			//查询 koji 当前 rpmid 对应的签名数据
			err = i.setSignInfo(kojiClient, rpmInfo, rpm.Id)
			if err != nil {
				continue
			}
			rpmInfos = append(rpmInfos, rpmInfo)
		}
	}
	return rpmInfos, nil
}

/**
 * @author: lwq <EMAIL>
 * @description:setSignInfo 获取签名数据
 * @param {*koji.Client} kojiClient
 * @param {*model.IntegrationRpmInfo} rpmInfo
 * @param {int} rpmId
 * @return {*}
 */
func (i *Integration) setSignInfo(kojiClient *koji.Client, rpmInfo *model.IntegrationRpmInfo, rpmId int) error {
	signInfos, err := kojiClient.GetRPMChecksums(koji.ParamGetRPMChecksums{
		RpmId: koji.GetPointerInt(rpmId),
	})

	if err != nil {
		return err
	}
	for _, signInfo := range signInfos {
		//key 为空的时候是未签名
		if signInfo.Key == "" {
			rpmInfo.UnsigndMd5 = signInfo.Md5
			rpmInfo.UnsigndSha1 = signInfo.Sha1
			rpmInfo.UnsigndSha256 = signInfo.Sha256
		} else {
			rpmInfo.SigndMd5 = signInfo.Md5
			rpmInfo.SigndSha1 = signInfo.Sha1
			rpmInfo.SigndSha256 = signInfo.Sha256
		}
	}
	return nil
}

/**
 * @author: lwq <EMAIL>
 * @description: getKojiRpms 查询Koji平台编译的软件包列表
 * @param {*model.BuildMapping} buildMapping
 * @return {[]koji.ListRpmsRpmInfo, *koji.Client, int, error}
 */
func (i *Integration) getKojiRpms(buildMapping *model.BuildMapping) (
	[]koji.ListRpmsRpmInfo, *koji.Client, int, error,
) {
	// buildid 查询 koji 的构建包列表
	fmt.Println("正在查询Koji构建Rpms:", buildMapping.IpAddress)
	// 根据kojilist中的ip字段初始化kojiClient
	kojiClient := koji.NewClient(
		koji.WithUrlBase(buildMapping.IpAddress),
	)
	// 解析源码包
	packInfo := utils.ParsePackageName(
		buildMapping.SrcName, global.SOURCE_NAME_SUBFIX, false,
	)
	// 根据nvr获取Koji的build信息
	buildInfo, _ := kojiClient.GetBuild(koji.ParamGetBuild{
		Nvr: koji.GetPointerString(strings.Join([]string{
			packInfo.Name, packInfo.Version, packInfo.Release,
		}, "-")),
	})
	if buildInfo.BuildId == 0 {
		slog.Error.Println("getKojiBuildInfo:未获取到构建信息")
		return nil, nil, 0, errors.New("未获取到构建信息")
	}

	// 根据buildid 查询当前构建的rmp包列表
	rpms, err := kojiClient.ListRpms(koji.ParamListRpms{
		BuildId: koji.GetPointerString(fmt.Sprintf("%d", buildInfo.BuildId)),
	})

	if err != nil {
		slog.Error.Println("getKojiBuildInfo:未获取到软件包列表")
		return nil, nil, 0, err
	}

	return rpms, kojiClient, buildInfo.BuildId, nil
}

/**
 * @author: lwq <EMAIL>
 * @description: checkNeedUsed 检查是否需要使用
 * @param {*model.BuildMapping} buildMapping
 * @return {bool}
 */
func (i *Integration) checkNeedUsed(rpm *koji.ListRpmsRpmInfo) bool {
	// 判断 rpm.Arch 是否为 "src"
	if rpm.Arch == "src" {
		return false
	}
	// 判断 rpm.Nvr 是否包含 "debugsource"
	if strings.Contains(rpm.Nvr, "debugsource") {
		return false
	}

	return true
}
