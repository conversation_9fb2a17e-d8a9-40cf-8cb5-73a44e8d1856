package config

import (
	"encoding/base64"
	"fmt"
	"gopkg.in/ini.v1"
	"strings"
)

func InitConfig() {
	file, err := ini.Load("conf/config.ini")
	if err != nil {
		fmt.Printf("配置文件读取失败，请检查配置文件！")
		return
	}
	LoadServer(file)
	LoadData(file)
	LoadLog(file)
	LoadHook(file)
	LoadJenkins(file)
	LoadKoji(file)
}

// base64ToStr 对base64编码的字符串进行解码
func base64ToStr(base64Str string) string {
	decoded, err := base64.StdEncoding.DecodeString(base64Str)
	if err != nil {
		fmt.Println(err)
		return ""
	}
	strDecoded := string(decoded)
	strDecoded = strings.ReplaceAll(strDecoded, "\n", "")

	return strDecoded
}

var (
	HostIP     string
	Port       string
	DomainName string
)

func LoadServer(file *ini.File) {
	HostIP = file.Section("server").Key("HOST_IP").MustString("*************")
	Port = file.Section("server").Key("PORT").MustString("8080")
	DomainName = file.Section("server").Key("HOST_DOMAIN_NAME").MustString("server.kylinos.cn")
}

// Database 数据库链接配置
var (
	Type   string
	DataAk string
	DataSk string
	DBHost string
	DBPort string
	DBName string
)

func LoadData(file *ini.File) {
	Type = file.Section("pgsql").Key("TYPE").Value()
	DBName = file.Section("pgsql").Key("NAME").Value()
	DBHost = file.Section("pgsql").Key("DATABASE_HOST").Value()
	DataAk = file.Section("pgsql").Key("DATA_AK").Value()
	DataskRaw := file.Section("pgsql").Key("DATA_SK").Value()
	DataSk = base64ToStr(DataskRaw)
	DBPort = file.Section("pgsql").Key("DATABASE_PORT").MustString("5432")
}

var (
	LogLevel     string
	MaxSize      int
	MaxBackups   int
	MaxAge       int
	IsStdout     bool
	IsStackTrace bool
)

func LoadLog(file *ini.File) {
	LogLevel = file.Section("log").Key("LOG_LEVEL").Value()
	MaxSize = file.Section("log").Key("LOG_MAX_SIZE").MustInt(10)
	MaxBackups = file.Section("log").Key("LOG_MAX_BACKUP").MustInt(6)
	MaxAge = file.Section("log").Key("LOG_MAX_AGE").MustInt(30)
	IsStdout = file.Section("log").Key("IS_STDOUT").MustBool()
	IsStackTrace = file.Section("log").Key("IS_STACK_TRACE").MustBool()
}

var (
	SonarToken           string
	CompatibilityIp      string
	LicenseAddress       string
	LicenseUserName      string
	LicensePassword      string
	CompatibilityRepoUrl string
	InnerTestUrl         string
)

func LoadHook(file *ini.File) {
	CompatibilityIp = file.Section("hook").Key("COMPATIBILITY_IP").Value()
	Sonar := file.Section("hook").Key("SONAR_TOKEN").Value()
	SonarToken = base64ToStr(Sonar)
	LicenseAddress = file.Section("hook").Key("LICENSE_ADDRESS").Value()
	LicenseUserName = file.Section("hook").Key("LICENSE_USERNAME").Value()
	password := file.Section("hook").Key("LICENSE_PASSWORD").Value()
	LicensePassword = base64ToStr(password)
	CompatibilityRepoUrl = file.Section("hook").Key("COMPATIBILITY_REPO_URL").Value()
	InnerTestUrl = file.Section("hook").Key("INNER_TEST_URL").Value()
}

var (
	JenkinsDomainName string
	JenkinsPort       int
	JenkinsAk         string
	JenkinsSk         string
)

func LoadJenkins(file *ini.File) {
	JenkinsDomainName = file.Section("jenkins").Key("JENKINS_DOMAIN_NAME").MustString("ci-jenkins.kylinos.cn")
	JenkinsPort = file.Section("jenkins").Key("JENKINS_PORT").MustInt(8085)
	JenkinsAk = file.Section("jenkins").Key("JENKINS_AK").Value()
	JenkinsskRaw := file.Section("jenkins").Key("JENKINS_SK").Value()
	JenkinsSk = base64ToStr(JenkinsskRaw)
}

var (
	KojiAk   string
	KojiSk   string
	KojiBPAk string
	KojiBPSk string
)

func LoadKoji(file *ini.File) {
	KojiAk = file.Section("koji").Key("KOJI_AK").Value()
	KojiSkRaw := file.Section("koji").Key("KOJI_SK").Value()
	KojiSk = base64ToStr(KojiSkRaw)
	KojiBPAk = file.Section("koji").Key("KOJI_BPAK").Value()
	KojiBPSkRaw := file.Section("koji").Key("KOJI_BPSK").Value()
	KojiBPSk = base64ToStr(KojiBPSkRaw)
}
