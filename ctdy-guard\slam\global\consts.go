package global

// 配置开关
const (
	SWITCH_ON  = "true"
	SWITCH_OFF = "false"
)

// 软件包类型
const (
	SERVER  = "1" // rpm格式
	DESKTOP = "2" // deb格式
)

// 等级指标
const (
	INDEX_LEVEL_HIGH   = "1"
	INDEX_LEVEL_MIDDLE = "2"
	INDEX_LEVEL_LOW    = "3"
)

const (
	TASKNODE_READY   = 1
	TASKNODE_EXEC    = 2
	TASKNODE_SUCCESS = 3
	TASKNODE_FAILED  = 4

	//Hook 检测结果状态
	HOOK_TESTING = "test"
	HOOK_FAILED  = "fail"
	HOOK_SUCCECD = "success"
)

// hook执行结果类型
const (
	HOOK_RESULT_PASS   = "通过"
	HOOK_RESULT_FAIL   = "未通过"
	HOOK_RESULT_UNUSED = "不涉及"
	HOOK_RESULT_ERROR  = "执行失败"
)

// hook规则级别
const (
	HOOK_LEVEL_FORBIDDEN = "禁止"
	HOOK_LEVEL_WARNING   = "警告"
	HOOK_LEVEL_TIPS      = "提醒"
)

// 产品类型
const (
	PRODUCT_TYPE_SERVER  = "server"
	PRODUCT_TYPE_DESKTOP = "desktop"
)

// 产品主线产品类型
const (
	PRODUCT_DISPLAY_INNER = "1" // 内部产品
)

// 发布类型
const (
	SUBMIT_TYPE_OPEN     = "open"
	SUBMIT_TYPE_INTERNAL = "internal"
)

// koji build状态
const (
	KOJI_BUILD_STATE_BUILDING = "BUILDING"
	KOJI_BUILD_STATE_COMPLETE = "COMPLETE"
	KOJI_BUILD_STATE_FAILED   = "FAILED"
	KOJI_BUILD_STATE_CANCELED = "CANCELED"
)

// koji task状态
const (
	KOJI_TASK_STATE_OPEN     = "open"
	KOJI_TASK_STATE_FREE     = "free"
	KOJI_TASK_STATE_CLOSED   = "closed"
	KOJI_TASK_STATE_REPO     = "repo"
	KOJI_TASK_STATE_FAILED   = "failed"
	KOJI_TASK_STATE_CANCELED = "canceled"
)

// 流程类型
const (
	XFLOW_TYPE_STOCK           = "stock"           //代码入库流程
	XFLOW_TYPE_STOCK_CENTER    = "stock_center"    //中心仓入库流程
	XFLOW_TYPE_STOCK_BIZ       = "stock_biz"       //商业软件入库流程
	XFLOW_TYPE_LECTOTYPE       = "lectotype"       //选型流程
	XFLOW_TYPE_STOCK_TAG       = "tagbuild"        //产品追加tag流程
	XFLOW_TYPE_STOCK_UNTAG     = "untag"           //产品untag流程
	XFLOW_TYPE_PRODUCT_ADD     = "product_add"     //产品新增流程
	XFLOW_TYPE_PRODUCT_UPDATE  = "product_update"  //产品更新流程
	XFLOW_TYPE_TAG_MANAGE      = "tagbuild_manage" //tag管理流程
	XFLOW_TYPE_CODEBASE_MANAGE = "codebase_manage" //源码仓库管理流程
)

// 流程状态
const (
	XFLOW_STATUS_ACTIVE   = "active"
	XFLOW_STATUS_PENDING  = "pending"
	XFLOW_STATUS_REVOKE   = "revoke"
	XFLOW_STATUS_FINISHED = "finished"
	XFLOW_STATUS_FAILED   = "failed"
	XFLOW_STATUS_LOCK     = "lock"
	XFLOW_STATUS_TIMEOUT  = "timeout"
)

// 入库申请组件来源
const (
	STOCK_COMMIT_OPEN  = "1"
	STOCK_COMMIT_SELF  = "2"
	STOCK_COMMIT_THIRD = "3"
)

// 入库申请入库方式
const (
	STOCK_TYPE_KOJI     = "1"
	STOCK_TYPE_GERRIT   = "2"
	STOCK_TYPE_CenterS  = "3" // 中心仓入库源码包形式
	STOCK_TYPE_CenterF  = "4" // 中心仓入库文件形式
	STOCK_TYPE_BUSINESS = "5" //商业软件二进制包
)

// 商业软件入库形态
const (
	STOCK_BUSINESS_FORM_SOURCE = "source"
	STOCK_BUSINESS_FORM_BINARY = "binary"
	STOCK_BUSINESS_FORM_DEB    = "deb"
)

// 商业软件入库类型
const (
	STOCK_BUSINESS_TYPE_CHIPS         = "chips"
	STOCK_BUSINESS_TYPE_CLOUDSERVICES = "cloud-services"
	STOCK_BUSINESS_TYPE_DRIVERS       = "drivers"
	STOCK_BUSINESS_TYPE_KERNEL        = "kernel"
	STOCK_BUSINESS_TYPE_APP           = "app"
	STOCK_BUSINESS_TYPE_DEVELOP       = "develop"
	STOCK_BUSINESS_TYPE_CA            = "ca"
	STOCK_BUSINESS_TYPE_OTHERS        = "others"
)

// 软件选型类型
const (
	LECT_TYPE_DEVELOPER = "developer" // 来自开源社区的软件包
	LECT_TYPE_BIZ       = "biz"       // 来自厂商的商业软件包
)

// 入库申请入库原因
const (
	STOCK_REASON_CVE     = "1"
	STOCK_REASON_BUG     = "2"
	STOCK_REASON_FEATURE = "3"
)

// 入库申请验证情况
const (
	STOCK_VERIFY_HAS    = "1"
	STOCK_VERIFY_HASNOT = "2"
)

// 追加产品（Tag）类型
const (
	STOCK_TYPE_TAG   = "1"
	STOCK_TYPE_UNTAG = "2"
)

// 产品管理流程类型
const (
	PRODUCT_MNG_TYPE_CREATE = "create"
	PRODUCT_MNG_TYPE_UPDATE = "update"
)

// 代码仓分支创建类型
const (
	CODEBASE_MNG_REPO_TYPE_CENTER    = "package"
	CODEBASE_MNG_REPO_TYPE_BRANCH    = "branch"
	CODEBASE_MNG_REPO_TYPE_EMPTYREPO = "emptyrepo"
)

// 流转状态-入库申请
const (
	STOCK_FLOW_SUBMIT               = "submit"
	STOCK_FLOW_DOWNLOAD             = "download"
	STOCK_FLOW_SUBMIT_FAILED        = "submit_failed"
	STOCK_FLOW_SCRATCH_BUILD        = "scratch_build"
	STOCK_FLOW_SCRATCH_BUILD_RETRY  = "scratch_build_retry"
	STOCK_FLOW_SCRATCH_BUILD_FAILED = "scratch_build_failed"
	STOCK_FLOW_HOOK                 = "hook"
	STOCK_FLOW_HOOK_FAILED          = "hook_failed"
	STOCK_FLOW_EVALUATE             = "evaluate"
	STOCK_FLOW_EVALUATE_DENY        = "evaluate_deny"
	STOCK_FLOW_REVIEW               = "review"
	STOCK_FLOW_REVIEW_DENY          = "review_deny"
	STOCK_FLOW_BUILD                = "build"
	STOCK_FLOW_BUILD_RETRY          = "build_retry"
	STOCK_FLOW_BUILD_FAILED         = "build_failed"
	STOCK_FLOW_TAG_BUILD            = "tagbuild"
	STOCK_FLOW_TAG_BUILD_RETRY      = "tagbuild_retry"
	STOCK_FLOW_TAG_BUILD_FAILED     = "tagbuild_failed"
	STOCK_FLOW_ARCHIVE              = "archive"
	STOCK_FLOW_FINISHED             = "finished"
	STOCK_FLOW_FAILED               = "failed"
	STOCK_FLOW_REVOKE               = "revoke"
)

// 流转状态-产品追加tag
const (
	STOCK_TAG_FLOW_SUBMIT           = "submit"
	STOCK_TAG_FLOW_SUBMIT_FAILED    = "submit_failed"
	STOCK_TAG_FLOW_HOOK             = "hook"
	STOCK_TAG_FLOW_HOOK_FAILED      = "hook_failed"
	STOCK_TAG_FLOW_EVALUATE         = "evaluate"
	STOCK_TAG_FLOW_EVALUATE_DENY    = "evaluate_deny"
	STOCK_TAG_FLOW_REVIEW           = "review"
	STOCK_TAG_FLOW_REVIEW_DENY      = "review_deny"
	STOCK_TAG_FLOW_TAG_BUILD        = "tagbuild"
	STOCK_TAG_FLOW_TAG_BUILD_FAILED = "tagbuild_failed"
	STOCK_TAG_FLOW_UNTAG            = "untag"
	STOCK_TAG_FLOW_UNTAG_FAILED     = "untag_failed"
	STOCK_TAG_FLOW_ARCHIVE          = "archive"
	STOCK_TAG_FLOW_FINISHED         = "finished"
	STOCK_TAG_FLOW_FAILED           = "failed"
	STOCK_TAG_FLOW_REVOKE           = "revoke"
)

// 可以进行编辑的流转状态
var CANEDIT_FLOW_ARR = []string{
	"submit",
	"download",
	"submit_failed",
	"scratch_build",
	"scratch_build_retry",
	"scratch_build_failed",
	"hook",
	"hook_failed",
	"evaluate",
	"evaluate_deny",
	"review",
}

// 流转状态-中心仓入库
const (
	STOCK_CENTER_FLOW_SUBMIT               = "submit"
	STOCK_CENTER_FLOW_DOWNLOAD             = "download"
	STOCK_CENTER_FLOW_SUBMIT_FAILED        = "submit_failed"
	STOCK_CENTER_FLOW_SCRATCH_BUILD        = "scratch_build"
	STOCK_CENTER_FLOW_SCRATCH_BUILD_RETRY  = "scratch_build_retry"
	STOCK_CENTER_FLOW_SCRATCH_BUILD_FAILED = "scratch_build_failed"
	STOCK_CENTER_FLOW_HOOK                 = "hook"
	STOCK_CENTER_FLOW_HOOK_FAILED          = "hook_failed"
	STOCK_CENTER_FLOW_EVALUATE             = "evaluate"
	STOCK_CENTER_FLOW_EVALUATE_DENY        = "evaluate_deny"
	STOCK_CENTER_FLOW_REVIEW               = "review"
	STOCK_CENTER_FLOW_REVIEW_DENY          = "review_deny"
	STOCK_CENTER_FLOW_BUILD                = "build"
	STOCK_CENTER_FLOW_BUILD_RETRY          = "build_retry"
	STOCK_CENTER_FLOW_BUILD_FAILED         = "build_failed"
	STOCK_CENTER_FLOW_IMPORT               = "import"
	STOCK_CENTER_FLOW_IMPORT_RETRY         = "import_retry"
	STOCK_CENTER_FLOW_IMPORT_FAILED        = "import_failed"
	STOCK_CENTER_FLOW_ARCHIVE              = "archive"
	STOCK_CENTER_FLOW_FINISHED             = "finished"
	STOCK_CENTER_FLOW_FAILED               = "failed"
	STOCK_CENTER_FLOW_REVOKE               = "revoke"
)

// 流转状态-选型
const (
	LECTOTYPE_FLOW_SUBMIT          = "submit"
	LECTOTYPE_FLOW_SUBMIT_FAILED   = "submit_failed"
	LECTOTYPE_FLOW_DOWNLOAD        = "download"
	LECTOTYPE_FLOW_DOWNLOAD_FAILED = "download_failed"
	LECTOTYPE_FLOW_CHECK           = "check"
	LECTOTYPE_FLOW_CHECK_FAILED    = "check_failed"
	LECTOTYPE_FLOW_HOOK            = "hook"
	LECTOTYPE_FLOW_HOOK_FAILED     = "hook_failed"
	LECTOTYPE_FLOW_EVALUATE        = "evaluate"
	LECTOTYPE_FLOW_EVALUATE_DENY   = "evaluate_deny"
	LECTOTYPE_FLOW_REVIEW          = "review"
	LECTOTYPE_FLOW_REVIEW_DENY     = "review_deny"
	LECTOTYPE_FLOW_ARCHIVE         = "archive"
	LECTOTYPE_FLOW_ARCHIVE_FAILED  = "archive_failed"
	LECTOTYPE_FLOW_FINISHED        = "finished"
	LECTOTYPE_FLOW_FAILED          = "failed"
	LECTOTYPE_FLOW_REVOKE          = "revoke"
)

// 流转状态-产品管理
const (
	PRODUCT_MANAGE_FLOW_SUBMIT         = "submit"
	PRODUCT_MANAGE_FLOW_SUBMIT_FAILED  = "submit_failed"
	PRODUCT_MANAGE_FLOW_EVALUATE       = "evaluate"
	PRODUCT_MANAGE_FLOW_EVALUATE_DENY  = "evaluate_deny"
	PRODUCT_MANAGE_FLOW_REVIEW         = "review"
	PRODUCT_MANAGE_FLOW_REVIEW_DENY    = "review_deny"
	PRODUCT_MANAGE_FLOW_MANAGE         = "manage"
	PRODUCT_MANAGE_FLOW_MANAGE_FAILED  = "manage_failed"
	PRODUCT_MANAGE_FLOW_ARCHIVE        = "archive"
	PRODUCT_MANAGE_FLOW_ARCHIVE_FAILED = "archive_failed"
	PRODUCT_MANAGE_FLOW_FINISHED       = "finished"
	PRODUCT_MANAGE_FLOW_FAILED         = "failed"
	PRODUCT_MANAGE_FLOW_REVOKE         = "revoke"
)

// 流转状态-tag管理
const (
	TAG_MANAGE_FLOW_SUBMIT         = "submit"
	TAG_MANAGE_FLOW_SUBMIT_FAILED  = "submit_failed"
	TAG_MANAGE_FLOW_EVALUATE       = "evaluate"
	TAG_MANAGE_FLOW_EVALUATE_DENY  = "evaluate_deny"
	TAG_MANAGE_FLOW_REVIEW         = "review"
	TAG_MANAGE_FLOW_REVIEW_DENY    = "review_deny"
	TAG_MANAGE_FLOW_MANAGE         = "manage"
	TAG_MANAGE_FLOW_MANAGE_FAILED  = "manage_failed"
	TAG_MANAGE_FLOW_ARCHIVE        = "archive"
	TAG_MANAGE_FLOW_ARCHIVE_FAILED = "archive_failed"
	TAG_MANAGE_FLOW_FINISHED       = "finished"
	TAG_MANAGE_FLOW_FAILED         = "failed"
	TAG_MANAGE_FLOW_REVOKE         = "revoke"
)

// 流转状态-源码仓库CodeBase管理
const (
	CODEBASE_MANAGE_FLOW_SUBMIT         = "submit"
	CODEBASE_MANAGE_FLOW_SUBMIT_FAILED  = "submit_failed"
	CODEBASE_MANAGE_FLOW_EVALUATE       = "evaluate"
	CODEBASE_MANAGE_FLOW_EVALUATE_DENY  = "evaluate_deny"
	CODEBASE_MANAGE_FLOW_REVIEW         = "review"
	CODEBASE_MANAGE_FLOW_REVIEW_DENY    = "review_deny"
	CODEBASE_MANAGE_FLOW_MANAGE         = "manage"
	CODEBASE_MANAGE_FLOW_MANAGE_FAILED  = "manage_failed"
	CODEBASE_MANAGE_FLOW_ARCHIVE        = "archive"
	CODEBASE_MANAGE_FLOW_ARCHIVE_FAILED = "archive_failed"
	CODEBASE_MANAGE_FLOW_FINISHED       = "finished"
	CODEBASE_MANAGE_FLOW_FAILED         = "failed"
	CODEBASE_MANAGE_FLOW_REVOKE         = "revoke"
)

// 评估审核类型
const (
	EVALUATE_TYPE_EVALUATE   = "evaluate"
	EVALUATE_TYPE_ARCHREVIEW = "arch_review"
	EVALUATE_TYPE_REVIEW     = "review"
)

// 评估意见
const (
	EVALUATE_TYPE_AGREE  = "1"
	EVALUATE_TYPE_REJECT = "2"
)

// 评估审核意见
const (
	REVIEW_TYPE_AGREE  = "1"
	REVIEW_TYPE_REJECT = "2"
)

// koji 任务状态
const (
	KOJI_TYPE_CICD      = "cicd"
	KOJI_TYPE_TASK      = "task"
	KOJI_TYPE_BUILD     = "build"
	KOJI_TYPE_TAG_BUILD = "tag-build"
	KOJI_TYPE_IMPORT    = "import"
)

const (
	CONFIG_YAML_DIR    = "./conf/config.yaml"
	CER_FILES_DIR      = "./certs/"
	DATABASEPGPASS     = "/root/.pgpass"
	LECTO_TMP_DIR      = "/tmp/slam/lectotype/"
	CODEBASE_FILES_DIR = "/tmp/slam/codebase/"
	EXECL_DIR_PATH     = "/tmp/slam/"

	optfile_prefix             = "/opt/files"
	APPENDIX_DIR               = optfile_prefix + "/guard/appendix/"
	Stock_Appendix_DIR         = optfile_prefix + "/guard/appendix/stock/"
	DATABASE_AUTOBACK_DIR      = optfile_prefix + "/slam/backup/database/"
	LECTOTYPE_Appendix_DIR     = optfile_prefix + "/guard/appendix/lectotype/"
	STOCK_CENTER_APPENDIX_REPO = optfile_prefix + "/guard/appendix/stockcenter/"
	STOCK_TAG_APPENDIX_REPO    = optfile_prefix + "/guard/appendix/stocktag/"
	COMMON_TEMPLATE_REPO       = optfile_prefix + "/guard/appendix/common/" // 模板文件存放公用目录
	QAX_TOOL_PATH              = optfile_prefix + "/hook/qaxsafe/qaxScanTools"
	FORTIFY_PATH               = optfile_prefix + "/hook/fortify/bin/sourceanalyzer"
	FORTIFY_REPORTGENERATOR    = optfile_prefix + "/hook/fortify/bin/ReportGenerator"
	FORTIFY_REPO_PATH          = optfile_prefix + "/hook/repo/fortify"
	FORTIFY_DIR                = optfile_prefix + "/hook/fortify"
	COMPATIBILITY_REPO_PATH    = optfile_prefix + "/hook/repo/compatibility"
	LICENSE_REPO_PATH          = optfile_prefix + "/hook/repo/license"
	PACKAGE_TMP_PATH           = optfile_prefix + "/slam/hook/"
	LECTOTYPE_REPO             = optfile_prefix + "/slam/repos/"
	LECTOTYPE_BUSINESS_REPO    = optfile_prefix + "/slam/repos/business/"
	SRC_PACKAGE_DIR            = optfile_prefix + "/slam/tmp/src/"
	FILE_UPLOAD_TMP_DIR        = optfile_prefix + "/slam/tmp/upload/"
	REPORT_PATH                = optfile_prefix + "/test_reports/"
)

// 往 CVE 平台推送审核数据
const (
	CVE_REVIEW_PASS   = "1"
	CVE_REVIEW_NOPASS = "2"
)

const (
	DATE_FORMAT              = "2006-01-02"
	TIME_FORMAT              = "2006-01-02 15:04:05.000"
	TIME_FORMAT_SECOUND      = "2006-01-02 15:04:05"
	TIME_FORMAT_NO_DELIMITER = "20060102150405"
)

// 软件版本：1LTS 版；2 稳定版；3 最新版
const (
	LECTOTYPE_VERSION_LTS    = "1"
	LECTOTYPE_VERSION_STABLE = "2"
	LECTOTYPE_VERSION_NEW    = "3"
)

// 哈希值校验方法：1 MD5；2 SHA256；3 SHA1；4 MD5、SHA1、SHA256（DEB）
const (
	LECTOTYPE_HASHMETHOD_MD5    = "md5"
	LECTOTYPE_HASHMETHOD_SHA256 = "sha1"
	LECTOTYPE_HASHMETHOD_SHA1   = "sha256"
	LECTOTYPE_HASHMETHOD_DEB    = "4"
)

// 流行程度：1 非常不流行；2 比较不流行；3 一般流行；4 比较流行；5 非常流行
const (
	LECTOTYPE_POPULARITY_1 = "1"
	LECTOTYPE_POPULARITY_2 = "2"
	LECTOTYPE_POPULARITY_3 = "3"
	LECTOTYPE_POPULARITY_4 = "4"
	LECTOTYPE_POPULARITY_5 = "5"
)

const (
	LECTOTYPE_TYPE_SINGLE = "1"
	LECTOTYPE_TYPE_BATCH  = "2"
	LECTOTYPE_TYPE_FILE   = "3"
	LECTOTYPE_TYPE_BIZ    = "4"
)

const LECTOTYPE_URL_LIMIT = 20

// 返回前端字符
const (
	STR_PASS    = "1"
	STR_NO_PASS = "2"
)

const (
	KOJI_PLATFORM_LOONGARCH = "http://************:8239"
)

const (
	HOOK_TASK_STATE_OPEN     = "open"
	HOOK_TASK_STATE_UNZIP    = "unzip"
	HOOK_TASK_STATE_READY    = "ready"
	HOOK_TASK_STATE_RUNNING  = "running"
	HOOK_TASK_STATE_CLOSED   = "closed"
	HOOK_TASK_STATE_FAILED   = "failed"
	HOOK_TASK_STATE_CANCELED = "canceled"
)

const (
	STR_PARA_NOID            = "参数传递异常,流程id未传递"
	STR_PARA_ERROR           = "参数列表解析失败"
	STR_PARA_STANDARD        = "参数传递不符合规范"
	STR_PARA_PRODANAL        = "入库产品ID解析失败"
	STR_PARA_EVALUATORS      = "评估人参数列表解析失败"
	STR_EVALUATORS_LIST      = "查询用户评估任务列表失败"
	STR_PARA_APPENDIX        = "附件名称参数列表解析失败"
	ARGUMENT_ERROR           = "您输入的参数不对，请重新检查"
	PARAMENT_PARSING_FAILURE = "参数解析失败"
	PARAMENT_PASSING_FAILURE = "参数传递错误，请确认后重试"
	CREATE_PATH_FAILED       = "创建目录失败:"
	GET_USER_INFO_FAILED     = "获取用户信息失败"
	PACKAGE_FORMAT_ERROR     = "软件包文件格式异常"
)
const (
	commonSortCreatetiem = "array_to_string(array_agg(distinct slam_xflow.create_time), ',')"
)

var STOCK_SORT = map[string]string{
	"xflow_id":   "slam_xflow.id",
	"creator":    "array_to_string(array_agg(distinct \"user\".nickname), ',')",
	"createtime": commonSortCreatetiem,
	"src_name":   "array_to_string(array_agg(distinct slam_kojilist.src_name), ',')",
}

var STOCK_CENTER_SORT = map[string]string{
	"xflow_id":   "slam_xflow.id",
	"creator":    "array_to_string(array_agg(distinct \"user\".nickname), ',')",
	"createtime": commonSortCreatetiem,
	"src_name":   "array_to_string(array_agg(distinct slam_stcpacklist.pack_name), ',')",
}

var STOCK_TAG_SORT = map[string]string{
	"xflow_id":   "slam_xflow.id",
	"creator":    "array_to_string(array_agg(distinct \"user\".nickname), ',')",
	"createtime": commonSortCreatetiem,
	"src_name":   "array_to_string(array_agg(distinct slam_stocktagpacklist.package_name), ',')",
}

var LECTO_SOURCE_SORT = map[string]string{
	"pack_name":    "slam_lectpacklist.pack_name",
	"developers":   "slam_lectpacklist.developers",
	"prod_release": "slam_lectpacklist.prod_release",
	"pack_version": "slam_lectpacklist.pack_version",
}

var LECTO_FLOW_SORT = map[string]string{
	"xflow_id":     "slam_xflow.id",
	"package_name": "array_to_string(array_agg(distinct slam_lectpacklist.pack_fullname), ',')",
	"creator":      "array_to_string(array_agg(distinct \"user\".nickname), ',')",
	"createtime":   commonSortCreatetiem,
}

var PROD_LIST_SORT = map[string]string{
	"product_name": "slam_products.product_name",
	"tag":          "slam_taglist.name",
}

var TAG_LIST_SORT = map[string]string{
	"name":    "slam_taglist.name",
	"prod_id": "slam_taglist.prod_id",
	"target":  "slam_taglist.target",
}

var PROD_MANA_SORT = map[string]string{
	"product_name": "slam_prodmng_list.product_name",
	"creator":      "\"user\".nickname",
	"createtime":   "slam_prodmng_list.create_time",
}

var TAG_MANA_SORT = map[string]string{
	"name":       "slam_tagmng_list.name",
	"creator":    "\"user\".nickname",
	"createtime": "slam_tagmng_list.create_time",
	"xflow_id":   "slam_tagmng_list.xflow_id",
}

var Vendor_LIST_SORT = map[string]string{
	"id":              "id",
	"cer_name":        "cer_name",
	"vendor":          "vendor",
	"release":         "release",
	"pack_life_start": "pack_life_start",
	"pack_life_end":   "pack_life_end",
}

const (
	DB_XLOWID = "slam_xflow.id"
)

const (
	STR_ADD_FLOWADD      = "您提交的申请信息创建异常，请稍候重试"
	STR_ADD_FLOWUPDATE   = "您提交的申请信息未能成功创建，请稍候重试"
	STR_ADD_APPENDIX     = "您提交的流程信息出现附件存储异常情况，请联系管理员协助您处理"
	STR_ADD_STOCKSTATIC  = "您提交的入库申请信息创建异常，请稍候重试"
	STR_ADD_STOCKCONTEXT = "您提交的入库申请流转信息创建异常，请稍候重试"
	STR_ADD_EVALUATE     = "您提交的申请信息，未能成功转移给您选择的评估人，请联系管理员协助您处理"
	STR_OP_PERM          = "您无权进行该操作"
)

const (
	UrlBase               = "https://apigw-cec.cec.com.cn" // 平台接入地址
	UrlAppToken           = "/v1/apptoken/create"          // 获取app_token地址
	UrlUserId             = "/v2/staffs/id_mapping/fetch"  // 获取人员唯一标识地址
	UrlSearchUser         = "/v2/staffs/search"            // 根据名称模糊搜索
	UrlSendMsgToUser      = "/v1/messages/create"          // 发送私聊消息地址
	UrlSendMsgToGroupChat = "/v1/bot/hook/messages/create" // 发送群聊消息地址
)

// 商业软件包相关
const (
	BUSINESS_BINARY_STR = "Binary"
)

// 同步禅道任务相关
const (
	ZENTAO_STATUS_ACTIVE   = "active"   //激活
	ZENTAO_STATUS_RESOLVED = "resolved" //已解决
	ZENTAO_STATUS_CLOSED   = "closed"   //已关闭

	ZENTAO_SERVERITY_URGENCY = "Urgency" //高
	ZENTAO_SERVERITY_NORMAL  = "Normal"  //中
	ZENTAO_SERVERITY_LOW     = "Low"     //低

)

// 重复字符串定义
const (
	KOJI_IP_SUFFIX                   = "/kojihub/"
	KOJI_WEBURL_SUFFIX               = "/koji/"
	URL_STOCKDETAIL_PREFIX           = "https://server.kylinos.cn:19021/opensource/stockdetail?id="
	URL_STOCKDETAIL_PARA_STOCK       = "&platform=1&committype=1&stocktype=2"
	URL_STOCKDETAIL_PARA_STOCKCENTER = "&platform=1&committype=1&stocktype=3"
	SRC_PACKAGE_SUFFIX               = ".src.rpm"
	HTTP_HEADER_CONTENT_DISPOSITION  = "Content-Disposition"
)

const (
	SERVER_CI_ACCOUNT     = "jenkins"
	GFB_SERVER_CI_ACCOUNT = "server_gfb"

	SOURCE_NAME_SUBFIX = "src.rpm"
)

const (
	INTEGRATION_STATUS_COMPARE    = iota + 1 //校对中
	INTEGRATION_STATUS_CREATING              //创建中
	INTEGRATION_STATUS_CHECKING              //审核中
	INTEGRATION_STATUS_TESTING               //测试中
	INTEGRATION_STATUS_RECHECKING            //复核中
	INTEGRATION_STATUS_RELEASING             //发布中
	INTEGRATION_STATUS_PUBLISHED             //已发布
)

const (
	INTEGRATION_UNURGENT = 1 //非紧急
	INTEGRATION_URGENT   = 2 //紧急单

	INTEGRATION_UNLOCK = 1 //未锁定
	INTEGRATION_LOCKED = 2 //已锁定

	NUMBER_FALSE = 0
	NUMBER_TRUE  = 1
)

const (
	INTEGRATION_SOURCE_XFLOW     = iota + 1 // 入库流程
	INTEGRATION_SOURCE_HISTORY              // 历史遗留
	INTEGRATION_SOURCE_MANUAL               // 手动创建
	INTEGRATION_SOURCE_REPLENISH            // 补录
)
