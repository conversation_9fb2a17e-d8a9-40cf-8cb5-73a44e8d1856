package v1

import (
	"errors"
	"net/http"
	"net/http/httptest"
	"slam/global"
	"slam/model"
	"slam/task/integration"
	"slam/task/integration/option"
	"slam/utils"
	"strconv"
	"strings"
	"testing"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
)

var c *gin.Context

func TestMain(m *testing.M) {
	c = &gin.Context{}
	userId := utils.SubmitterId
	user := model.User{
		Id:       &userId,
		Role_id:  1,
		Nickname: "仓库小吕",
		Email:    "<EMAIL>",
	}
	c.Set("user_info", user)
	c.Set("traceid", time.Now().Format(global.TIME_FORMAT_NO_DELIMITER))
	m.Run()
}

var (
	msgSuccess         = "msg\":\"success"
	msgFailedToRecheck = "\"msg\":\"failed to recheck integration"
	msgCannotUnmarshal = "cannot unmarshal"
	contentType        = "Content-Type"
	applicationJson    = "application/json"
)

// MockIntegrationInterface 是 IntegrationInterface 的 mock 实现
type MockIntegrationInterface struct {
	mock.Mock
}

func (m *MockIntegrationInterface) CreateIntegration(c *gin.Context) (any, error) {
	args := m.Called(c)
	return args.Get(0), args.Error(1)
}

func (m *MockIntegrationInterface) GetIntegrationList(pager utils.Pager, req integration.PacksListReq) (*model.Data_List, error) {
	args := m.Called(pager, req)
	return args.Get(0).(*model.Data_List), args.Error(1)
}

func (m *MockIntegrationInterface) GetIntegrationById(igid int) (*integration.IgDetailInfo, error) {
	args := m.Called(igid)
	return args.Get(0).(*integration.IgDetailInfo), args.Error(1)
}

func (m *MockIntegrationInterface) EditIntegration(c *gin.Context, req *option.EditIgReq) (any, error) {
	args := m.Called(c, req)
	return args.Get(0), args.Error(1)
}

func (m *MockIntegrationInterface) RotateStatus(c *gin.Context, igId int) (any, error) {
	args := m.Called(c, igId)
	return args.Get(0), args.Error(1)
}

func (m *MockIntegrationInterface) LockIntegration(c *gin.Context, igId, lockStatus int) (any, error) {
	args := m.Called(c, igId, lockStatus)
	return args.Get(0), args.Error(1)
}

func (m *MockIntegrationInterface) ReplenishPackages(c *gin.Context, igid int, packageName string) []string {
	args := m.Called(c, igid, packageName)
	return args.Get(0).([]string)
}

func (m *MockIntegrationInterface) ReplenishData(c *gin.Context, req *option.ReplenishReq) (any, error) {
	args := m.Called(c, req)
	return args.Get(0), args.Error(1)
}

func (m *MockIntegrationInterface) ReviewIntegration(c *gin.Context, req *integration.CheckReq) (*integration.BatchCheckRes, error) {
	args := m.Called(c, req)
	return args.Get(0).(*integration.BatchCheckRes), args.Error(1)
}

func (m *MockIntegrationInterface) CheckIntegration(c *gin.Context, req *integration.CheckReq) (*integration.BatchCheckRes, error) {
	args := m.Called(c, req)
	return args.Get(0).(*integration.BatchCheckRes), args.Error(1)
}

func (m *MockIntegrationInterface) AddTestResult(c *gin.Context, req *integration.CheckReq) (*integration.BatchCheckRes, error) {
	args := m.Called(c, req)
	return args.Get(0).(*integration.BatchCheckRes), args.Error(1)
}

func (m *MockIntegrationInterface) RecheckIntegration(c *gin.Context, req *integration.CheckReq) (*integration.BatchCheckRes, error) {
	args := m.Called(c, req)
	return args.Get(0).(*integration.BatchCheckRes), args.Error(1)
}

func (m *MockIntegrationInterface) FlagPublishStatus(c *gin.Context, req *option.CheckReq) (any, error) {
	args := m.Called(c, req)
	return args.Get(0), args.Error(1)
}

func (m *MockIntegrationInterface) ConfirmIntegration(c *gin.Context, igid int) (any, error) {
	args := m.Called(c, igid)
	return args.Get(0), args.Error(1)
}

func (m *MockIntegrationInterface) ExportIntegration(c *gin.Context, igid int) (string, error) {
	args := m.Called(c, igid)
	return args.String(0), args.Error(1)
}

func (m *MockIntegrationInterface) CheckList(c *gin.Context, id, checkType int) ([]*model.IntegrationCheck, error) {
	args := m.Called(c, id, checkType)
	return args.Get(0).([]*model.IntegrationCheck), args.Error(1)
}

func (m *MockIntegrationInterface) GetPackageList(c *gin.Context, pager utils.Pager, req integration.PacksListReq) (*model.Data_List, error) {
	args := m.Called(c, pager, req)
	return args.Get(0).(*model.Data_List), args.Error(1)
}

func (m *MockIntegrationInterface) AddPackage(c *gin.Context, igid, xflowId int) (any, error) {
	args := m.Called(c, igid, xflowId)
	return args.Get(0), args.Error(1)
}

func (m *MockIntegrationInterface) RemovePackage(c *gin.Context, bq *option.RemoveReq) error {
	args := m.Called(c, bq)
	return args.Error(0)
}

func (m *MockIntegrationInterface) GetIntegrationBugList(c *gin.Context, req integration.PacksListReq, pager utils.Pager) (*model.Data_List, error) {
	args := m.Called(c, req, pager)
	return args.Get(0).(*model.Data_List), args.Error(1)
}

func (m *MockIntegrationInterface) GetIntegrationCveList(c *gin.Context, req integration.PacksListReq, pager utils.Pager) (*model.Data_List, error) {
	args := m.Called(c, req, pager)
	return args.Get(0).(*model.Data_List), args.Error(1)
}

// integration.POST("/create", integrationCenter.HandleCreateIntegration)
func TestHandleCreateIntegration(t *testing.T) {
	// 设置Gin为测试模式
	gin.SetMode(gin.TestMode)

	// 定义测试用例
	testCases := []struct {
		name           string
		mockReturnData any
		mockReturnErr  error
		expectedCode   int
		expectedBody   string
	}{
		{
			name:           "CreateIntegrationSuccess",
			mockReturnData: "success",
			mockReturnErr:  nil,
			expectedCode:   http.StatusOK,
			expectedBody:   msgSuccess,
		},
		{
			name:           "CreateIntegrationFailure",
			mockReturnData: nil,
			mockReturnErr:  errors.New("failed"),
			expectedCode:   http.StatusOK,
			expectedBody:   "msg\":\"failed",
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			// 创建mock对象
			mockIntegration := new(MockIntegrationInterface)
			controller := &IntegrationController{
				igTask: mockIntegration,
			}

			// 设置mock期望
			mockIntegration.On("CreateIntegration", mock.Anything).Return(tc.mockReturnData, tc.mockReturnErr)

			// 创建Gin路由器和请求
			r := gin.New()
			r.POST("/integration", controller.HandleCreateIntegration)

			req, _ := http.NewRequest("POST", "/integration", nil)
			w := httptest.NewRecorder()

			// 执行请求
			r.ServeHTTP(w, req)

			// 断言
			assert.Equal(t, tc.expectedCode, w.Code)
			assert.Contains(t, w.Body.String(), tc.expectedBody)

			mockIntegration.AssertExpectations(t)
		})
	}
}

func TestHandleIntegrationList(t *testing.T) {
	// 设置Gin为测试模式
	gin.SetMode(gin.TestMode)

	// 定义测试用例
	testCases := []struct {
		name           string
		mockReturnData *model.Data_List
		mockReturnErr  error
		expectedCode   int
	}{
		{
			name: "GetIntegrationListSuccess",
			mockReturnData: &model.Data_List{
				List: []any{},
				Page: model.PageRes{
					Page_index: 1,
					Page_size:  10,
					Total:      2,
				},
			},
			mockReturnErr: nil,
			expectedCode:  http.StatusOK,
		},
		{
			name:           "GetIntegrationListFailure",
			mockReturnData: nil,
			mockReturnErr:  errors.New("failed to get integration list"),
			expectedCode:   http.StatusOK,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			// 创建mock对象
			mockIntegration := new(MockIntegrationInterface)
			controller := &IntegrationController{
				igTask: mockIntegration,
			}

			// 设置mock期望
			mockIntegration.On("GetIntegrationList", mock.Anything, mock.Anything).Return(tc.mockReturnData, tc.mockReturnErr)

			// 创建Gin路由器和请求
			r := gin.New()
			r.GET("/list", controller.HandleIntegrationList)

			req, _ := http.NewRequest("GET", "/list", nil)
			req = req.WithContext(c)
			w := httptest.NewRecorder()

			// 执行请求
			r.ServeHTTP(w, req)

			// 断言
			assert.Equal(t, tc.expectedCode, w.Code)
			mockIntegration.AssertExpectations(t)
		})
	}
}

func TestHandleIntegrationDetail(t *testing.T) {
	// 设置Gin为测试模式
	gin.SetMode(gin.TestMode)

	// 定义测试用例
	testCases := []struct {
		name           string
		igid           int
		mockReturnData *integration.IgDetailInfo
		mockReturnErr  error
		expectedCode   int
		expectedBody   string
	}{
		{
			name: "GetIntegrationDetailSuccess",
			igid: 1,
			mockReturnData: &integration.IgDetailInfo{
				Integration: &model.Integration{
					Id: 1,
				},
			},
			mockReturnErr: nil,
			expectedCode:  http.StatusOK,
			expectedBody:  msgSuccess,
		},
		{
			name:           "GetIntegrationDetailFailure",
			igid:           2,
			mockReturnData: nil,
			mockReturnErr:  errors.New("failed to get integration detail"),
			expectedCode:   http.StatusOK,
			expectedBody:   "\"msg\":\"failed to get integration detail",
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			// 创建mock对象
			mockIntegration := new(MockIntegrationInterface)
			controller := &IntegrationController{
				igTask: mockIntegration,
			}

			// 设置mock期望
			mockIntegration.On("GetIntegrationById", tc.igid).Return(tc.mockReturnData, tc.mockReturnErr)

			// 创建Gin路由器和请求
			r := gin.New()
			r.GET("/detail/:igid", controller.HandleIntegrationDetail)

			req, _ := http.NewRequest("GET", "/detail/"+strconv.Itoa(tc.igid), nil)
			req = req.WithContext(c)
			w := httptest.NewRecorder()

			// 执行请求
			r.ServeHTTP(w, req)

			// 断言
			assert.Equal(t, tc.expectedCode, w.Code)
			assert.Contains(t, w.Body.String(), tc.expectedBody)
			mockIntegration.AssertExpectations(t)
		})
	}
}

func TestHandleIntegrationBugList(t *testing.T) {
	// 设置Gin为测试模式
	gin.SetMode(gin.TestMode)

	// 定义测试用例
	testCases := []struct {
		name           string
		igid           int
		mockReturnData *model.Data_List
		mockReturnErr  error
		expectedCode   int
		expectedBody   string
	}{
		{
			name: "GetIntegrationBugListSuccess",
			igid: 1,
			mockReturnData: &model.Data_List{
				List: []any{},
				Page: model.PageRes{
					Page_index: 1,
					Page_size:  10,
					Total:      2,
				},
			},
			mockReturnErr: nil,
			expectedCode:  http.StatusOK,
			expectedBody:  msgSuccess,
		},
		{
			name:           "GetIntegrationBugListFailure",
			igid:           2,
			mockReturnData: nil,
			mockReturnErr:  errors.New("failed to get integration bug list"),
			expectedCode:   http.StatusOK,
			expectedBody:   "\"msg\":\"failed to get integration bug list",
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			// 创建mock对象
			mockIntegration := new(MockIntegrationInterface)
			controller := &IntegrationController{
				igTask: mockIntegration,
			}

			// 设置mock期望
			mockIntegration.On("GetIntegrationBugList", mock.Anything, mock.Anything, mock.Anything).Return(tc.mockReturnData, tc.mockReturnErr)

			// 创建Gin路由器和请求
			r := gin.New()
			r.GET("/list/bug/:igid", controller.HandleIntegrationBugList)

			req, _ := http.NewRequest("GET", "/list/bug/"+strconv.Itoa(tc.igid), nil)
			req = req.WithContext(c)
			w := httptest.NewRecorder()

			// 执行请求
			r.ServeHTTP(w, req)

			// 断言
			assert.Equal(t, tc.expectedCode, w.Code)
			assert.Contains(t, w.Body.String(), tc.expectedBody)

			// 额外检查错误信息（仅在失败情况下）
			if tc.mockReturnErr != nil {
				assert.Contains(t, w.Body.String(), tc.mockReturnErr.Error())
			}

			mockIntegration.AssertExpectations(t)
		})
	}
}

func TestHandleIntegrationCveList(t *testing.T) {
	// 设置Gin为测试模式
	gin.SetMode(gin.TestMode)

	// 定义测试用例
	testCases := []struct {
		name           string
		igid           int
		mockReturnData *model.Data_List
		mockReturnErr  error
		expectedCode   int
		expectedBody   string
	}{
		{
			name: "GetIntegrationCveListSuccess",
			igid: 1,
			mockReturnData: &model.Data_List{
				List: []any{},
				Page: model.PageRes{
					Page_index: 1,
					Page_size:  10,
					Total:      2,
				},
			},
			mockReturnErr: nil,
			expectedCode:  http.StatusOK,
			expectedBody:  msgSuccess,
		},
		{
			name:           "GetIntegrationCveListFailure",
			igid:           2,
			mockReturnData: nil,
			mockReturnErr:  errors.New("failed to get integration cve list"),
			expectedCode:   http.StatusOK,
			expectedBody:   "\"msg\":\"failed to get integration cve list",
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			// 创建mock对象
			mockIntegration := new(MockIntegrationInterface)
			controller := &IntegrationController{
				igTask: mockIntegration,
			}

			// 设置mock期望
			mockIntegration.On("GetIntegrationCveList", mock.Anything, mock.Anything, mock.Anything).Return(tc.mockReturnData, tc.mockReturnErr)

			// 创建Gin路由器和请求
			r := gin.New()
			r.GET("/list/cve/:igid", controller.HandleIntegrationCveList)

			req, _ := http.NewRequest("GET", "/list/cve/"+strconv.Itoa(tc.igid), nil)
			req = req.WithContext(c)
			w := httptest.NewRecorder()

			// 执行请求
			r.ServeHTTP(w, req)

			// 断言
			assert.Equal(t, tc.expectedCode, w.Code)
			assert.Contains(t, w.Body.String(), tc.expectedBody)

			// 额外检查错误信息（仅在失败情况下）
			if tc.mockReturnErr != nil {
				assert.Contains(t, w.Body.String(), tc.mockReturnErr.Error())
			}

			mockIntegration.AssertExpectations(t)
		})
	}
}

func TestHandleDownloadIntegration(t *testing.T) {
	// 设置Gin为测试模式
	gin.SetMode(gin.TestMode)

	// 定义测试用例
	testCases := []struct {
		name                 string
		igid                 int
		mockReturnData       string
		mockReturnErr        error
		expectedCode         int
		expectedBodyContains string
	}{
		{
			name:                 "DownloadIntegrationSuccess",
			igid:                 1,
			mockReturnData:       "/path/to/file",
			mockReturnErr:        nil,
			expectedCode:         http.StatusOK,
			expectedBodyContains: "",
		},
		{
			name:                 "DownloadIntegrationFailure",
			igid:                 2,
			mockReturnData:       "",
			mockReturnErr:        errors.New("failed to export integration"),
			expectedCode:         http.StatusOK,
			expectedBodyContains: "\"msg\":\"failed to export integration",
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			// 创建mock对象
			mockIntegration := new(MockIntegrationInterface)
			controller := &IntegrationController{
				igTask: mockIntegration,
			}

			// 设置mock期望
			mockIntegration.On("ExportIntegration", mock.Anything, tc.igid).Return(tc.mockReturnData, tc.mockReturnErr)

			// 创建Gin路由器和请求
			r := gin.New()
			r.GET("/download/:igid", controller.HandleDownloadIntegration)

			req, _ := http.NewRequest("GET", "/download/"+strconv.Itoa(tc.igid), nil)
			req = req.WithContext(c)
			w := httptest.NewRecorder()

			// 执行请求
			r.ServeHTTP(w, req)

			// 断言
			assert.Equal(t, tc.expectedCode, w.Code)

			// 检查错误信息（仅在失败情况下）
			if tc.mockReturnErr != nil {
				assert.Contains(t, w.Body.String(), tc.expectedBodyContains)
			}

			mockIntegration.AssertExpectations(t)
		})
	}
}

func TestHandleCheckLogs(t *testing.T) {
	// 设置Gin为测试模式
	gin.SetMode(gin.TestMode)

	// 定义测试用例
	testCases := []struct {
		name                 string
		queryParams          string
		mockReturnData       []*model.IntegrationCheck
		mockReturnErr        error
		expectedCode         int
		expectedBodyContains string
	}{
		{
			name:        "CheckLogsSuccess",
			queryParams: "?id=1&type=1",
			mockReturnData: []*model.IntegrationCheck{
				{Id: 1, CheckType: 1},
				{Id: 2, CheckType: 2},
			},
			mockReturnErr:        nil,
			expectedCode:         http.StatusOK,
			expectedBodyContains: "\"id\":1",
		},
		{
			name:                 "CheckLogsFailure",
			queryParams:          "?id=2&type=1",
			mockReturnData:       nil,
			mockReturnErr:        errors.New("failed to get check logs"),
			expectedCode:         http.StatusOK,
			expectedBodyContains: "\"msg\":\"failed to get check logs",
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			// 创建mock对象
			mockIntegration := new(MockIntegrationInterface)
			controller := &IntegrationController{
				igTask: mockIntegration,
			}

			// 设置mock期望
			if tc.mockReturnData != nil || tc.mockReturnErr != nil {
				mockIntegration.On("CheckList", mock.Anything, mock.AnythingOfType("int"), mock.AnythingOfType("int")).Return(tc.mockReturnData, tc.mockReturnErr)
			}

			// 创建Gin路由器和请求
			r := gin.New()
			r.GET("/check/logs", controller.HandleCheckLogs)

			req, _ := http.NewRequest("GET", "/check/logs"+tc.queryParams, nil)
			req = req.WithContext(c)
			w := httptest.NewRecorder()

			// 执行请求
			r.ServeHTTP(w, req)

			// 断言
			assert.Equal(t, tc.expectedCode, w.Code)
			assert.Contains(t, w.Body.String(), tc.expectedBodyContains)

			mockIntegration.AssertExpectations(t)
		})
	}
}

func TestHandleEditIntegration(t *testing.T) {
	// 设置Gin为测试模式
	gin.SetMode(gin.TestMode)

	// 定义测试用例
	testCases := []struct {
		name           string
		reqBody        string
		mockReturnData any
		mockReturnErr  error
		expectedCode   int
		expectedBody   string
	}{
		{
			name:           "EditIntegrationSuccess",
			reqBody:        `{"id":1,"alias_name":"test","remark":"test remark"}`,
			mockReturnData: "success",
			mockReturnErr:  nil,
			expectedCode:   http.StatusOK,
			expectedBody:   msgSuccess,
		},
		{
			name:           "EditIntegrationFailure",
			reqBody:        `{"id":1,"alias_name":"test","remark":"test remark"}`,
			mockReturnData: nil,
			mockReturnErr:  errors.New("failed to edit integration"),
			expectedCode:   http.StatusOK,
			expectedBody:   "msg\":\"failed to edit integration",
		},
		{
			name:           "EditIntegrationInvalidRequest",
			reqBody:        `{"id":"invalid","alias_name":"test","remark":"test remark"}`,
			mockReturnData: nil,
			mockReturnErr:  nil,
			expectedCode:   http.StatusOK,
			expectedBody:   msgCannotUnmarshal,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			// 创建mock对象
			mockIntegration := new(MockIntegrationInterface)
			controller := &IntegrationController{
				igTask: mockIntegration,
			}

			// 设置mock期望
			if tc.mockReturnData != nil || tc.mockReturnErr != nil {
				mockIntegration.On("EditIntegration", mock.Anything, mock.Anything).Return(tc.mockReturnData, tc.mockReturnErr)
			}

			// 创建Gin路由器和请求
			r := gin.New()
			r.POST("/edit", controller.HandleEditIntegration)

			req, _ := http.NewRequest("POST", "/edit", strings.NewReader(tc.reqBody))
			req.Header.Set(contentType, applicationJson)
			req = req.WithContext(c)
			w := httptest.NewRecorder()

			// 执行请求
			r.ServeHTTP(w, req)

			// 断言
			assert.Equal(t, tc.expectedCode, w.Code)
			assert.Contains(t, w.Body.String(), tc.expectedBody)

			mockIntegration.AssertExpectations(t)
		})
	}
}

func TestHandlePackageList(t *testing.T) {
	// 设置Gin为测试模式
	gin.SetMode(gin.TestMode)

	// 定义测试用例
	testCases := []struct {
		name           string
		mockReturnData *model.Data_List
		mockReturnErr  error
		expectedCode   int
		expectedBody   string
	}{
		{
			name: "GetPackageListSuccess",
			mockReturnData: &model.Data_List{
				List: []any{},
				Page: model.PageRes{
					Page_index: 1,
					Page_size:  10,
					Total:      2,
				},
			},
			mockReturnErr: nil,
			expectedCode:  http.StatusOK,
			expectedBody:  "\"total\":2",
		},
		{
			name:           "GetPackageListFailure",
			mockReturnData: nil,
			mockReturnErr:  errors.New("failed to get package list"),
			expectedCode:   http.StatusOK,
			expectedBody:   "\"msg\":\"failed to get package list",
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			// 创建mock对象
			mockIntegration := new(MockIntegrationInterface)
			controller := &IntegrationController{
				igTask: mockIntegration,
			}

			// 设置mock期望
			mockIntegration.On("GetPackageList", mock.Anything, mock.Anything, mock.Anything).Return(tc.mockReturnData, tc.mockReturnErr)

			// 创建Gin路由器和请求
			r := gin.New()
			r.GET("/packs", controller.HandlePackageList)

			req, _ := http.NewRequest("GET", "/packs", nil)
			req = req.WithContext(c)
			w := httptest.NewRecorder()

			// 执行请求
			r.ServeHTTP(w, req)

			// 断言
			assert.Equal(t, tc.expectedCode, w.Code)
			assert.Contains(t, w.Body.String(), tc.expectedBody)

			// 额外检查错误信息（仅在失败情况下）
			if tc.mockReturnErr != nil {
				assert.Contains(t, w.Body.String(), tc.mockReturnErr.Error())
			}

			mockIntegration.AssertExpectations(t)
		})
	}
}

func TestHandleAddPackage(t *testing.T) {
	// 设置Gin为测试模式
	gin.SetMode(gin.TestMode)

	// 定义测试用例
	testCases := []struct {
		name          string
		reqBody       string
		mockReturnErr error
		expectedCode  int
		expectedBody  string
		toQuery       bool
	}{
		{
			name:          "AddPackageSuccess",
			reqBody:       `{"igid":1,"xflow_id":1}`,
			mockReturnErr: nil,
			expectedCode:  http.StatusOK,
			expectedBody:  msgSuccess,
			toQuery:       true,
		},
		{
			name:          "AddPackageFailure",
			reqBody:       `{"igid":1,"xflow_id":1}`,
			mockReturnErr: errors.New("failed to add package"),
			expectedCode:  http.StatusOK,
			expectedBody:  "msg\":\"failed to add package",
			toQuery:       true,
		},
		{
			name:          "AddPackageInvalidRequest",
			reqBody:       `{"igid":"invalid","xflow_id":1}`,
			mockReturnErr: nil,
			expectedCode:  http.StatusOK,
			expectedBody:  msgCannotUnmarshal,
			toQuery:       false,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			// 创建mock对象
			mockIntegration := new(MockIntegrationInterface)
			controller := &IntegrationController{
				igTask: mockIntegration,
			}
			if tc.toQuery {
				mockIntegration.On("AddPackage", mock.Anything, mock.AnythingOfType("int"), mock.AnythingOfType("int")).Return("", tc.mockReturnErr)
			}

			// 创建Gin路由器和请求
			r := gin.New()
			r.POST("/add", controller.HandleAddPackage)

			req, _ := http.NewRequest("POST", "/add", strings.NewReader(tc.reqBody))
			req.Header.Set(contentType, applicationJson)
			req = req.WithContext(c)
			w := httptest.NewRecorder()

			// 执行请求
			r.ServeHTTP(w, req)

			// 断言
			assert.Equal(t, tc.expectedCode, w.Code)
			assert.Contains(t, w.Body.String(), tc.expectedBody)

			mockIntegration.AssertExpectations(t)
		})
	}
}

func TestHandleRemovePackage(t *testing.T) {
	// 设置Gin为测试模式
	gin.SetMode(gin.TestMode)

	// 定义测试用例
	testCases := []struct {
		name          string
		reqBody       string
		mockReturnErr error
		expectedCode  int
		expectedBody  string
		toQuery       bool
	}{
		{
			name:          "RemovePackageSuccess",
			reqBody:       `{"igid":1,"ids":[1,2],"type":1}`,
			mockReturnErr: nil,
			expectedCode:  http.StatusOK,
			expectedBody:  msgSuccess,
			toQuery:       true,
		},
		{
			name:          "RemovePackageFailure",
			reqBody:       `{"igid":1,"ids":[1,2],"type":1}`,
			mockReturnErr: errors.New("failed to remove package"),
			expectedCode:  http.StatusOK,
			expectedBody:  "msg\":\"failed to remove package",
			toQuery:       true,
		},
		{
			name:          "RemovePackageInvalidRequest",
			reqBody:       `{"igid":"invalid","xflow_id":1}`,
			mockReturnErr: nil,
			expectedCode:  http.StatusOK,
			expectedBody:  msgCannotUnmarshal,
			toQuery:       false,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			// 创建mock对象
			mockIntegration := new(MockIntegrationInterface)
			controller := &IntegrationController{
				igTask: mockIntegration,
			}

			// 设置mock期望
			if tc.toQuery {
				mockIntegration.On("RemovePackage", mock.Anything, mock.Anything).Return(tc.mockReturnErr)
			}

			// 创建Gin路由器和请求
			r := gin.New()
			r.POST("/remove", controller.HandleRemovePackage)

			req, _ := http.NewRequest("POST", "/remove", strings.NewReader(tc.reqBody))
			req.Header.Set(contentType, applicationJson)
			req = req.WithContext(c)
			w := httptest.NewRecorder()

			// 执行请求
			r.ServeHTTP(w, req)

			// 断言
			assert.Equal(t, tc.expectedCode, w.Code)
			assert.Contains(t, w.Body.String(), tc.expectedBody)

			mockIntegration.AssertExpectations(t)
		})
	}
}

func TestHandleReviewIntegration(t *testing.T) {
	// 设置Gin为测试模式
	gin.SetMode(gin.TestMode)

	// 定义测试用例
	testCases := []struct {
		name           string
		reqBody        string
		mockReturnData *integration.BatchCheckRes
		mockReturnErr  error
		expectedCode   int
		expectedBody   string
		toQuery        bool
	}{
		{
			name:    "ReviewIntegrationSuccess",
			reqBody: `{"ids":[1,2],"igid":1,"check_result":1,"check_advice":"test","check_type":1}`,
			mockReturnData: &integration.BatchCheckRes{
				SuccessNum: 1,
				FailNum:    1,
			},
			mockReturnErr: nil,
			expectedCode:  http.StatusOK,
			expectedBody:  "\"msg\":\"success\"",
			toQuery:       true,
		},
		{
			name:    "ReviewIntegrationFailure",
			reqBody: `{"ids":[1,2],"igid":1,"check_result":1,"check_advice":"test","check_type":1}`,
			mockReturnData: &integration.BatchCheckRes{
				SuccessNum: 0,
				FailNum:    0,
			},
			mockReturnErr: errors.New("failed to review integration"),
			expectedCode:  http.StatusOK,
			expectedBody:  "\"msg\":\"failed to review integration",
			toQuery:       true,
		},
		{
			name:    "ReviewIntegrationInvalidRequest",
			reqBody: `{"ids":"invalid","igid":1,"check_result":1,"check_advice":"test","check_type":1}`,
			mockReturnData: &integration.BatchCheckRes{
				SuccessNum: 0,
				FailNum:    0,
			},
			mockReturnErr: nil,
			expectedCode:  http.StatusOK,
			expectedBody:  msgCannotUnmarshal,
			toQuery:       false,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			// 创建mock对象
			mockIntegration := new(MockIntegrationInterface)
			controller := &IntegrationController{
				igTask: mockIntegration,
			}

			// 设置mock期望
			if tc.toQuery {
				mockIntegration.On("ReviewIntegration", mock.Anything, mock.Anything).Return(tc.mockReturnData, tc.mockReturnErr)
			}

			// 创建Gin路由器和请求
			r := gin.New()
			r.POST("/review", controller.HandleReviewIntegration)

			req, _ := http.NewRequest("POST", "/review", strings.NewReader(tc.reqBody))
			req.Header.Set(contentType, applicationJson)
			req = req.WithContext(c)
			w := httptest.NewRecorder()

			// 执行请求
			r.ServeHTTP(w, req)

			// 断言
			assert.Equal(t, tc.expectedCode, w.Code)
			assert.Contains(t, w.Body.String(), tc.expectedBody)

			mockIntegration.AssertExpectations(t)
		})
	}
}

func TestHandleReplenishPackages(t *testing.T) {
	// 设置Gin为测试模式
	gin.SetMode(gin.TestMode)

	// 定义测试用例
	testCases := []struct {
		name           string
		igid           string
		srcName        string
		mockReturnData []string
		mockReturnErr  error
		expectedCode   int
		expectedBody   string
		toQuery        bool
	}{
		{
			name:           "ReplenishPackagesSuccess",
			igid:           "1",
			srcName:        "test",
			mockReturnData: []string{"pkg1", "pkg2"},
			mockReturnErr:  nil,
			expectedCode:   http.StatusOK,
			expectedBody:   "\"data\":[\"pkg1\",\"pkg2\"]",
			toQuery:        true,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			// 创建mock对象
			mockIntegration := new(MockIntegrationInterface)
			controller := &IntegrationController{
				igTask: mockIntegration,
			}

			// 设置mock期望
			if tc.toQuery {
				igid, _ := strconv.Atoi(tc.igid)
				mockIntegration.On("ReplenishPackages", mock.Anything, igid, tc.srcName).Return(tc.mockReturnData, tc.mockReturnErr)
			}

			// 创建Gin路由器和请求
			r := gin.New()
			r.GET("/replenish/:igid", controller.HandleReplenishPackages)

			url := "/replenish/" + tc.igid
			if tc.srcName != "" {
				url += "?src_name=" + tc.srcName
			}
			req, _ := http.NewRequest("GET", url, nil)
			req = req.WithContext(c)
			w := httptest.NewRecorder()

			// 执行请求
			r.ServeHTTP(w, req)

			// 断言
			assert.Equal(t, tc.expectedCode, w.Code)
			assert.Contains(t, w.Body.String(), tc.expectedBody)

			mockIntegration.AssertExpectations(t)
		})
	}
}

func TestHandleReplenishData(t *testing.T) {
	// 设置Gin为测试模式
	gin.SetMode(gin.TestMode)

	// 定义测试用例
	testCases := []struct {
		name           string
		reqBody        string
		mockReturnData any
		mockReturnErr  error
		expectedCode   int
		expectedBody   string
		toQuery        bool
	}{
		{
			name:           "ReplenishDataSuccess",
			reqBody:        `{"ig_id":1,"src_name":"test","bug_info":"bug info","cve_info":"cve info"}`,
			mockReturnData: "success",
			mockReturnErr:  nil,
			expectedCode:   http.StatusOK,
			expectedBody:   msgSuccess,
			toQuery:        true,
		},
		{
			name:           "ReplenishDataFailure",
			reqBody:        `{"ig_id":1,"src_name":"test","bug_info":"bug info","cve_info":"cve info"}`,
			mockReturnData: nil,
			mockReturnErr:  errors.New("failed to replenish data"),
			expectedCode:   http.StatusOK,
			expectedBody:   "msg\":\"failed to replenish data",
			toQuery:        true,
		},
		{
			name:           "ReplenishDataInvalidRequest",
			reqBody:        `{"ig_id":"invalid","src_name":"test","bug_info":"bug info","cve_info":"cve info"}`,
			mockReturnData: nil,
			mockReturnErr:  nil,
			expectedCode:   http.StatusOK,
			expectedBody:   msgCannotUnmarshal,
			toQuery:        false,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			// 创建mock对象
			mockIntegration := new(MockIntegrationInterface)
			controller := &IntegrationController{
				igTask: mockIntegration,
			}

			// 设置mock期望
			if tc.toQuery {
				mockIntegration.On("ReplenishData", mock.Anything, mock.Anything).Return(tc.mockReturnData, tc.mockReturnErr)
			}

			// 创建Gin路由器和请求
			r := gin.New()
			r.POST("/replenish", controller.HandleReplenishData)

			req, _ := http.NewRequest("POST", "/replenish", strings.NewReader(tc.reqBody))
			req.Header.Set(contentType, applicationJson)
			req = req.WithContext(c)
			w := httptest.NewRecorder()

			// 执行请求
			r.ServeHTTP(w, req)

			// 断言
			assert.Equal(t, tc.expectedCode, w.Code)
			assert.Contains(t, w.Body.String(), tc.expectedBody)

			mockIntegration.AssertExpectations(t)
		})
	}
}

func TestHandleRotateIntegration(t *testing.T) {
	// 设置Gin为测试模式
	gin.SetMode(gin.TestMode)

	// 定义测试用例
	testCases := []struct {
		name           string
		igid           string
		mockReturnData any
		mockReturnErr  error
		expectedCode   int
		expectedBody   string
		toQuery        bool
	}{
		{
			name:           "RotateIntegrationSuccess",
			igid:           "1",
			mockReturnData: "success",
			mockReturnErr:  nil,
			expectedCode:   http.StatusOK,
			expectedBody:   msgSuccess,
			toQuery:        true,
		},
		{
			name:           "RotateIntegrationFailure",
			igid:           "2",
			mockReturnData: nil,
			mockReturnErr:  errors.New("failed to rotate integration"),
			expectedCode:   http.StatusOK,
			expectedBody:   "msg\":\"failed to rotate integration",
			toQuery:        true,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			// 创建mock对象
			mockIntegration := new(MockIntegrationInterface)
			controller := &IntegrationController{
				igTask: mockIntegration,
			}

			// 设置mock期望
			if tc.toQuery {
				igid, _ := strconv.Atoi(tc.igid)
				mockIntegration.On("RotateStatus", mock.Anything, igid).Return(tc.mockReturnData, tc.mockReturnErr)
			}

			// 创建Gin路由器和请求
			r := gin.New()
			r.POST("/rotate/:igid", controller.HandleRotateIntegration)

			req, _ := http.NewRequest("POST", "/rotate/"+tc.igid, nil)
			req = req.WithContext(c)
			w := httptest.NewRecorder()

			// 执行请求
			r.ServeHTTP(w, req)

			// 断言
			assert.Equal(t, tc.expectedCode, w.Code)
			assert.Contains(t, w.Body.String(), tc.expectedBody)

			mockIntegration.AssertExpectations(t)
		})
	}
}

func TestHandleCheckIntegration(t *testing.T) {
	// 设置Gin为测试模式
	gin.SetMode(gin.TestMode)

	// 定义测试用例
	testCases := []struct {
		name           string
		reqBody        string
		mockReturnData *integration.BatchCheckRes
		mockReturnErr  error
		expectedCode   int
		expectedBody   string
		toQuery        bool
	}{
		{
			name:    "CheckIntegrationSuccess",
			reqBody: `{"ids":[1,2],"igid":1,"check_result":1,"check_advice":"test","check_type":1}`,
			mockReturnData: &integration.BatchCheckRes{
				SuccessNum: 1,
				FailNum:    1,
			},
			mockReturnErr: nil,
			expectedCode:  http.StatusOK,
			expectedBody:  msgSuccess,
			toQuery:       true,
		},
		{
			name:    "CheckIntegrationFailure",
			reqBody: `{"ids":[1,2],"igid":1,"check_result":1,"check_advice":"test","check_type":1}`,
			mockReturnData: &integration.BatchCheckRes{
				SuccessNum: 0,
				FailNum:    0,
			},
			mockReturnErr: errors.New("failed to check integration"),
			expectedCode:  http.StatusOK,
			expectedBody:  "\"msg\":\"failed to check integration",
			toQuery:       true,
		},
		{
			name:    "CheckIntegrationInvalidRequest",
			reqBody: `{"ids":"invalid","igid":1,"check_result":1,"check_advice":"test","check_type":1}`,
			mockReturnData: &integration.BatchCheckRes{
				SuccessNum: 0,
				FailNum:    0,
			},
			mockReturnErr: nil,
			expectedCode:  http.StatusOK,
			expectedBody:  msgCannotUnmarshal,
			toQuery:       false,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			// 创建mock对象
			mockIntegration := new(MockIntegrationInterface)
			controller := &IntegrationController{
				igTask: mockIntegration,
			}

			// 设置mock期望
			if tc.toQuery {
				mockIntegration.On("CheckIntegration", mock.Anything, mock.Anything).Return(tc.mockReturnData, tc.mockReturnErr)
			}

			// 创建Gin路由器和请求
			r := gin.New()
			r.POST("/check", controller.HandleCheckIntegration)

			req, _ := http.NewRequest("POST", "/check", strings.NewReader(tc.reqBody))
			req.Header.Set(contentType, applicationJson)
			req = req.WithContext(c)
			w := httptest.NewRecorder()

			// 执行请求
			r.ServeHTTP(w, req)

			// 断言
			assert.Equal(t, tc.expectedCode, w.Code)
			assert.Contains(t, w.Body.String(), tc.expectedBody)

			mockIntegration.AssertExpectations(t)
		})
	}
}

func TestHandleRecheckIntegration(t *testing.T) {
	// 设置Gin为测试模式
	gin.SetMode(gin.TestMode)

	// 定义测试用例
	testCases := []struct {
		name           string
		reqBody        string
		mockReturnData *integration.BatchCheckRes
		mockReturnErr  error
		expectedCode   int
		expectedBody   string
		toQuery        bool
	}{
		{
			name:    "RecheckIntegrationSuccess",
			reqBody: `{"ids":[1,2],"igid":1,"check_result":1,"check_advice":"test","check_type":1}`,
			mockReturnData: &integration.BatchCheckRes{
				SuccessNum: 1,
				FailNum:    1,
			},
			mockReturnErr: nil,
			expectedCode:  http.StatusOK,
			expectedBody:  msgSuccess,
			toQuery:       true,
		},
		{
			name:    "RecheckIntegrationFailure",
			reqBody: `{"ids":[1,2],"igid":1,"check_result":1,"check_advice":"test","check_type":1}`,
			mockReturnData: &integration.BatchCheckRes{
				SuccessNum: 0,
				FailNum:    0,
			},
			mockReturnErr: errors.New("failed to recheck integration"),
			expectedCode:  http.StatusOK,
			expectedBody:  msgFailedToRecheck,
			toQuery:       true,
		},
		{
			name:    "RecheckIntegrationInvalidRequest",
			reqBody: `{"ids":"invalid","igid":1,"check_result":1,"check_advice":"test","check_type":1}`,
			mockReturnData: &integration.BatchCheckRes{
				SuccessNum: 0,
				FailNum:    0,
			},
			mockReturnErr: nil,
			expectedCode:  http.StatusOK,
			expectedBody:  msgCannotUnmarshal,
			toQuery:       false,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			// 创建mock对象
			mockIntegration := new(MockIntegrationInterface)
			controller := &IntegrationController{
				igTask: mockIntegration,
			}

			// 设置mock期望
			if tc.toQuery {
				mockIntegration.On("RecheckIntegration", mock.Anything, mock.Anything).Return(tc.mockReturnData, tc.mockReturnErr)
			}

			// 创建Gin路由器和请求
			r := gin.New()
			r.POST("/recheck", controller.HandleRecheckIntegration)

			req, _ := http.NewRequest("POST", "/recheck", strings.NewReader(tc.reqBody))
			req.Header.Set(contentType, applicationJson)
			req = req.WithContext(c)
			w := httptest.NewRecorder()

			// 执行请求
			r.ServeHTTP(w, req)

			// 断言
			assert.Equal(t, tc.expectedCode, w.Code)
			assert.Contains(t, w.Body.String(), tc.expectedBody)

			mockIntegration.AssertExpectations(t)
		})
	}
}

func TestHandleAddTestResult(t *testing.T) {
	// 定义测试用例
	testCases := []struct {
		name           string
		reqBody        string
		mockReturnData *integration.BatchCheckRes
		mockReturnErr  error
		expectedCode   int
		expectedBody   string
		toQuery        bool
	}{
		{
			name:    "RecheckIntegrationSuccess",
			reqBody: `{"ids":[1,2],"igid":1,"check_result":1,"check_advice":"test","check_type":1}`,
			mockReturnData: &integration.BatchCheckRes{
				SuccessNum: 1,
				FailNum:    1,
			},
			mockReturnErr: nil,
			expectedCode:  http.StatusOK,
			expectedBody:  msgSuccess,
			toQuery:       true,
		},
		{
			name:    "RecheckIntegrationFailure",
			reqBody: `{"ids":[1,2],"igid":1,"check_result":1,"check_advice":"test","check_type":1}`,
			mockReturnData: &integration.BatchCheckRes{
				SuccessNum: 0,
				FailNum:    0,
			},
			mockReturnErr: errors.New("failed to recheck integration"),
			expectedCode:  http.StatusOK,
			expectedBody:  msgFailedToRecheck,
			toQuery:       true,
		},
		{
			name:    "RecheckIntegrationInvalidRequest",
			reqBody: `{"ids":"invalid","igid":1,"check_result":1,"check_advice":"test","check_type":1}`,
			mockReturnData: &integration.BatchCheckRes{
				SuccessNum: 0,
				FailNum:    0,
			},
			mockReturnErr: nil,
			expectedCode:  http.StatusOK,
			expectedBody:  msgCannotUnmarshal,
			toQuery:       false,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			// 创建mock对象
			mockIntegration := new(MockIntegrationInterface)
			controller := &IntegrationController{
				igTask: mockIntegration,
			}

			// 设置mock期望
			if tc.toQuery {
				mockIntegration.On("AddTestResult", mock.Anything, mock.Anything).Return(tc.mockReturnData, tc.mockReturnErr)
			}

			// 创建Gin路由器和请求
			r := gin.New()
			r.POST("/recheck", controller.HandleAddTestResult)

			req, _ := http.NewRequest("POST", "/recheck", strings.NewReader(tc.reqBody))
			req.Header.Set(contentType, applicationJson)
			req = req.WithContext(c)
			w := httptest.NewRecorder()

			// 执行请求
			r.ServeHTTP(w, req)

			// 断言
			assert.Equal(t, tc.expectedCode, w.Code)
			assert.Contains(t, w.Body.String(), tc.expectedBody)

			mockIntegration.AssertExpectations(t)
		})
	}
}

func TestHandleLockIntegration(t *testing.T) {
	// 设置Gin为测试模式
	gin.SetMode(gin.TestMode)

	// 定义测试用例
	testCases := []struct {
		name           string
		reqBody        string
		mockReturnData any
		mockReturnErr  error
		expectedCode   int
		expectedBody   string
		toQuery        bool
	}{
		{
			name:           "LockIntegrationSuccess",
			reqBody:        `{"id":1,"lock":1}`,
			mockReturnData: "success",
			mockReturnErr:  nil,
			expectedCode:   http.StatusOK,
			expectedBody:   msgSuccess,
			toQuery:        true,
		},
		{
			name:           "LockIntegrationFailure",
			reqBody:        `{"id":1,"lock":1}`,
			mockReturnData: nil,
			mockReturnErr:  errors.New("failed to lock integration"),
			expectedCode:   http.StatusOK,
			expectedBody:   "msg\":\"failed to lock integration",
			toQuery:        true,
		},
		{
			name:           "LockIntegrationInvalidRequest",
			reqBody:        `{"id":"invalid","lock":1}`,
			mockReturnData: nil,
			mockReturnErr:  nil,
			expectedCode:   http.StatusOK,
			expectedBody:   msgCannotUnmarshal,
			toQuery:        false,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			// 创建mock对象
			mockIntegration := new(MockIntegrationInterface)
			controller := &IntegrationController{
				igTask: mockIntegration,
			}

			// 设置mock期望
			if tc.toQuery {
				mockIntegration.On("LockIntegration", mock.Anything, mock.AnythingOfType("int"), mock.AnythingOfType("int")).Return(tc.mockReturnData, tc.mockReturnErr)
			}

			// 创建Gin路由器和请求
			r := gin.New()
			r.POST("/lock", controller.HandleLockIntegration)

			req, _ := http.NewRequest("POST", "/lock", strings.NewReader(tc.reqBody))
			req.Header.Set(contentType, applicationJson)
			req = req.WithContext(c)
			w := httptest.NewRecorder()

			// 执行请求
			r.ServeHTTP(w, req)

			// 断言
			assert.Equal(t, tc.expectedCode, w.Code)
			assert.Contains(t, w.Body.String(), tc.expectedBody)

			mockIntegration.AssertExpectations(t)
		})
	}
}

func TestHandleFlagIntegration(t *testing.T) {
	// 设置Gin为测试模式
	gin.SetMode(gin.TestMode)

	// 定义测试用例
	testCases := []struct {
		name           string
		reqBody        string
		mockReturnData any
		mockReturnErr  error
		expectedCode   int
		expectedBody   string
		toQuery        bool
	}{
		{
			name:           "FlagIntegrationSuccess",
			reqBody:        `{"id":1,"igid":1,"check_result":1,"check_advice":"test","check_type":1}`,
			mockReturnData: "success",
			mockReturnErr:  nil,
			expectedCode:   http.StatusOK,
			expectedBody:   msgSuccess,
			toQuery:        true,
		},
		{
			name:           "FlagIntegrationFailure",
			reqBody:        `{"id":1,"igid":1,"check_result":1,"check_advice":"test","check_type":1}`,
			mockReturnData: nil,
			mockReturnErr:  errors.New("failed to flag integration"),
			expectedCode:   http.StatusOK,
			expectedBody:   "msg\":\"failed to flag integration",
			toQuery:        true,
		},
		{
			name:           "FlagIntegrationInvalidRequest",
			reqBody:        `{"id":"invalid","igid":1,"check_result":1,"check_advice":"test","check_type":1}`,
			mockReturnData: nil,
			mockReturnErr:  nil,
			expectedCode:   http.StatusOK,
			expectedBody:   msgCannotUnmarshal,
			toQuery:        false,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			// 创建mock对象
			mockIntegration := new(MockIntegrationInterface)
			controller := &IntegrationController{
				igTask: mockIntegration,
			}

			// 设置mock期望
			if tc.toQuery {
				mockIntegration.On("FlagPublishStatus", mock.Anything, mock.Anything).Return(tc.mockReturnData, tc.mockReturnErr)
			}

			// 创建Gin路由器和请求
			r := gin.New()
			r.POST("/flag", controller.HandleFlagIntegration)

			req, _ := http.NewRequest("POST", "/flag", strings.NewReader(tc.reqBody))
			req.Header.Set(contentType, applicationJson)
			req = req.WithContext(c)
			w := httptest.NewRecorder()

			// 执行请求
			r.ServeHTTP(w, req)

			// 断言
			assert.Equal(t, tc.expectedCode, w.Code)
			assert.Contains(t, w.Body.String(), tc.expectedBody)

			mockIntegration.AssertExpectations(t)
		})
	}
}

func TestHandleConfirmIntegration(t *testing.T) {
	// 设置Gin为测试模式
	gin.SetMode(gin.TestMode)

	// 定义测试用例
	testCases := []struct {
		name           string
		igid           string
		mockReturnData any
		mockReturnErr  error
		expectedCode   int
		expectedBody   string
		toQuery        bool
	}{
		{
			name:           "ConfirmIntegrationSuccess",
			igid:           "1",
			mockReturnData: "success",
			mockReturnErr:  nil,
			expectedCode:   http.StatusOK,
			expectedBody:   msgSuccess,
			toQuery:        true,
		},
		{
			name:           "ConfirmIntegrationFailure",
			igid:           "2",
			mockReturnData: nil,
			mockReturnErr:  errors.New("failed to confirm integration"),
			expectedCode:   http.StatusOK,
			expectedBody:   "msg\":\"failed to confirm integration",
			toQuery:        true,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			// 创建mock对象
			mockIntegration := new(MockIntegrationInterface)
			controller := &IntegrationController{
				igTask: mockIntegration,
			}

			// 设置mock期望
			if tc.toQuery {
				igid, _ := strconv.Atoi(tc.igid)
				mockIntegration.On("ConfirmIntegration", mock.Anything, igid).Return(tc.mockReturnData, tc.mockReturnErr)
			}

			// 创建Gin路由器和请求
			r := gin.New()
			r.POST("/confirm/:igid", controller.HandleConfirmIntegration)

			req, _ := http.NewRequest("POST", "/confirm/"+tc.igid, nil)
			req = req.WithContext(c)
			w := httptest.NewRecorder()

			// 执行请求
			r.ServeHTTP(w, req)

			// 断言
			assert.Equal(t, tc.expectedCode, w.Code)
			assert.Contains(t, w.Body.String(), tc.expectedBody)

			mockIntegration.AssertExpectations(t)
		})
	}
}
