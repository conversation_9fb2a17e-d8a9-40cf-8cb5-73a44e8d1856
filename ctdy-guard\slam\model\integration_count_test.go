package model

import (
	"regexp"
	"testing"
	"time"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/stretchr/testify/assert"
)

func TestIntegrationCountModelGetIntegrationCount(t *testing.T) {
	assert := assert.New(t)

	// 初始化测试数据库
	gormDB, tmock, err := initSqlmock()
	assert.NoError(err)

	model := &IntegrationCountModel{db: gormDB}

	t.Run("success case", func(t *testing.T) {
		// 设置测试数据
		testTime := time.Now()
		expectedCount := &IntegrationCount{
			Id:             1,
			IntegrationId:  10,
			IgStatus:       2,
			TotalCount:     100,
			TotalBugCount:  30,
			TotalCveCount:  70,
			ReviewBugCount: 25,
			ReviewCveCount: 60,
			UnpublishCount: 5,
			CreatedAt:      testTime,
			UpdatedAt:      testTime,
		}

		// 设置SQL预期 - 修正正则表达式以匹配实际的SQL查询
		tmock.ExpectQuery(regexp.QuoteMeta(`SELECT * FROM "slam_integration_count" WHERE integration_id = $1 AND ig_status >= $2 ORDER BY id desc,"slam_integration_count"."id" LIMIT 1`)).
			WithArgs(10, 2).
			WillReturnRows(sqlmock.NewRows([]string{
				"id", "integration_id", "ig_status", "total_count", "total_bug_count", "total_cve_count",
				"review_bug_count", "review_cve_count", "unpublish_count", "create_time", "update_time",
			}).AddRow(
				expectedCount.Id, expectedCount.IntegrationId, expectedCount.IgStatus, expectedCount.TotalCount,
				expectedCount.TotalBugCount, expectedCount.TotalCveCount, expectedCount.ReviewBugCount,
				expectedCount.ReviewCveCount, expectedCount.UnpublishCount, expectedCount.CreatedAt, expectedCount.UpdatedAt,
			))

		// 执行测试
		count, err := model.GetIntegrationCount(10, 2)

		// 验证结果
		assert.NoError(err)
		assert.NotNil(count)
		assert.Equal(expectedCount, count)
		assert.NoError(tmock.ExpectationsWereMet())
	})
}

func TestIntegrationCountModelCreateIntegrationCount(t *testing.T) {
	assert := assert.New(t)

	// 初始化测试数据库
	gormDB, tmock, err := initSqlmock()
	assert.NoError(err)

	model := &IntegrationCountModel{db: gormDB}

	t.Run("success case1", func(t *testing.T) {
		// 设置测试数据
		testTime := time.Now()
		count := &IntegrationCount{
			IntegrationId:  10,
			IgStatus:       2,
			TotalCount:     100,
			TotalBugCount:  30,
			TotalCveCount:  70,
			ReviewBugCount: 25,
			ReviewCveCount: 60,
			UnpublishCount: 5,
			CreatedAt:      testTime,
			UpdatedAt:      testTime,
		}

		// 设置SQL预期
		tmock.ExpectBegin()
		tmock.ExpectQuery(regexp.QuoteMeta(`INSERT INTO "slam_integration_count" ("integration_id","ig_status","total_count","total_bug_count","total_cve_count","review_bug_count","review_cve_count","unpublish_count","create_time","update_time") VALUES ($1,$2,$3,$4,$5,$6,$7,$8,$9,$10) RETURNING "id"`)).
			WithArgs(
				count.IntegrationId, count.IgStatus, count.TotalCount, count.TotalBugCount, count.TotalCveCount,
				count.ReviewBugCount, count.ReviewCveCount, count.UnpublishCount, count.CreatedAt, count.UpdatedAt,
			).
			WillReturnRows(sqlmock.NewRows([]string{"id"}).AddRow(1))
		tmock.ExpectCommit()

		// 执行测试
		err := model.CreateIntegrationCount(count)

		// 验证结果
		assert.NoError(err)
		assert.NoError(tmock.ExpectationsWereMet())
	})
}

func TestIntegrationCountModelUpdateCountInfo(t *testing.T) {
	assert := assert.New(t)

	// 初始化测试数据库
	gormDB, tmock, err := initSqlmock()
	assert.NoError(err)

	model := &IntegrationCountModel{db: gormDB}

	t.Run("success case2", func(t *testing.T) {
		// 设置SQL预期
		tmock.ExpectBegin()
		tmock.ExpectExec(regexp.QuoteMeta(`UPDATE "slam_integration_count" SET "review_bug_count"=$1,"total_count"=$2 WHERE integration_id = $3 AND ig_status = $4`)).
			WithArgs(40, 150, 10, 2).
			WillReturnResult(sqlmock.NewResult(0, 1))
		tmock.ExpectCommit()

		// 执行测试
		umap := map[string]any{
			"total_count":      150,
			"review_bug_count": 40,
		}
		err := model.UpdateCountInfo(10, 2, umap)

		// 验证结果
		assert.NoError(err)
		assert.NoError(tmock.ExpectationsWereMet())
	})
}
