/*
 * @Author: lwq <EMAIL>
 * @Date: 2025-07-08
 * @LastEditors: lwq <EMAIL>
 * @LastEditTime: 2025-07-30
 * @FilePath: /ctdy-guard/slam/task/integration/option/base.go
 * @Description: 集成操作
 * @Copyright (c) 2025 by 2022.kylinos.cn, All Rights Reserved.
 */
package option

import (
	"slam/model"

	"github.com/gin-gonic/gin"
)

const (
	CreateOptionID     = iota + 1 // 创建集成单，库管执行
	UpdateOptionID                // 更新集成单的备注和名称，目前所有人可执行
	ReviewOptionID                // 初步审核，通常产品架构负责人执行
	ReplenishOptionID             // 补充操作，通常由产品架构负责人、库管执行
	AddOptionId                   // 添加软件包操作，库管执行
	RemoveOptionId                // 删除软件包操作，库管执行
	CheckOptionID                 // 复核操作，通常由技术经理执行
	TestResultOptionID            // 填写测试结果操作，通常由测试人员执行，由库管代理
	ReCheckOptionID               // 复核操作，通常由项目经理执行
	RotateOptionID                // 轮转操作，通常由库管代理执行，用于特殊情况，谨慎使用
	DownloadOptionID              // 下载操作，库管、技术经理、项目经理可执行
	LockOptionID                  // 锁定操作，库管特权(废弃)
	UnlockOptionID                // 解锁操作，库管特权(废弃)
	FlagOptionID                  // 标记操作，库管、项目经理可执行,用于标记是否可发布
	ConfirmOptionID               // 一键审批操作，项目经理特权
)

type OpUser struct {
	UserInfo
	OpIgInfo *model.Integration //操作的集成单信息
}

type UserInfo struct {
	UserId   int    `json:"user_id"`   // 用户ID
	UserName string `json:"user_name"` // 用户名
	RoleId   int    `json:"role_id"`   // 角色ID(集成单中承担的角色)
	RoleName string `json:"role_name"` // 角色名称
	Email    string `json:"email"`     // 用户邮箱
}

type OptionLog struct {
	LogType string `json:"log_type"`
	UserInfo
}
type OptionLogInterface interface {
	// GetLog 获取操作日志
	GetLog() OptionLog
}

// Option 定义操作接口
type OptionInterface interface {
	/**
	 * @author: lwq <EMAIL>
	 * @description: GetID 获取操作ID
	 * @return {string}
	 */
	GetID() string

	/**
	 * @author: lwq <EMAIL>
	 * @description: GetName 获取操作名称
	 * @return {string}
	 */
	GetName() string

	/**
	 * @author: lwq <EMAIL>
	 * @description: Execute 执行操作
	 * @return {any, error}
	 */
	Execute(c *gin.Context, opUser *OpUser, params any) (any, error)
}

// BaseOption 基础操作实现
type BaseOption struct {
	ID   string // 操作ID,用于唯一标识操作，暂时没有实际作用
	Name string
}

func (o *BaseOption) GetID() string {
	return o.ID
}

func (o *BaseOption) GetName() string {
	return o.Name
}

/**
 * @author: lwq <EMAIL>
 * @description: 初始化操作
 * @return {*}
 */
func GetOptions() map[int]OptionInterface {
	options := make(map[int]OptionInterface)
	options[CreateOptionID] = NewCreateOption()
	options[UpdateOptionID] = NewUpdateOption()
	options[ReviewOptionID] = NewReviewOption()
	options[ReplenishOptionID] = NewReplenishOption()
	options[AddOptionId] = NewAddOption()
	options[RemoveOptionId] = NewRemoveOption()
	options[CheckOptionID] = NewCheckOption()
	options[TestResultOptionID] = NewTestResultOption()
	options[ReCheckOptionID] = NewReCheckOption()
	options[RotateOptionID] = NewRotateOption()
	options[DownloadOptionID] = NewDownloadOption()
	options[LockOptionID] = NewLockOption()
	options[UnlockOptionID] = NewUnlockOption()
	options[FlagOptionID] = NewFlagOption()
	options[ConfirmOptionID] = NewConfirmOption()
	return options
}
