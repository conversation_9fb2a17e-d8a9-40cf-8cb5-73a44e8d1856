/*
 * @Author: lwq <EMAIL>
 * @Date: 2025-04-30
 * @LastEditors: lwq <EMAIL>
 * @LastEditTime: 2025-07-31
 * @FilePath: /ctdy-guard/slam/model/integration_cve.go
 * @Description: 集成单模型
 * @Copyright (c) 2025 by 2022.kylinos.cn, All Rights Reserved.
 */
package model

import (
	"errors"
	"slam/global"
	"slam/utils"
	"time"

	"gorm.io/gorm"
)

var queryByIntegrationId = "integration_id = ?"

type IntegrationCveWithRpms struct {
	IntegrationCve
	Rpms      []*Rpm `gorm:"rpms;serializer:json" json:"rpms" label:"二进制包列表"`
	IsUntag   int    `gorm:"column:is_untag" json:"is_untag" label:"是否被untag 1-是，0-否"`
	StockType string `gorm:"column:stock_type" json:"stock_type" label:"类型"`
}

type IntegrationCve struct {
	Id            int       `gorm:"column:id;primaryKey;autoIncrement:true;type:serial4;not null" json:"id" label:"序号，主键"`
	IntegrationId int       `gorm:"column:integration_id;not null" json:"integration_id" label:"集成单ID"`
	XflowId       int       `gorm:"column:xflow_id;not null" json:"xflow_id" label:"xflow ID"`
	CveId         string    `gorm:"column:cve_id;type:varchar(255);not null;default:''" json:"cve_id" label:"cve编号"`
	SubmiterId    int       `gorm:"column:submiter_id;not null" json:"submiter_id" label:"提交人id"`
	SubmiterName  string    `gorm:"column:submiter_name;type:varchar(50);not null;default:0" json:"submiter_name" label:"提交人姓名"`
	SrcName       string    `gorm:"column:src_name;type:varchar(255);not null;default:''" json:"src_name" label:"源包名称"`
	ProductId     int       `gorm:"column:product_id;not null" json:"product_id" label:"产品id"`
	ArchInfo      string    `gorm:"column:arch_info;type:varchar(255);not null;default:''" json:"arch_info" label:"架构信息"`
	TaskType      string    `gorm:"column:task_type;type:varchar(255);not null;default:''" json:"task_type" label:"任务类型"`
	Level         string    `gorm:"column:level;type:varchar(25);not null;default:''" json:"level" label:"紧急级别"`
	FixInfo       string    `gorm:"column:fix_info;type:text;not null;default:''" json:"fix_info" label:"修复信息"`
	CheckRes      int       `gorm:"column:check_res;not null;default:0" json:"check_res" label:"当前状态下审核结果 0-未审核 1-通过，2-不通过"`
	IsPublish     int       `gorm:"column:is_publish;not null;default:1" json:"is_publish" label:"最终结果是否发布 1-发布 2-不发布"`
	Source        int       `gorm:"column:source;not null;default:1" json:"source" label:"数据来源 1-入库流程 2-历史遗留 3-手动添加 4-补录"`
	IsDel         int       `gorm:"column:is_del;not null;default:0" json:"is_del" label:"是否删除 1-是，0-否"`
	CreatedAt     time.Time `gorm:"column:create_time;not null" json:"create_time,omitempty" label:"创建时间"`
	UpdatedAt     time.Time `gorm:"column:update_time;not null" json:"update_time,omitempty" label:"更新时间"`
}

// TableName 表名
func (*IntegrationCve) TableName() string {
	return "slam_integration_cve"
}
func (i IntegrationCveWithRpms) GetID() int {
	return i.Id
}

// GetProductID 实现 IntegrationItem 接口
func (i IntegrationCveWithRpms) GetProductID() int {
	return i.ProductId
}

func (i IntegrationCveWithRpms) GetXflowID() int {
	return i.XflowId
}

func (i IntegrationCveWithRpms) GetRpms() []*Rpm {
	return i.Rpms
}

// integrationCveModel 集成单Cve模型
type integrationCveModel struct {
	db *gorm.DB
}

/**
 * @author: lwq <EMAIL>
 * @description: NewIntegrationCveModel 初始化集成单Cve模型
 * @return {*}
 */
func NewIntegrationCveModel() IntegrationRowsRepo {
	return &integrationCveModel{
		db: GetDB(),
	}
}

// 批量插入slam_integration_cve表
func (i *integrationCveModel) BatchInsertIntegration(rows any) error {
	integrationBugs, ok := rows.([]*IntegrationCve)
	if !ok {
		return errors.New("rows类型断言失败")
	}
	return i.db.Create(integrationBugs).Error
}

// 分页查询集成单Cve列表
func (i *integrationCveModel) GetIntegrationList(req *PacksListReq, pager utils.Pager) (any, int64, error) {
	var integrationCves []*IntegrationCveWithRpms
	var total int64
	query := i.db.Table("slam_integration_cve as ic").
		Select("ic.*, COALESCE(cl.stock_type, '') as stock_type, "+
			"json_agg(CASE WHEN ir.nvr IS NOT NULL THEN json_build_object('src_name', concat_ws('.', ir.nvr, ir.arch_info, 'rpm'), 'signd_sha256', ir.signd_sha256) ELSE NULL END) FILTER (WHERE ir.nvr IS NOT NULL) as rpms").
		Joins("LEFT JOIN slam_integration_rpm as ir "+
			"ON ic.xflow_id = ir.xflow_id AND ic.src_name = ir.src_name AND ic.arch_info = ir.arch_info").
		Joins("LEFT JOIN slam_stockcenterlist as cl "+
			"ON ic.xflow_id = cl.xflow_id").
		Where("ic.integration_id = ?", req.IgId).
		Where("ic.is_del = ?", global.NUMBER_FALSE)

	if req.SrcName != "" {
		query = query.Where("ic.src_name LIKE ?", "%"+req.SrcName+"%")
	}

	// 如果与我相关
	if req.Uid > 0 {
		query = query.Joins("LEFT JOIN slam_products as sp ON ic.product_id = sp.id").
			Where("sp.architecture_manager = ? or sp.technical_manager = ? or sp.project_manager = ?", req.Uid, req.Uid, req.Uid)
	}

	err := query.Count(&total).Offset(pager.Offset).
		Limit(pager.Size).Group("ic.id, ic.src_name, cl.stock_type").
		Order("ic.id desc").Find(&integrationCves).Error
	return integrationCves, total, err
}

// 根据igid查询所有xflowid
func (i *integrationCveModel) GetXflowIdsByIgid(igid int) ([]int, error) {
	var xflowIds []int
	i.db.Model(&IntegrationCve{}).
		Select("xflow_id").Distinct().
		Where(queryByIntegrationId, igid).
		Pluck("xflow_id", &xflowIds)

	return xflowIds, nil
}

// 更新bug发布状态
func (i *integrationCveModel) UpdateWithMap(id int, umap map[string]any) (int, error) {
	result := i.db.Model(&IntegrationCve{}).
		Where("id = ?", id).
		Where("is_del = ?", global.NUMBER_FALSE).
		UpdateColumns(umap)
	return int(result.RowsAffected), result.Error
}

// 更新
func (i *integrationCveModel) BatchUpdateWithMapByIgid(igid int, umap map[string]any) error {
	return i.db.Model(&IntegrationCve{}).
		Where(queryByIntegrationId, igid).
		UpdateColumns(umap).Error
}

// 删除数据
func (i *integrationCveModel) RemoveRows(ids []int) (int64, error) {
	result := i.db.Model(&IntegrationCve{}).
		Where("id in ?", ids).
		Where("source = ?", global.INTEGRATION_SOURCE_MANUAL).
		Where("is_del", global.NUMBER_FALSE).
		Update("is_del", global.NUMBER_TRUE)
	return result.RowsAffected, result.Error
}

// 根据igid获取bug中产品Id列表
func (i *integrationCveModel) GetProductListByIgid(igid int) (map[int]int, error) {
	var bugs []IntegrationCve
	err := i.db.Model(&IntegrationCve{}).
		Select("id, product_id").
		Where(queryByIntegrationId, igid).
		Find(&bugs).Error
	if err != nil {
		return nil, err
	}
	result := make(map[int]int)
	for _, bug := range bugs {
		result[bug.Id] = bug.ProductId
	}
	return result, nil
}

// 把历史遗留么有发布的数据跟新到新的集成单中
func (i *integrationCveModel) UpdateFromHistoryToNewIg(igid int) (int, error) {
	query := i.db.Model(&IntegrationCve{}).
		Where("is_publish = ?", IntegrationCheckUnPass).
		Where("is_del = ?", global.NUMBER_FALSE).
		UpdateColumns(map[string]any{
			"integration_id": igid,
			"is_publish":     IntegrationCheckPass,
			"check_res":      global.NUMBER_FALSE,
			"source":         global.INTEGRATION_SOURCE_HISTORY,
		})
	return int(query.RowsAffected), query.Error
}
