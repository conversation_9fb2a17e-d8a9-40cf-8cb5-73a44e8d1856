package model

import (
	"fmt"
	"log"
)

// GetCommitTypeByXflowId 获取stock流程的commit type
func GetCommitTypeByXflowId(xflowId int) int {
	var commitType int
	err := DB.Table("slam_stocklist").Where("xflow_id=?", xflowId).Pluck("commit_type", &commitType).Error
	if err != nil {
		fmt.Println(err.Error())
		return 0
	}
	return commitType
}

// 获取xflow流程的用户id和类型
func GetUserIdAndTypeByXflowId(xflowId int) (int64, string, error) {
	result := make(map[string]interface{})
	err := DB.Debug().Table("slam_xflow").
		Where("id = ?", xflowId).
		Select("user_id, type").
		Scan(&result).Error
	if err != nil {
		log.Fatal(err)
	}
	return result["user_id"].(int64), result["type"].(string), err
}
