/*
 * @Author: lwq <EMAIL>
 * @Date: 2025-07-08
 * @LastEditors: lwq <EMAIL>
 * @LastEditTime: 2025-07-30
 * @FilePath: /ctdy-guard/slam/task/integration/option/falg.go
 * @Description: 集成操作
 * @Copyright (c) 2025 by 2022.kylinos.cn, All Rights Reserved.
 */
package option

import (
	"errors"
	"slam/model"
	"slam/slog"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// FlagOption 标记软件包操作
type FlagOption struct {
	CheckOption
}

func NewFlagOption() *FlagOption {
	return &FlagOption{
		CheckOption: CheckOption{
			BaseOption: BaseOption{
				ID:   "flag",
				Name: "标记软件包",
			},
			igCheckModel: model.NewIntegrationCheckModel(),
			igCountModel: model.NewIntegrationCountModel(),
			bugModel:     model.NewIntegrationBugModel(),
			cveModel:     model.NewIntegrationCveModel(),
		},
	}
}

// Execute 执行操作 重写父类方法
func (o *FlagOption) Execute(c *gin.Context, opUser *OpUser, params any) (any, error) {
	req := params.(*CheckReq)

	umap := map[string]any{}
	bumap := map[string]any{"is_publish": req.CheckResult}
	var m model.IntegrationRowsRepo
	switch req.CheckType {
	case model.IntegrationCheckTypeBug:
		m = o.bugModel
	case model.IntegrationCheckTypeCve:
		m = o.cveModel
	}
	// 修改发布状态
	rowsAffected, _ := m.UpdateWithMap(req.Id, bumap)
	if rowsAffected <= 0 {
		slog.ErrorWithContext(c, "更新状态失败，受影响行数为0")
		return nil, errors.New("审核失败，请重试！")
	}

	err := o.addCheckInfo(req, opUser)
	if err != nil {
		slog.ErrorWithContext(c, "添加审核记录失败", err)
		return nil, err
	}
	// 修改发布统计数据
	if req.CheckResult == model.IntegrationCheckUnPass {
		umap["unpublish_count"] = gorm.Expr("unpublish_count + ?", 1)
	} else {
		umap["unpublish_count"] = gorm.Expr("unpublish_count - ?", 1)
	}
	err = o.igCountModel.UpdateCountInfo(req.IgId, opUser.OpIgInfo.Status, umap)
	if err != nil {
		return nil, err
	}

	return nil, nil
}
