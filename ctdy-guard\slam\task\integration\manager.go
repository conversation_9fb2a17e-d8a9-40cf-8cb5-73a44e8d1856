/*
 * @Author: lwq lvwenqi<PERSON>@kylinos.cn
 * @Date: 2025-07-08
 * @LastEditors: lwq <EMAIL>
 * @LastEditTime: 2025-07-31
 * @FilePath: /ctdy-guard/slam/task/integration/manager.go
 * @Description: 集成单流程管理，权限控制，节点流转，操作执行
 * @Copyright (c) 2025 by 2022.kylinos.cn, All Rights Reserved.
 */
package integration

import (
	"errors"
	"fmt"

	"slam/global"
	"slam/model"
	"slam/slog"
	"slam/task"
	"slam/task/integration/node"
	"slam/task/integration/option"
	"slam/task/integration/role"

	"github.com/gin-gonic/gin"
)

var (
	// 这个需要从配置文件中获取，需指定角色ID
	ReposManagerRoleId        = 1 // 假设用户角色ID:1 是仓库管理员角色ID
	ArchitectureManagerRoleId = 2 // 假设用户角色ID:2 是产品架构负责人角色ID
	TechnicalManagerRoleId    = 3 // 假设用户角色ID:3 是技术经理角色ID
	ProjectManagerRoleId      = 4 // 假设用户角色ID:4 是项目经理角色ID

	SkipCheckAccess = -1 // 跳过节点ID，表示不进行节点权限检查
)

type Option struct {
	IgID     int      // 集成单ID
	NodeID   int      // 操作节点ID,必须与集成单状态一致
	OptionID int      // 操作ID
	Pusers   PuserMap // 产品上挂载的用户ID
}

type PmInterface interface {
	ExecuteOption(c *gin.Context, userOp *Option, params any) (any, error)
}

type ProcessManager struct {
	igModel model.IntegrationRepo // 集成单模型
	nodes   map[int]node.NodeInterface
	roles   map[int]role.RoleInterface
	options map[int]option.OptionInterface
}

// NewProcessManager 创建流程管理器实例
func NewProcessManager() *ProcessManager {
	return &ProcessManager{
		igModel: model.NewIntegrationModel(),
		nodes:   node.GetNodes(),
		roles:   role.GetRoles(),
		options: option.GetOptions(),
	}
}

/**
 * @author: lwq <EMAIL>
 * @description: 所有受控操作的执行入口
 * @param {*gin.Context} c
 * @param {*Option} userOp
 * @param {any} params
 * @return {*}
 */
func (pm *ProcessManager) ExecuteOption(c *gin.Context, userOp *Option, params any) (any, error) {
	user, err := pm.getOpUser(c)
	if err != nil {
		return nil, err
	}

	// 如果是创建则不需要查询集成单信息
	if userOp.OptionID == option.CreateOptionID {
		userOp.NodeID = global.INTEGRATION_STATUS_COMPARE // 创建的时候指定集成单状态为校对中
	} else {
		// 非创建操作，则集成单ID不能为空
		if userOp.IgID <= 0 {
			return nil, fmt.Errorf("集成单ID不能为空")
		}
		// 获取集成单信息
		integration, err := pm.igModel.GetIntegrationById(userOp.IgID)
		if err != nil {
			return nil, fmt.Errorf("查询集成单信息失败:%w", err)
		}

		// 集成单已锁定，非管理员用户不能操作
		if integration.LockStatus == global.INTEGRATION_LOCKED && user.RoleId != ReposManagerRoleId {
			return nil, fmt.Errorf("集成单已锁定，无法操作")
		}
		user.OpIgInfo = integration

		// 如果没设置跳过该节点，则设置当前节点
		if userOp.NodeID != SkipCheckAccess {
			userOp.NodeID = integration.Status // 获取当前集成单节点
		}
	}

	// 基础检查权限
	if err := pm.CheckAccess(user, userOp); err != nil {
		slog.ErrorWithContext(c, "权限校验失败", err)
		return nil, fmt.Errorf("您无权操作")
	}

	// 判断该用户能否执行某一条数据
	if err := pm.canDoExcute(user, userOp); err != nil {
		slog.ErrorWithContext(c, "该用户不能执行操作", err)
		return nil, err
	}

	// 执行操作
	op := pm.options[userOp.OptionID]
	res, err := op.Execute(c, user, params)
	if err != nil {
		return nil, fmt.Errorf("操作失败:%w", err)
	}
	// 记录操作日志
	slog.InfoWithContext(c, fmt.Sprintf("用户 %s 执行了操作 %s", user.UserName, op.GetName()))
	return res, err
}

/**
 * @author: lwq <EMAIL>
 * @description: 检查角色是否有权限在指定节点执行指定操作
 * @param {*IgUser} user
 * @param {*Option} option
 * @return {*}
 */
func (pm *ProcessManager) CheckAccess(user *option.OpUser, option *Option) error {
	// 如果不跳过校验
	if option.NodeID >= 0 {
		// 判断角色能否执行操作
		r, e := pm.roles[user.RoleId]
		if !e {
			return fmt.Errorf("角色 %d 不存在", user.RoleId)
		}
		// 判断操作是否有效
		op, e := pm.options[option.OptionID]
		if !e {
			return fmt.Errorf("无效的操作 %d", user.RoleId)
		}

		// 检查角色是否有权限执行操作
		if !r.CheckAccess(option.OptionID) {
			return fmt.Errorf("角色 %d 无权执行操作 %s", user.RoleId, op.GetName())
		}

		n, e := pm.nodes[option.NodeID]
		if !e {
			return fmt.Errorf("无效的节点 %d", option.NodeID)
		}
		// 检查节点是否允许执行操作
		if !n.CheckAccess(option.OptionID) {
			return fmt.Errorf("角色 %d 在节点 %s 上无权执行操作 %s", user.RoleId, n.GetName(), op.GetName())
		}
	}

	return nil
}

/**
 * @author: lwq <EMAIL>
 * @description: 获取当前用户信息
 * @param {*gin.Context} c
 * @return {*}
 */
func (i *ProcessManager) getOpUser(c *gin.Context) (*option.OpUser, error) {
	// 从上下文中获取用户信息
	res, _, userItem := task.GetUserInfo(c)
	if !res {
		slog.ErrorWithContext(c, "获取用户信息失败")
		return nil, errors.New("获取用户信息失败")
	}
	// 初始化集成单用户信息
	opUser := &option.OpUser{
		UserInfo: option.UserInfo{
			UserId:   *userItem.Id,
			UserName: userItem.Nickname,
			RoleId:   0,
			RoleName: userItem.Nickname,
			Email:    userItem.Email,
		},
	}
	// 判断仓库管理员
	if userItem.Role_id == ReposManagerRoleId {
		opUser.RoleId = role.RepoManagerRoleID
	}
	// 判断项目经理
	if userItem.Role_id == ProjectManagerRoleId {
		opUser.RoleId = role.ProjectManagerRoleID
	}
	// 判断技术经理
	if userItem.Role_id == TechnicalManagerRoleId {
		opUser.RoleId = role.TechManagerRoleID
	}
	// 判断架构负责人
	if userItem.Role_id == ArchitectureManagerRoleId {
		opUser.RoleId = role.ArchitectRoleID
	}

	return opUser, nil
}

/**
 * @author: lwq <EMAIL>
 * @description: 校验用户是否能执行操作某一条数据
 * @description: 根据产品表上挂载角色的用户ID进行校验，只有PuserMap不为空且用户ID在PuserMap中存在
 * @param {*option.OpUser} opUser
 * @param {PuserMap} pUserMap
 * @return {*}
 */
func (i *ProcessManager) canDoExcute(opUser *option.OpUser, op *Option) error {
	if op.NodeID == SkipCheckAccess {
		return nil
	}

	//如果没有关联的产品角色，那么说明只是操作的集成单
	if op.Pusers != nil {
		if op.Pusers[opUser.RoleId] != opUser.UserId {
			return fmt.Errorf("您不是产品相关角色，无法操作")
		}
	}
	return nil
}
