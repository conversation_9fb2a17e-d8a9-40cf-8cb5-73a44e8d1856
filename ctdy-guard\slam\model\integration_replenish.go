/*
 * @Author: lwq <EMAIL>
 * @Date: 2025-04-30
 * @LastEditors: lwq <EMAIL>
 * @LastEditTime: 2025-07-24
 * @FilePath: /ctdy-guard/slam/model/integration_replenish.go
 * @Description: 集成单模型
 * @Copyright (c) 2025 by 2022.kylinos.cn, All Rights Reserved.
 */
package model

import (
	"time"

	"gorm.io/gorm"
)

type IntegrationReplenish struct {
	Id        int       `gorm:"column:id;primaryKey;autoIncrement:true;type:serial4;not null" json:"id"`
	SrcName   string    `gorm:"column:src_name;type:varchar(255);not null;default:''" json:"src_name" label:"源码包名"`
	BugInfo   string    `gorm:"column:bug_info" json:"bug_info" label:"漏洞信息"`
	CveInfo   string    `gorm:"column:cve_info" json:"cve_info" label:"CVE信息"`
	CreatedAt time.Time `gorm:"column:create_time" json:"create_time"`
	UpdatedAt time.Time `gorm:"column:update_time" json:"update_time"`
}

func (*IntegrationReplenish) TableName() string {
	return "slam_integration_replenish"
}

type IntegrationReplenishRepo interface {
	/**
	 * @author: lwq <EMAIL>
	 * @description: 批量插入补丁数据
	 * @param {[]*IntegrationReplenish} rows
	 * @return {error}
	 */
	BatchInsertReplenish(rows []*IntegrationReplenish) error
}

type IntegrationReplenishModel struct {
	db *gorm.DB
}

func NewIntegrationReplenishModel() *IntegrationReplenishModel {
	return &IntegrationReplenishModel{
		db: GetDB(),
	}
}

func (m *IntegrationReplenishModel) BatchInsertReplenish(rows []*IntegrationReplenish) error {
	return m.db.Create(&rows).Error
}
