/*
 * @Author: lwq <EMAIL>
 * @Date: 2025-07-08
 * @LastEditors: lwq <EMAIL>
 * @LastEditTime: 2025-07-16
 * @FilePath: /ctdy-guard/slam/task/integration/option/test_result.go
 * @Description: 集成操作
 * @Copyright (c) 2025 by 2022.kylinos.cn, All Rights Reserved.
 */
package option

import "slam/model"

// TestResultOption 添加测试结果操作
type TestResultOption struct {
	CheckOption
}

func NewTestResultOption() *TestResultOption {
	return &TestResultOption{
		CheckOption: CheckOption{
			BaseOption: BaseOption{
				ID:   "test_result",
				Name: "添加测试结果",
			},
			igCheckModel: model.NewIntegrationCheckModel(),
			igCountModel: model.NewIntegrationCountModel(),
			bugModel:     model.NewIntegrationBugModel(),
			cveModel:     model.NewIntegrationCveModel(),
		},
	}
}
