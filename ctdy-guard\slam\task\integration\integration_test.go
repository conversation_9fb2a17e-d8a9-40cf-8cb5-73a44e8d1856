package integration

import (
	"errors"
	"slam/global"
	"slam/model"
	"slam/task/integration/option"
	"slam/task/integration/role"
	"slam/utils"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
)

func TestFlushBuildInfo(t *testing.T) {
	// 初始化mock对象
	mockBug := new(MockIntegrationRowsRepo)
	mockCve := new(MockIntegrationRowsRepo)
	mockXflow := new(MockXfllowModelRepo)
	mockRpm := new(MockIntegrationRpmRepo)

	// 创建Integration实例
	i := &Integration{
		igBugModel: mockBug,
		igCveModel: mockCve,
		xflowModel: mockXflow,
		igRpmModel: mockRpm,
	}

	// 测试用例1：正常场景
	t.Run("success case", func(t *testing.T) {
		// 准备测试数据
		igid := 1
		xflowIdsForBug := []int{21037}
		xflowIdsForCve := []int{}
		buildMappings := []*model.BuildMapping{{
			XflowId: 21037, SrcName: "moby-engine-27.4.1-1.ky10.src.rpm",
			IpAddress: "http://************:8238",
		}}

		// 设置mock期望
		mockBug.On("GetXflowIdsByIgid", igid).Return(xflowIdsForBug, nil)
		mockCve.On("GetXflowIdsByIgid", igid).Return(xflowIdsForCve, nil)
		mockXflow.On("GetBuildInfosByXflowIds", mock.Anything).Return(buildMappings, nil)
		mockRpm.On("BatchInsertIntegrationBuild", mock.Anything).Return(nil)

		// 执行测试
		i.FlushBuildInfo(igid)

		// 验证结果
		mockBug.AssertExpectations(t)
		mockCve.AssertExpectations(t)
		mockXflow.AssertExpectations(t)
		mockRpm.AssertExpectations(t)
	})

	// 测试用例2：空列表场景
	t.Run("empty list case", func(t *testing.T) {
		// 准备测试数据
		igid := 1

		// 设置mock期望
		mockBug.On("GetXflowIdsByIgid", igid).Return([]int{}, nil)
		mockCve.On("GetXflowIdsByIgid", igid).Return([]int{}, nil)

		// 执行测试
		i.FlushBuildInfo(igid)

		// 验证结果
		mockBug.AssertExpectations(t)
		mockCve.AssertExpectations(t)
	})
}

func TestNewIntegration(t *testing.T) {
	NewIntegration()
}

// TestGetIntegrationList tests the GetIntegrationList method
func TestIntegrationGetIntegrationList(t *testing.T) {
	mockIgModel := new(MockIntegrationRepo)
	i := &Integration{
		igModel: mockIgModel,
	}

	pager := utils.Pager{
		Index: 1,
		Size:  10,
	}

	req := PacksListReq{
		IgId:    1,
		SrcName: "test-package1",
	}

	expectedResult := &model.Data_List{
		List: []any{
			&model.Integration{Id: 1, AliasName: "Integration 1"},
			&model.Integration{Id: 2, AliasName: "Integration 2"},
		},
		Page: model.PageRes{
			Page_index: 1,
			Page_size:  10,
			Total:      2,
		},
	}

	t.Run("successful retrieval", func(t *testing.T) {
		// Mock expectations
		mockIgModel.On("GetIntegrationList", pager, mock.MatchedBy(func(mReq *model.PacksListReq) bool {
			return mReq.IgId == req.IgId && mReq.SrcName == req.SrcName
		})).Return(expectedResult, nil)

		// Execute
		result, err := i.GetIntegrationList(pager, req)

		// Assertions
		assert.NoError(t, err)
		assert.Equal(t, expectedResult, result)
		mockIgModel.AssertExpectations(t)
	})
}

func TestGetIntegrationById(t *testing.T) {
	// type fields struct {
	// 	ctx context.Context
	// }
	type igRes struct {
		ig      *model.Integration
		wantErr bool
		err     error
	}
	type countRes struct {
		count   *model.IntegrationCount
		wantErr bool
		err     error
	}
	type args struct {
		in       int
		igRes    igRes
		countRes countRes
	}
	tests := []struct {
		name string
		args args
	}{
		{"TestGetIntegrationById_Success",
			args{
				in: 1,
				igRes: igRes{
					ig:  &model.Integration{Id: 1, Status: 1},
					err: nil, wantErr: false,
				},
				countRes: countRes{
					count: &model.IntegrationCount{Id: 1},
					err:   nil, wantErr: false,
				},
			},
		},
		{"TestGetIntegrationById_Failure",
			args{
				in: 1,
				igRes: igRes{
					ig:  &model.Integration{Id: 1, Status: 1},
					err: errors.New(""), wantErr: true,
				},
				countRes: countRes{
					count: &model.IntegrationCount{Id: 1},
					err:   nil, wantErr: false,
				},
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mockIgModel := new(MockIntegrationRepo)
			mockCountModel := new(MockIntegrationCountRepo)
			i := &Integration{
				igModel:      mockIgModel,
				igCountModel: mockCountModel,
			}
			mockIgModel.On("GetIntegrationById", tt.args.in).
				Return(tt.args.igRes.ig, tt.args.igRes.err)

			if tt.args.igRes.err == nil {
				mockCountModel.
					On("GetIntegrationCount", tt.args.igRes.ig.Id, tt.args.igRes.ig.Status).
					Return(tt.args.countRes.count, tt.args.countRes.err)
			}
			ig, err := i.GetIntegrationById(tt.args.in)
			if tt.args.igRes.wantErr || tt.args.countRes.wantErr {
				assert.NotNil(t, err)
				return
			}
			assert.NotNil(t, ig)
		})
	}
}

func TestGetIntegrationBugList(t *testing.T) {
	type args struct {
		req   PacksListReq
		pager utils.Pager
		c     *gin.Context
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		{
			name: "TestGetIntegrationBugList_Success",
			args: args{
				c: getContext(ReposManagerRoleId),
				req: PacksListReq{
					IgId:    1,
					SrcName: "test-package-2",
				},
				pager: utils.Pager{
					Index: 1,
					Size:  10,
				},
			},
			wantErr: false,
		},
		{
			name: "TestGetIntegrationBugList_WithUid",
			args: args{
				c: getContext(ReposManagerRoleId),
				req: PacksListReq{
					IgId:    1,
					SrcName: "test-package-3",
					Uid:     1,
				},
				pager: utils.Pager{
					Index: 1,
					Size:  10,
				},
			},
			wantErr: false,
		},
		{
			name: "TestGetIntegrationBugList_Error",
			args: args{
				c: getContext(ReposManagerRoleId),
				req: PacksListReq{
					IgId:    1,
					SrcName: "test-package-4",
				},
				pager: utils.Pager{
					Index: 1,
					Size:  10,
				},
			},
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 初始化mock对象
			mockBugModel := new(MockIntegrationRowsRepo)
			i := &Integration{
				igBugModel: mockBugModel,
			}

			// 设置mock期望
			if tt.wantErr {
				mockBugModel.On("GetIntegrationList", mock.AnythingOfType("*model.PacksListReq"), tt.args.pager).
					Return(nil, int64(0), errors.New("database error"))
			} else {
				expectedResult := []*model.IntegrationBugWithRpms{
					{
						IntegrationBug: model.IntegrationBug{
							Id:        1,
							ProductId: 1,
							ZentaoNo:  "001",
						},
					},
				}
				mockBugModel.On("GetIntegrationList", mock.AnythingOfType("*model.PacksListReq"), tt.args.pager).
					Return(expectedResult, int64(1), nil)
			}

			// 执行测试
			result, err := i.GetIntegrationBugList(tt.args.c, tt.args.req, tt.args.pager)

			// 验证结果
			if tt.wantErr {
				assert.NotNil(t, err)
				assert.Nil(t, result)
			} else {
				assert.Nil(t, err)
				assert.NotNil(t, result)
				assert.Equal(t, int64(1), result.Page.Total)
				mockBugModel.AssertExpectations(t)
			}
		})
	}
}

func TestGetIntegrationCveList(t *testing.T) {
	type args struct {
		req   PacksListReq
		pager utils.Pager
		c     *gin.Context
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		{
			name: "TestGetIntegrationCveList_Success",
			args: args{
				c: getContext(ReposManagerRoleId),
				req: PacksListReq{
					IgId:    1,
					SrcName: "test-package-2",
				},
				pager: utils.Pager{
					Index: 1,
					Size:  10,
				},
			},
			wantErr: false,
		},
		{
			name: "TestGetIntegrationCveList_WithUid",
			args: args{
				c: getContext(ReposManagerRoleId),
				req: PacksListReq{
					IgId:    1,
					SrcName: "test-package-3",
					Uid:     1,
				},
				pager: utils.Pager{
					Index: 1,
					Size:  10,
				},
			},
			wantErr: false,
		},
		{
			name: "TestGetIntegrationCveList_Error",
			args: args{
				c: getContext(ReposManagerRoleId),
				req: PacksListReq{
					IgId:    1,
					SrcName: "test-package-4",
				},
				pager: utils.Pager{
					Index: 1,
					Size:  10,
				},
			},
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 初始化mock对象
			mockCveModel := new(MockIntegrationRowsRepo)
			i := &Integration{
				igCveModel: mockCveModel,
			}

			// 设置mock期望
			if tt.wantErr {
				mockCveModel.On("GetIntegrationList", mock.AnythingOfType("*model.PacksListReq"), tt.args.pager).
					Return(nil, int64(0), errors.New("database error"))
			} else {
				expectedResult := []*model.IntegrationCveWithRpms{
					{
						IntegrationCve: model.IntegrationCve{
							Id:        1,
							ProductId: 1,
							CveId:     "CVE-2025-0001",
						},
					},
				}
				mockCveModel.On("GetIntegrationList", mock.AnythingOfType("*model.PacksListReq"), tt.args.pager).
					Return(expectedResult, int64(1), nil)
			}

			// 执行测试
			result, err := i.GetIntegrationCveList(tt.args.c, tt.args.req, tt.args.pager)

			// 验证结果
			if tt.wantErr {
				assert.NotNil(t, err)
				assert.Nil(t, result)
			} else {
				assert.Nil(t, err)
				assert.NotNil(t, result)
				assert.Equal(t, int64(1), result.Page.Total)
				mockCveModel.AssertExpectations(t)
			}
		})
	}
}

func TestIntegrationSimpleOperations(t *testing.T) {
	tests := []struct {
		name            string
		executeFunc     func(*Integration, *gin.Context) (any, error)
		op              *Option
		expectedResult  any
		skipCheckAccess bool
		roleId          int
	}{
		{
			name: "CreateIntegration",
			executeFunc: func(i *Integration, c *gin.Context) (any, error) {
				return i.CreateIntegration(c)
			},
			op:             &Option{OptionID: option.CreateOptionID},
			expectedResult: &model.Integration{},
			roleId:         ReposManagerRoleId,
		},
		{
			name: "EditIntegration",
			executeFunc: func(i *Integration, c *gin.Context) (any, error) {
				req := &option.EditIgReq{
					IgId:   1,
					IgName: "Updated Name",
					Remark: "Updated Remark",
				}
				return i.EditIntegration(c, req)
			},
			op: &Option{
				IgID:     1,
				OptionID: option.UpdateOptionID,
				NodeID:   SkipCheckAccess,
			},
			expectedResult: "success",
			roleId:         ReposManagerRoleId,
		},
		{
			name: "RotateStatus",
			executeFunc: func(i *Integration, c *gin.Context) (interface{}, error) {
				igId := 1
				return i.RotateStatus(c, igId)
			},
			op: &Option{
				IgID:     1,
				OptionID: option.RotateOptionID,
			},
			expectedResult: "success",
			roleId:         ReposManagerRoleId,
		},
		{
			name: "LockIntegration",
			executeFunc: func(i *Integration, c *gin.Context) (interface{}, error) {
				return i.LockIntegration(c, 1, global.INTEGRATION_LOCKED)
			},
			op: &Option{
				IgID:     1,
				OptionID: option.LockOptionID,
				NodeID:   SkipCheckAccess,
			},
			expectedResult: "success",
			roleId:         ReposManagerRoleId,
		},
		{
			name: "UnLockIntegration",
			executeFunc: func(i *Integration, c *gin.Context) (interface{}, error) {
				return i.LockIntegration(c, 1, global.INTEGRATION_UNLOCK)
			},
			op: &Option{
				IgID:     1,
				OptionID: option.UnlockOptionID,
				NodeID:   SkipCheckAccess,
			},
			expectedResult: "success",
			roleId:         ReposManagerRoleId,
		},
		{
			name: "ReplenishData",
			executeFunc: func(i *Integration, c *gin.Context) (interface{}, error) {
				req := &option.ReplenishReq{
					IgId:    1,
					SrcName: "test-package",
					BugInfo: `[{"id": "bug1"}]`,
					CveInfo: `[{"id": "cve1"}]`,
				}
				return i.ReplenishData(c, req)
			},
			op: &Option{
				IgID:     1,
				OptionID: option.ReplenishOptionID,
			},
			expectedResult: "success",
			roleId:         ReposManagerRoleId,
		},
		{
			name: "FlagPublishStatus",
			executeFunc: func(i *Integration, c *gin.Context) (any, error) {
				req := &option.CheckReq{
					Id:          1,
					IgId:        1,
					CheckResult: 1,
					CheckAdvice: "",
					CheckType:   1,
				}
				return i.FlagPublishStatus(c, req)
			},
			op: &Option{
				IgID:     1,
				OptionID: option.FlagOptionID,
			},
			expectedResult: "success",
			roleId:         ReposManagerRoleId,
		},
		{
			name: "ExportIntegration",
			executeFunc: func(i *Integration, c *gin.Context) (any, error) {
				return i.ExportIntegration(c, 1)
			},
			op: &Option{
				IgID:     1,
				OptionID: option.DownloadOptionID,
			},
			expectedResult: "success",
			roleId:         ReposManagerRoleId,
		},
		{
			name: "AddPackage",
			executeFunc: func(i *Integration, c *gin.Context) (any, error) {
				return i.AddPackage(c, 1, 1)
			},
			op: &Option{
				IgID:     1,
				OptionID: option.AddOptionId,
			},
			expectedResult: "success",
			roleId:         ReposManagerRoleId,
		},
	}
	//ReposManagerRoleId
	for _, tt := range tests {
		mockPm := new(MockProcessManager)
		i := &Integration{
			pm: mockPm,
		}
		ginContext := getContext(tt.roleId)
		mockPm.On("ExecuteOption", ginContext, tt.op, mock.Anything).Return(tt.expectedResult, nil)

		result, err := tt.executeFunc(i, ginContext)
		assert.NoError(t, err)
		assert.Equal(t, result, tt.expectedResult)

		mockPm.AssertExpectations(t)
	}
}

func TestReplenishPackages(t *testing.T) {
	// 创建mock对象
	mockBugModel := new(MockIntegrationRowsRepo)
	mockCveModel := new(MockIntegrationRowsRepo)
	mockXflowModel := new(MockXfllowModelRepo)
	i := &Integration{
		igBugModel: mockBugModel,
		igCveModel: mockCveModel,
		xflowModel: mockXflowModel,
	}
	ginContext := getContext(ReposManagerRoleId)
	mockBugModel.On("GetXflowIdsByIgid", 1).Return([]int{1}, nil)
	mockCveModel.On("GetXflowIdsByIgid", 1).Return([]int{2}, nil)
	mockXflowModel.On("GetNeedReplenishPackages", 1, []int{1, 2}, "").Return([]string{}, nil)

	i.ReplenishPackages(ginContext, 1, "")
}

func TestIntegrationOperations(t *testing.T) {
	// 定义测试用例
	testCases := []struct {
		name       string
		optionID   int
		autoRotate bool
		testFunc   func(*Integration, *gin.Context, *CheckReq) (*BatchCheckRes, error)
	}{
		{
			name:       "TestReviewIntegration",
			optionID:   option.ReviewOptionID,
			autoRotate: true,
			testFunc: func(i *Integration, c *gin.Context, req *CheckReq) (*BatchCheckRes, error) {
				return i.ReviewIntegration(c, req)
			},
		},
		{
			name:       "TestCheckIntegration",
			optionID:   option.CheckOptionID,
			autoRotate: true,
			testFunc: func(i *Integration, c *gin.Context, req *CheckReq) (*BatchCheckRes, error) {
				return i.CheckIntegration(c, req)
			},
		},
		{
			name:       "TestAddTestIntegration",
			optionID:   option.TestResultOptionID,
			autoRotate: true,
			testFunc: func(i *Integration, c *gin.Context, req *CheckReq) (*BatchCheckRes, error) {
				return i.AddTestResult(c, req)
			},
		},
		{
			name:       "TestRecheckIntegration",
			optionID:   option.ReCheckOptionID,
			autoRotate: false,
			testFunc: func(i *Integration, c *gin.Context, req *CheckReq) (*BatchCheckRes, error) {
				return i.RecheckIntegration(c, req)
			},
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			// 创建mock对象
			mockPm := new(MockProcessManager)
			mockBugModel := new(MockIntegrationRowsRepo)

			// 创建Integration实例
			integration := &Integration{
				pm:         mockPm,
				igBugModel: mockBugModel,
			}

			// 设置期望值
			ginContext := getContext(ReposManagerRoleId)
			req := &CheckReq{
				Ids:         []int{1},
				IgId:        1,
				CheckResult: 1,
				CheckAdvice: "Approved",
				CheckType:   model.IntegrationCheckTypeBug,
			}

			// Mocks for batchExecuteCheck
			pids := map[int]int{1: 10} // id -> product_id
			mockBugModel.On("GetProductListByIgid", req.IgId).Return(pids, nil)

			// Mock product list
			mockFuncsModel := new(MockFuncsModel)
			integration.funcsModel = mockFuncsModel
			pid := 10
			productList := []model.ProductList{
				{
					Id:                  &pid,
					ArchitectureManager: 101,
					TechnicalManager:    102,
					ProjectManager:      103,
				},
			}
			mockFuncsModel.On("ProductList", []int{10}).Return(productList)

			// 对于Review操作，还需要mock自动rotate调用
			// Mock batchExecuteCheck calls
			mockPm.On("ExecuteOption", ginContext, &Option{
				IgID:     req.IgId,
				OptionID: tc.optionID,
				Pusers:   PuserMap{role.ArchitectRoleID: 101, role.TechManagerRoleID: 102, role.ProjectManagerRoleID: 103},
			}, &option.CheckReq{
				Id:          1,
				IgId:        req.IgId,
				CheckResult: req.CheckResult,
				CheckAdvice: req.CheckAdvice,
				CheckType:   req.CheckType,
			}).Return(nil, nil)

			if tc.autoRotate {
				// Mock the automatic rotate call
				mockPm.On("ExecuteOption", ginContext, &Option{
					IgID:     req.IgId,
					OptionID: option.RotateOptionID,
					NodeID:   SkipCheckAccess,
					Pusers:   PuserMap{role.ArchitectRoleID: 101, role.TechManagerRoleID: 102, role.ProjectManagerRoleID: 103},
				}, nil).Return(nil, nil)
			}

			// 执行测试
			result, err := tc.testFunc(integration, ginContext, req)

			// 验证结果
			assert.NoError(t, err)
			assert.NotNil(t, result)
			assert.Equal(t, 1, result.SuccessNum)
			assert.Equal(t, 0, result.FailNum)
			mockPm.AssertExpectations(t)
			mockBugModel.AssertExpectations(t)
			mockFuncsModel.AssertExpectations(t)
		})
	}
}
