package service

import (
	"container/heap"
	"container/list"
	"context"
	"fmt"
	"net/url"
	"slam-hook/logs"
	"slam-hook/model"
	"sync"
	"sync/atomic"
	"time"
)

// 配置常量
const (
	HighPriority = 2 // 高优先级任务级别
	LowPriority  = 1 // 低优先级任务级别
)

// Task 任务结构体
type Task struct {
	ID        int                // 任务唯一标识
	Priority  int                // 任务优先级(HighPriority/LowPriority)
	Data      model.HookTaskList // 任务数据（步骤一使用）
	Step2Data model.HookTaskList // 步骤二任务数据
}

// Step1Queue 步骤一优先级队列(大顶堆实现)
type Step1Queue []*Task

func (h Step1Queue) Len() int            { return len(h) }
func (h Step1Queue) Less(i, j int) bool  { return h[i].Priority > h[j].Priority }
func (h Step1Queue) Swap(i, j int)       { h[i], h[j] = h[j], h[i] }
func (h *Step1Queue) Push(x interface{}) { *h = append(*h, x.(*Task)) }
func (h *Step1Queue) Pop() interface{} {
	old := *h
	n := len(old)
	item := old[n-1]
	*h = old[0 : n-1]
	return item
}

// Step2Queue 步骤二FIFO队列
type Step2Queue struct {
	mu   sync.Mutex
	list *list.List
}

func NewStep2Queue() *Step2Queue {
	return &Step2Queue{list: list.New()}
}

func (q *Step2Queue) Enqueue(task *Task) {
	q.mu.Lock()
	defer q.mu.Unlock()
	q.list.PushBack(task)
}

func (q *Step2Queue) Dequeue() *Task {
	q.mu.Lock()
	defer q.mu.Unlock()
	if q.list.Len() == 0 {
		return nil
	}
	return q.list.Remove(q.list.Front()).(*Task)
}

func (q *Step2Queue) Len() int {
	q.mu.Lock()
	defer q.mu.Unlock()
	return q.list.Len()
}

// TaskSystem 任务系统
type TaskSystem struct {
	step1Queue      *Step1Queue
	step1Cond       *sync.Cond
	step2Queue      *Step2Queue
	step2Cond       *sync.Cond
	ctx             context.Context
	cancel          context.CancelFunc
	maxStep1Workers int
	maxStep2Workers int

	// 统计指标
	totalTasks     int64
	processedStep1 int64
	processedStep2 int64
	failedStep1    int64
	failedStep2    int64
	rateLimited    int64
}

// NewTaskSystem 创建任务系统
func NewTaskSystem(maxStep1, maxStep2 int) *TaskSystem {
	ctx, cancel := context.WithCancel(context.Background())
	return &TaskSystem{
		step1Queue:      &Step1Queue{},
		step1Cond:       sync.NewCond(&sync.Mutex{}),
		step2Queue:      NewStep2Queue(),
		step2Cond:       sync.NewCond(&sync.Mutex{}),
		ctx:             ctx,
		cancel:          cancel,
		maxStep1Workers: maxStep1,
		maxStep2Workers: maxStep2,
	}
}

// AddTask 添加任务到步骤一队列
func (s *TaskSystem) AddTask(task *Task) {
	s.step1Cond.L.Lock()
	heap.Push(s.step1Queue, task)
	atomic.AddInt64(&s.totalTasks, 1)
	s.step1Cond.L.Unlock()
	s.step1Cond.Signal()
	logs.Log.Infof("任务 %d 已添加到步骤一队列 (优先级:%d)", task.ID, task.Priority)
}

// Start 启动系统
func (s *TaskSystem) Start() {
	// 启动步骤一worker
	for i := 0; i < s.maxStep1Workers; i++ {
		go s.step1Worker(i)
	}

	// 启动步骤二worker
	for i := 0; i < s.maxStep2Workers; i++ {
		go s.step2Worker(i)
	}

	logs.Log.Infof("系统启动: 步骤一worker=%d, 步骤二worker=%d", s.maxStep1Workers, s.maxStep2Workers)
}

// Stop 停止系统
func (s *TaskSystem) Stop() {
	s.cancel()
	s.step1Cond.Broadcast()
	s.step2Cond.Broadcast()
	logs.Log.Info("系统已停止")
}

func (s *TaskSystem) step1Worker(id int) {
	for {
		select {
		case <-s.ctx.Done():
			return
		default:
			s.step1Cond.L.Lock()
			for s.step1Queue.Len() == 0 {
				s.step1Cond.Wait()
				if s.ctx.Err() != nil {
					s.step1Cond.L.Unlock()
					return
				}
			}

			task := heap.Pop(s.step1Queue).(*Task)
			s.step1Cond.L.Unlock()

			// 低优先级任务限流检查
			if task.Priority == LowPriority && rateLimitFunc(task) {
				atomic.AddInt64(&s.rateLimited, 1)
				logs.Log.Infof("Step1Worker-%d: 任务 %d 被限流", id, task.ID)

				s.step1Cond.L.Lock()
				heap.Push(s.step1Queue, task)
				s.step1Cond.L.Unlock()
				time.Sleep(1 * time.Second)
				continue
			}

			// 处理步骤一
			err := processHookTask(task)
			if err != nil {
				atomic.AddInt64(&s.failedStep1, 1)
				logs.Log.Errorf("Step1Worker-%d: 任务 %d 步骤一失败: %v", id, task.ID, err)

				s.step1Cond.L.Lock()
				heap.Push(s.step1Queue, task)
				s.step1Cond.L.Unlock()
				continue
			}

			atomic.AddInt64(&s.processedStep1, 1)
			logs.Log.Infof("Step1Worker-%d: 完成任务 %d 步骤一", id, task.ID)
		}
	}
}

func (s *TaskSystem) step2Worker(id int) {
	for {
		select {
		case <-s.ctx.Done():
			return
		default:
			s.step2Cond.L.Lock()
			for s.step2Queue.Len() == 0 {
				s.step2Cond.Wait()
				if s.ctx.Err() != nil {
					s.step2Cond.L.Unlock()
					return
				}
			}

			task := s.step2Queue.Dequeue()
			s.step2Cond.L.Unlock()

			//// 处理步骤二
			err := deliverSingleTask(task)
			if err != nil {
				atomic.AddInt64(&s.failedStep2, 1)
				logs.Log.Errorf("Step2Worker-%d: 任务 %v 步骤二失败: %v", id, task.Step2Data, err)
				continue
			}

			atomic.AddInt64(&s.processedStep2, 1)
			logs.Log.Infof("Step2Worker-%d: 完成任务 %v 步骤二", id, task.Step2Data)
		}
	}
}

// Stats 获取统计信息
func (s *TaskSystem) Stats() map[string]int64 {
	s.step1Cond.L.Lock()
	defer s.step1Cond.L.Unlock()

	return map[string]int64{
		"total_tasks":     atomic.LoadInt64(&s.totalTasks),
		"processed_step1": atomic.LoadInt64(&s.processedStep1),
		"processed_step2": atomic.LoadInt64(&s.processedStep2),
		"failed_step1":    atomic.LoadInt64(&s.failedStep1),
		"failed_step2":    atomic.LoadInt64(&s.failedStep2),
		"rate_limited":    atomic.LoadInt64(&s.rateLimited),
		"step1_queue":     int64(s.step1Queue.Len()),
		"step2_queue":     int64(s.step2Queue.Len()),
	}
}

func rateLimitFunc(task *Task) bool {
	// 模拟限流函数
	kojiUrl := task.Data.KojiAddr
	if kojiUrl == "" {
		kojiUrl = task.Data.TagKoji
	}
	parsedURL, err := url.Parse(kojiUrl)
	if err != nil {
		logs.Log.Error("解析URL失败:", err)
		return false
	}
	baseUrl := fmt.Sprintf("%s://%s", parsedURL.Scheme, parsedURL.Host)
	kojiUsage := KojiUsage[baseUrl]
	if kojiUsage >= 0.6 {
		return false
	}
	return true
}
