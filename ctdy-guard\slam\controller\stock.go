package controller

import (
	"fmt"
	"slam/global"
	"slam/model"
	"slam/slog"
	"slam/task"
	"slam/utils"
	"strconv"
	"strings"
	"time"

	"github.com/cavaliergopher/rpm"
)

func StockContextUpdate(cont *model.StockContext, flow_type string) bool {
	if cont == nil {
		slog.Error.Println("StockContextUpdate failed, cont is nil")
		return false
	}
	cont.Current_flow = flow_type
	ret := model.UpdateStockContext(cont)
	if !ret {
		slog.Error.Println("StockContextUpdate UpdateStockContext failed:", cont.Xflow_id, flow_type)
		return false
	}
	//发送蓝信通知
	StockMsgSend(cont, flow_type)

	return true
}

func StockMsgSend(cont *model.StockContext, flow_type string) bool {
	//向提交人发送状态变更提醒
	if flow_type == global.STOCK_FLOW_SUBMIT ||
		flow_type == global.STOCK_FLOW_SCRATCH_BUILD ||
		flow_type == global.STOCK_FLOW_HOOK ||
		flow_type == global.STOCK_FLOW_EVALUATE ||
		flow_type == global.STOCK_FLOW_REVIEW ||
		flow_type == global.STOCK_FLOW_BUILD ||
		flow_type == global.STOCK_FLOW_TAG_BUILD ||
		flow_type == global.STOCK_FLOW_FINISHED {
		ret := task.MsgLxGerritToSummiter(cont.Xflow_id)
		if !ret {
			slog.Error.Println("StockMsgSend MsgLxGerritToSummiter failed:", cont.Xflow_id, flow_type)
			return false
		}
	}
	//向评估人发送评估提醒
	if flow_type == global.STOCK_FLOW_EVALUATE {
		ret := task.MsgLxGerritToEvaluater(cont.Xflow_id)
		if !ret {
			slog.Error.Println("StockMsgSend MsgLxGerritToEvaluater failed:", cont.Xflow_id, flow_type)
			return false
		}
	}
	//向审核人发送审核提醒
	if flow_type == global.STOCK_FLOW_REVIEW {
		ret := task.MsgLxGerritToReviewer(cont.Xflow_id)
		if !ret {
			slog.Error.Println("StockMsgSend MsgLxGerritToReviewer failed:", cont.Xflow_id, flow_type)
			return false
		}
	}
	//向提交人发送任务异常提醒
	if flow_type == global.STOCK_FLOW_SUBMIT_FAILED ||
		flow_type == global.STOCK_FLOW_SCRATCH_BUILD_FAILED ||
		flow_type == global.STOCK_FLOW_HOOK_FAILED ||
		flow_type == global.STOCK_FLOW_EVALUATE_DENY ||
		flow_type == global.STOCK_FLOW_REVIEW_DENY ||
		flow_type == global.STOCK_FLOW_BUILD_FAILED ||
		flow_type == global.STOCK_FLOW_TAG_BUILD_FAILED ||
		flow_type == global.STOCK_FLOW_FAILED ||
		flow_type == global.STOCK_FLOW_REVOKE {
		ret := task.MsgLxGerritFailedToSubmiter(cont.Xflow_id)
		if !ret {
			slog.Error.Println("StockMsgSend MsgLxGerritFailedToSubmiter failed:", cont.Xflow_id, flow_type)
			return false
		}
	}
	//向hook接口人发送失败提醒
	if flow_type == global.STOCK_FLOW_HOOK_FAILED {
		ret := task.MsgLxGerritToHooker(cont.Xflow_id)
		if !ret {
			slog.Error.Println("StockMsgSend MsgLxGerritToHooker failed:", cont.Xflow_id, flow_type)
			return false
		}
	}
	//向管理员发送入库失败提醒
	if flow_type == global.STOCK_FLOW_SCRATCH_BUILD_FAILED ||
		flow_type == global.STOCK_FLOW_BUILD_FAILED ||
		flow_type == global.STOCK_FLOW_TAG_BUILD_FAILED {
		ret := task.MsgLxGerritToAdmin(cont.Xflow_id)
		if !ret {
			slog.Error.Println("StockMsgSend MsgLxGerritToAdmin failed:", cont.Xflow_id, flow_type)
			return false
		}
	}

	return true
}

func XflowStockHandle(xflow *model.Xflow) bool {
	stock_context := model.GetStockContextByXflowId(*xflow.Id)
	if stock_context == nil {
		slog.Error.Printf("XflowStockHandle GetStockContextByXflowId failed, xflow_Id = %d\n", *xflow.Id)
		xflow.Status = global.XFLOW_STATUS_FAILED
		xflow.Stop_reason = fmt.Sprintf("查找xflow流程对应上下文失败, sflow_id: %d", *xflow.Id)
		ret := model.UpdateXflow(xflow)
		if !ret {
			slog.Error.Println("XflowStockHandle UpdateXflow failed:", *xflow.Id)
		}
		return false
	}

	v, ok := model.SafeGetCurrentFlow(*xflow.Id)
	if ok && v != stock_context.Current_flow {
		slog.Error.Println("XflowStockHandle Current_flow error", *xflow.Id, v, stock_context.Current_flow)
		return false
	}

	switch stock_context.Current_flow {

	case global.STOCK_FLOW_SUBMIT:
		go XflowStockSubmitProc(xflow, stock_context)

	case global.STOCK_FLOW_SCRATCH_BUILD:
		go XflowStockScratchBuildProc(xflow, stock_context)

	case global.STOCK_FLOW_SCRATCH_BUILD_RETRY:
		go XflowStockScratchBuildRetry(xflow, stock_context)

	case global.STOCK_FLOW_SCRATCH_BUILD_FAILED:
		//失败状态不做处理,等老化释放或重启
		return true

	case global.STOCK_FLOW_HOOK:
		go XflowStockHookProc(xflow, stock_context)

	case global.STOCK_FLOW_HOOK_FAILED:
		//HOOK失败状态不做处理,等老化释放
		return true

	case global.STOCK_FLOW_EVALUATE:
		go XflowStockEvaluateProc(xflow, stock_context)

	case global.STOCK_FLOW_REVIEW:
		go XflowStockReviewProc(xflow, stock_context)

	case global.STOCK_FLOW_BUILD:
		go XflowStockBuildProc(xflow, stock_context)

	case global.STOCK_FLOW_BUILD_RETRY:
		go XflowStockBuildRetryProc(xflow, stock_context)

	case global.STOCK_FLOW_BUILD_FAILED:
		//build失败状态不做处理,等老化释放或重启
		return true

	case global.STOCK_FLOW_TAG_BUILD:
		go XflowStockTagBuildProc(xflow, stock_context)

	case global.STOCK_FLOW_TAG_BUILD_RETRY:
		go XflowStockTagBuildRetry(xflow, stock_context)

	case global.STOCK_FLOW_TAG_BUILD_FAILED:
		//tag-build失败状态不做处理,等老化释放或重启
		return true

	case global.STOCK_FLOW_ARCHIVE:
		go XflowStockArchiveProc(xflow, stock_context)

	case global.STOCK_FLOW_REVOKE:
		go XflowStockRevokeProc(xflow, stock_context)

	case global.STOCK_FLOW_FAILED:
		go XflowStockFailedProc(xflow, stock_context)

	case global.STOCK_FLOW_FINISHED:
		go XflowStockFinishedProc(xflow, stock_context)

	default:
		//流程状态有误
		slog.Error.Println("XflowStockHandle failed, Current_flow error:", stock_context.Current_flow)
		//结束整个xflow流程
		xflow.Status = global.XFLOW_STATUS_FAILED
		xflow.Stop_reason = "流程状态有误"
		ret := model.UpdateXflow(xflow)
		if !ret {
			slog.Error.Printf("XflowStockHandle UpdateXflow failed %d\n", *xflow.Id)
		}
		return false
	}

	return true
}

///////////////////////////////////////////////////////////////////////////////////////////////////////////

func XflowStockSubmitProc(xflow *model.Xflow, cont *model.StockContext) {
	//创建临时下载目录
	if !task.CreateSrcTmpDirByXflowId(*xflow.Id) {
		slog.Error.Println("XflowStockSubmitProc CreateSrcTmpDirByXflowId failed, xflow_Id ", *xflow.Id)
		StockContextUpdate(cont, global.STOCK_FLOW_SUBMIT_FAILED)
		return
	}
	//先对流转状态做跳转
	StockContextUpdate(cont, global.STOCK_FLOW_SCRATCH_BUILD)

	//获取流程对应的gerrit信息
	gerritLists := model.GetGerritListByXflowId(*xflow.Id)
	if gerritLists == nil {
		slog.Error.Println("XflowStockSubmitProc GetGerritListByXflowId failed, ", *xflow.Id)
		StockContextUpdate(cont, global.STOCK_FLOW_SCRATCH_BUILD_FAILED)
		return
	}
	//多线程开启scratch编译任务
	for i := range gerritLists {
		go StockBuildScratch(&gerritLists[i], xflow, cont)
		time.Sleep(100 * time.Millisecond)
	}
}

// 开启scratch编译,并填充结构
func StockBuildScratch(gerritList *model.GerritList, xflow *model.Xflow, cont *model.StockContext) {
	kojis := task.KojiBuildScratchByGerrit(gerritList)
	if kojis == nil {
		slog.Error.Println("StockBuildScratch KojiBuildScratchByGerrit failed:", gerritList)
		StockContextUpdate(cont, global.STOCK_FLOW_SCRATCH_BUILD_FAILED)
		return
	}
	//更新gerrit信息中的koji任务信息
	ids := []int{}
	for _, koji := range kojis {
		ids = append(ids, *koji.Id)
	}
	gerritList.Kojis = ids
	ret := model.UpdateGerritList(gerritList)
	if !ret {
		slog.Error.Println("StockBuildScratch UpdateGerritList failed:", gerritList)
		StockContextUpdate(cont, global.STOCK_FLOW_SCRATCH_BUILD_FAILED)
		return
	}
}

///////////////////////////////////////////////////////////////////////////////////////////////////////////

func XflowStockScratchBuildProc(xflow *model.Xflow, cont *model.StockContext) {
	gerrits := model.GetGerritListByXflowId(*xflow.Id)
	if gerrits == nil {
		slog.Error.Println("XflowStockScratchBuildProc GetGerritListByXflowId failed, xflow_Id ", *xflow.Id)
		StockContextUpdate(cont, global.STOCK_FLOW_SCRATCH_BUILD_FAILED)
		return
	}

	var taskFailed []string
	for i := range gerrits {
		if len(gerrits[i].Kojis) == 0 {
			//任务还没有开启
			return
		}
		for _, taskId := range gerrits[i].Kojis {
			kojiTask := model.Koji.GetKojiListById(taskId)
			if kojiTask == nil {
				slog.Error.Println("XflowStockScratchBuildProc GetKojiListById failed, xflow_Id ", *xflow.Id)
				StockContextUpdate(cont, global.STOCK_FLOW_SCRATCH_BUILD_FAILED)
				return
			}
			if kojiTask.Status == global.KOJI_TASK_STATE_OPEN ||
				kojiTask.Status == global.KOJI_TASK_STATE_FREE ||
				kojiTask.Status == global.KOJI_TASK_STATE_REPO {
				//有编译任务未完成
				return
			}
			if kojiTask.Status == global.KOJI_TASK_STATE_FAILED ||
				kojiTask.Status == global.KOJI_TASK_STATE_CANCELED {
				taskFailed = append(taskFailed, kojiTask.Task_id)
			}
		}
	}

	if len(taskFailed) == 0 {
		// 任务全部结束,状态都是成功,跳转到下一状态
		go task.CreateArchReviewer(*xflow.Id)
		StockContextUpdate(cont, global.STOCK_FLOW_HOOK)
		// 任务结束，根据gerritlist追加tag
		res, resMsg := task.FillStockTagBuild(*xflow.Id)
		if !res {
			slog.Error.Println("XflowStockScratchBuildProc FillStockTagBuild failed:", *xflow.Id, resMsg)
			StockContextUpdate(cont, global.STOCK_FLOW_SCRATCH_BUILD_FAILED)
			return
		}
		go task.CreateArchReviewer(*xflow.Id)
		// 下载scrash编译后的软件包
		if !task.GetSrpmByXflowId(*xflow.Id) {
			slog.Error.Println("XflowStockScratchBuildProc GetSrpmByXflowId failed:", *xflow.Id)
			StockContextUpdate(cont, global.STOCK_FLOW_SCRATCH_BUILD_FAILED)
			return
		}

		//开启hook检测
		go HookProcessRun(*xflow.Id, xflow.Type)

		//将CVE与Bug信息同步推送到CVE平台
		go task.TransferCveInfo(xflow)
	} else {
		//任务全部结束,有失败任务
		reason := strings.Join(taskFailed, ",")
		reason = "编译失败任务：" + reason
		slog.Error.Println("XflowStockScratchBuildProc failed:", *xflow.Id, reason)
		StockContextUpdate(cont, global.STOCK_FLOW_SCRATCH_BUILD_FAILED)
		return
	}
}

///////////////////////////////////////////////////////////////////////////////////////////////////////////

func XflowStockScratchBuildRetry(xflow *model.Xflow, cont *model.StockContext) {
	//查找本次流程中的中心仓入库包列表
	gerrits := model.GetGerritListByXflowId(*xflow.Id)
	if gerrits == nil {
		slog.Error.Println("XflowStockScratchBuildRetry GetGerritListByXflowId failed, xflow_Id ", *xflow.Id)
		StockContextUpdate(cont, global.STOCK_FLOW_SCRATCH_BUILD_FAILED)
		return
	}
	//检查软件包下载是否完成
	for i := range gerrits {
		if len(gerrits[i].Kojis) == 0 {
			// 还未创建koji任务,只需按原流程创建
			go StockBuildScratch(&gerrits[i], xflow, cont)
		} else {
			// 已创建过koji任务，进入重试流程
			go StockRebuildScratch(&gerrits[i], xflow, cont)
		}
		time.Sleep(100 * time.Millisecond)
	}
	// 流转状态跳转回
	StockContextUpdate(cont, global.STOCK_FLOW_SCRATCH_BUILD)
}

// 单开启重新scratch编译,并填充结构
func StockRebuildScratch(gerritList *model.GerritList, xflow *model.Xflow, cont *model.StockContext) {
	// 获取gerrit中已经保存scratch任务
	if gerritList == nil {
		slog.Error.Println("StockRebuildScratch failed, parameter error")
		StockContextUpdate(cont, global.STOCK_FLOW_SCRATCH_BUILD_FAILED)
		return
	}
	for _, taskId := range gerritList.Kojis {
		kojiTask := model.Koji.GetKojiListById(taskId)
		if kojiTask == nil {
			slog.Error.Println("StockRebuildScratch GetKojiListById failed:", taskId)
			StockContextUpdate(cont, global.STOCK_FLOW_SCRATCH_BUILD_FAILED)
			return
		}
		// 重启前的任务处于执行中，或已经成功返回，跳过
		if kojiTask.Status == global.KOJI_TASK_STATE_OPEN ||
			kojiTask.Status == global.KOJI_TASK_STATE_FREE ||
			kojiTask.Status == global.KOJI_TASK_STATE_REPO ||
			kojiTask.Status == global.KOJI_TASK_STATE_CLOSED {
			continue
		}
		// 任务已经失败，重启新任务，不新增kkojiList槽位
		ret := task.KojiBuildScratchByGerritProc(kojiTask, gerritList.GerritRepoName, gerritList.CommitID)
		if !ret {
			slog.Error.Println("StockRebuildScratch KojiBuildScratchByGerritProc failed:", kojiTask, gerritList.GerritRepoName, gerritList.CommitID)
			StockContextUpdate(cont, global.STOCK_FLOW_SCRATCH_BUILD_FAILED)
			return
		}
		ret = model.Koji.UpdateKojiList(kojiTask)
		if !ret {
			slog.Error.Println("StockRebuildScratch UpdateKojiList failed:", kojiTask)
			StockContextUpdate(cont, global.STOCK_FLOW_SCRATCH_BUILD_FAILED)
			return
		}
	}
}

///////////////////////////////////////////////////////////////////////////////////////////////////////////

func XflowStockHookProc(xflow *model.Xflow, cont *model.StockContext) {
	hookStatus := ScanHookTask(*xflow.Id)
	if hookStatus == global.HOOK_SUCCECD {
		StockContextUpdate(cont, global.STOCK_FLOW_EVALUATE)
		return
	}
	if hookStatus == global.HOOK_FAILED {
		// 检测异常，设置hook检测状态为失败
		StockContextUpdate(cont, global.STOCK_FLOW_HOOK_FAILED)
		return
	}
}

///////////////////////////////////////////////////////////////////////////////////////////////////////////

func XflowStockEvaluateProc(xflow *model.Xflow, cont *model.StockContext) {
	evaluateList := model.GetEvaluateListByXflowId(*xflow.Id)
	if evaluateList == nil {
		return
	}

	null_opinion := ""
	for _, evaluateNode := range evaluateList {
		if evaluateNode.Opinion == "" {
			null_opinion = "yes"
			continue
		}
		if evaluateNode.Opinion == global.REVIEW_TYPE_REJECT {
			XflowStockEvaluateFailedProc(xflow, cont)
			return
		}
	}
	if null_opinion == "yes" {
		return
	}

	StockContextUpdate(cont, global.STOCK_FLOW_REVIEW)
}

func XflowStockEvaluateFailedProc(xflow *model.Xflow, cont *model.StockContext) {
	//入库申请流转状态修改
	StockContextUpdate(cont, global.STOCK_FLOW_EVALUATE_DENY)
	//流程状态修改
	xflow.Status = global.XFLOW_STATUS_FAILED
	xflow.Stop_reason = "评估阶段失败，流程结束"
	ret := model.UpdateXflow(xflow)
	if !ret {
		slog.Error.Printf("XflowStockEvaluateFailedProc UpdateXflow failed %d\n", *xflow.Id)
	}
	//TODO: 评估失败，发送信息给CVE平台
	//go task.ReviewCveInfo(xflow)
}

///////////////////////////////////////////////////////////////////////////////////////////////////////////

func XflowStockReviewProc(xflow *model.Xflow, cont *model.StockContext) {
	reviewList := model.GetReviewListByXflowId(*xflow.Id)
	if len(reviewList) == 0 {
		return
	}

	null_opinion := ""
	for _, reviewNode := range reviewList {
		if reviewNode.Opinion == "" {
			null_opinion = "yes"
			continue
		}
		if reviewNode.Opinion == global.REVIEW_TYPE_REJECT {
			XflowStockReviewFailedProc(xflow, cont)
			return
		}
	}
	if null_opinion == "yes" {
		return
	}

	//判断是否有旧版本软件包在流程中
	if task.GetOldVersionPackInProcess(*xflow.Id) {
		return
	}

	//TODO: 审核成功，回传CVE平台信息
	go task.ReviewCveInfo(xflow)
	//先进行状态跳转，避免重复执行
	StockContextUpdate(cont, global.STOCK_FLOW_BUILD)
	//启动build任务
	ret := XflowStockBuildStart(xflow, cont)
	if !ret {
		slog.Error.Printf("XflowStockReviewProc XflowStockBuildStart failed %d\n", *xflow.Id)
		StockContextUpdate(cont, global.STOCK_FLOW_BUILD_FAILED)
		return
	}

}

func XflowStockReviewFailedProc(xflow *model.Xflow, cont *model.StockContext) {
	//入库申请流转状态修改
	StockContextUpdate(cont, global.STOCK_FLOW_REVIEW_DENY)
	//流程状态修改
	xflow.Status = global.XFLOW_STATUS_FAILED
	xflow.Stop_reason = "审核阶段失败，流程结束"
	ret := model.UpdateXflow(xflow)
	if !ret {
		slog.Error.Printf("XflowStockReviewFailedProc UpdateXflow failed %d\n", *xflow.Id)
	}
	//TODO: 审核失败，发送信息给CVE平台
	//go task.ReviewCveInfo(xflow)
}

// 启动入库任务
func XflowStockBuildStart(xflow *model.Xflow, cont *model.StockContext) bool {
	gerrits := model.GetGerritListByXflowId(*xflow.Id)
	if gerrits == nil {
		slog.Error.Println("XflowStockBuildStart GetGerritListByXflowId failed, xflow_Id ", *xflow.Id)
		StockContextUpdate(cont, global.STOCK_FLOW_BUILD_FAILED)
		return false
	}
	//获取所有本次流程关联的koji_scratch
	kojiTasks := []model.KojiList{}
	for _, gerrit := range gerrits {
		if len(gerrit.Kojis) == 0 {
			slog.Error.Println("XflowStockBuildStart failed, Kojis = 0 ", gerrit)
			StockContextUpdate(cont, global.STOCK_FLOW_BUILD_FAILED)
			return false
		}
		for _, id := range gerrit.Kojis {
			task := model.Koji.GetKojiListById(id)
			if task == nil {
				slog.Error.Println("XflowStockBuildStart GetKojiListById failed:", id)
				StockContextUpdate(cont, global.STOCK_FLOW_BUILD_FAILED)
				return false
			}
			kojiTasks = append(kojiTasks, *task)
		}
	}
	//遍历每个koji_task，启动对应的build任务
	for _, kojiTask := range kojiTasks {
		//如果判断软件包已经入库，则跳过
		if task.KojiBuildCheck(&kojiTask) {
			slog.Info.Println("XflowStockBuildStart KojiBuildCheck return true, kojitask bypass:", *kojiTask.Id)
			continue
		}
		//启动新的build任务
		ret := task.KojiBuildFromTask(&kojiTask)
		if !ret {
			slog.Error.Println("XflowStockBuildStart KojiBuildFromTask failed:\n", *kojiTask.Id)
			//TODO:通知模块通知管理员
			return false
		}
	}
	return true
}

///////////////////////////////////////////////////////////////////////////////////////////////////////////

func XflowStockBuildProc(xflow *model.Xflow, cont *model.StockContext) {
	gerrits := model.GetGerritListByXflowId(*xflow.Id)
	if gerrits == nil {
		slog.Error.Println("XflowStockBuildProc GetGerritListByXflowId failed, xflow_Id ", *xflow.Id)
		StockContextUpdate(cont, global.STOCK_FLOW_BUILD_FAILED)
		return
	}

	var taskIds []int
	for i := range gerrits {
		if len(gerrits[i].Kojis) == 0 {
			slog.Error.Println("XflowStockBuildProc failed, gerrits.Kojis error", gerrits[i])
			StockContextUpdate(cont, global.STOCK_FLOW_BUILD_FAILED)
			return
		}
		taskIds = append(taskIds, gerrits[i].Kojis...)
	}
	kojiTasks := model.Koji.GetKojiListsByIds(taskIds)
	if len(kojiTasks) == 0 {
		slog.Error.Println("XflowStockBuildProc GetKojiListsByIds koji_tasks failed:", *xflow.Id, taskIds)
		StockContextUpdate(cont, global.STOCK_FLOW_BUILD_FAILED)
		return
	}

	var buildIds []int
	for i := range kojiTasks {
		if kojiTasks[i].Stock_koji == 0 {
			return
		}
		buildIds = append(buildIds, kojiTasks[i].Stock_koji)
	}
	kojiBuilds := model.Koji.GetKojiListsByIds(buildIds)
	if len(kojiBuilds) == 0 {
		slog.Error.Println("XflowStockBuildProc GetKojiListsByIds koji_builds failed:", *xflow.Id, buildIds)
		StockContextUpdate(cont, global.STOCK_FLOW_BUILD_FAILED)
		return
	}

	//遍历数据库信息
	var buildFailed []string
	for i := range kojiBuilds {
		//有build任务未完成,停止本次查询
		if kojiBuilds[i].Status == global.KOJI_TASK_STATE_OPEN ||
			kojiBuilds[i].Status == global.KOJI_TASK_STATE_FREE ||
			kojiBuilds[i].Status == global.KOJI_TASK_STATE_REPO {
			return
		}
		//有build异常任务
		if kojiBuilds[i].Status == global.KOJI_TASK_STATE_FAILED || kojiBuilds[i].Status == global.KOJI_TASK_STATE_CANCELED {
			buildFailed = append(buildFailed, kojiBuilds[i].Task_id)
		}
	}

	if len(buildFailed) != 0 {
		//任务全部结束,有失败任务
		reason := strings.Join(buildFailed, ",")
		reason = "build失败任务：" + reason
		slog.Error.Println("XflowStockBuildProc failed:", reason)
		StockContextUpdate(cont, global.STOCK_FLOW_BUILD_FAILED)
		//TODO:通知模块通知管理员
		return
	}

	//先跳转状态到tag-build，如果出错将跳转tag-build-failed
	StockContextUpdate(cont, global.STOCK_FLOW_TAG_BUILD)

	//判断是否有tag-build任务
	kojis := model.Koji.GetTagTaskKojisByXflowId(*xflow.Id)
	if len(kojis) == 0 {
		//没有tag-build任务
		StockContextUpdate(cont, global.STOCK_FLOW_ARCHIVE)
		return
	}

	//启动tag-build任务
	ret := XflowStockTagBuildStart(xflow, cont)
	if !ret {
		slog.Error.Println("XflowStockBuildProc XflowStockTagBuildStart failed:", *xflow.Id)
		StockContextUpdate(cont, global.STOCK_FLOW_TAG_BUILD_FAILED)
		//TODO:通知模块通知管理员
		return
	}
}

func XflowStockTagBuildStart(xflow *model.Xflow, cont *model.StockContext) bool {
	//获取所有本次流程关联的koji_task
	kojis := model.Koji.GetTagTaskKojisByXflowId(*xflow.Id)
	if kojis == nil {
		slog.Error.Println("XflowStockTagBuildStart GetTagTaskKojisByXflowId failed, xflow_Id:", *xflow.Id)
		return false
	}
	//遍历每个koji任务，启动对应的tag-build任务
	for i := range kojis {
		if kojis[i].Task_id != "" {
			// 任务ID已存在，说明是重新触发
			if kojis[i].Status == global.KOJI_TASK_STATE_OPEN ||
				kojis[i].Status == global.KOJI_TASK_STATE_FREE ||
				kojis[i].Status == global.KOJI_TASK_STATE_REPO ||
				kojis[i].Status == global.KOJI_TASK_STATE_CLOSED {
				// 任务已成功，或者还在执行中
				continue
			}

			if task.KojiBuildCheck(&kojis[i]) {
				kojis[i].Status = global.KOJI_TASK_STATE_CLOSED
				model.Koji.UpdateKojiList(&kojis[i])
				continue
			}
		}
		//启动tag-build任务，任务ID和状态回填入结构体
		ret := task.KojiTagBuildProc(&kojis[i])
		if !ret {
			slog.Error.Println("XflowStockTagBuildStart KojiTagBuildProc failed:", *kojis[i].Id)
			continue
		}
		ret = model.Koji.UpdateKojiList(&kojis[i])
		if !ret {
			slog.Error.Println("XflowStockTagBuildStart UpdateKojiList failed:", kojis[i])
			continue
		}
	}

	return true
}

// /////////////////////////////////////////////////////////////////////////////////////////////////////////

func XflowStockBuildRetryProc(xflow *model.Xflow, cont *model.StockContext) {
	XflowLock(xflow)
	XflowStockBuildRetry(xflow, cont)
	XflowUnLock(xflow)
}

func XflowStockBuildRetry(xflow *model.Xflow, cont *model.StockContext) {
	//获取所有本次流程关联的koji_task
	gerrits := model.GetGerritListByXflowId(*xflow.Id)
	if gerrits == nil {
		slog.Error.Println("XflowStockBuildRetry GetGerritListByXflowId failed, xflow_Id ", *xflow.Id)
		StockContextUpdate(cont, global.STOCK_FLOW_BUILD_FAILED)
		return
	}

	koji_tasks := []model.KojiList{}
	for _, gerrit := range gerrits {
		if len(gerrit.Kojis) == 0 {
			slog.Error.Println("XflowStockBuildRetry failed, Kojis = 0 ", gerrit)
			StockContextUpdate(cont, global.STOCK_FLOW_BUILD_FAILED)
			return
		}
		for _, id := range gerrit.Kojis {
			task := model.Koji.GetKojiListById(id)
			if task == nil {
				slog.Error.Println("XflowStockBuildRetry GetKojiListById failed:", id)
				StockContextUpdate(cont, global.STOCK_FLOW_BUILD_FAILED)
				return
			}
			koji_tasks = append(koji_tasks, *task)
		}
	}
	//遍历每个koji_task，启动对应的build任务
	for _, koji_task := range koji_tasks {
		if koji_task.Stock_koji != 0 {
			koji_build := model.Koji.GetKojiListById(koji_task.Stock_koji)
			if koji_build == nil {
				slog.Error.Println("XflowStockBuildRetry GetKojiListById failed:", koji_task.Stock_koji)
				StockContextUpdate(cont, global.STOCK_FLOW_BUILD_FAILED)
				return
			}
			if koji_build.Status == global.KOJI_TASK_STATE_FAILED || koji_build.Status == global.KOJI_TASK_STATE_CANCELED {
				ret := task.KojiRebuildFromTask(&koji_task)
				if !ret {
					slog.Error.Println("XflowStockBuildRetry KojiRebuildFromTask failed:", *koji_build.Id)
					StockContextUpdate(cont, global.STOCK_FLOW_BUILD_FAILED)
					return
				}
			}
		} else {
			// 启动新的build任务
			ret := task.KojiBuildFromTask(&koji_task)
			if !ret {
				slog.Error.Println("XflowStockBuildRetry KojiBuildFromTask failed:", *koji_task.Id)
				StockContextUpdate(cont, global.STOCK_FLOW_BUILD_FAILED)
				return
			}
		}
	}
	// 重启编译流程时，需要跳转到BUILD状态
	StockContextUpdate(cont, global.STOCK_FLOW_BUILD)
}

///////////////////////////////////////////////////////////////////////////////////////////////////////////

func XflowStockTagBuildProc(xflow *model.Xflow, cont *model.StockContext) {
	//获取xflowId对应的koji任务
	kojis := model.Koji.GetTagTaskKojisByXflowId(*xflow.Id)
	//遍历数据库信息
	var taskFailed []string
	for i := range kojis {
		if kojis[i].Task_id == "" {
			//有任务未启动
			return
		}
		if kojis[i].Status == global.KOJI_TASK_STATE_OPEN ||
			kojis[i].Status == global.KOJI_TASK_STATE_FREE ||
			kojis[i].Status == global.KOJI_TASK_STATE_REPO {
			//有任务未完成
			return
		}
		if kojis[i].Status == global.KOJI_TASK_STATE_FAILED || kojis[i].Status == global.KOJI_TASK_STATE_CANCELED {
			taskFailed = append(taskFailed, kojis[i].Task_id)
		}
	}
	if len(taskFailed) != 0 {
		// 任务全部结束,有失败任务
		reason := strings.Join(taskFailed, ",")
		reason = "tag-build失败任务：" + reason
		slog.Error.Println("XflowStockTagBuildProc failed:", reason)
		StockContextUpdate(cont, global.STOCK_FLOW_TAG_BUILD_FAILED)
		//TODO:通知模块通知管理员
		return
	}
	//任务全部结束，无失败任务，跳转状态
	StockContextUpdate(cont, global.STOCK_FLOW_ARCHIVE)
}

// /////////////////////////////////////////////////////////////////////////////////////////////////////////
// 重启追加tag任务
func XflowStockTagBuildRetry(xflow *model.Xflow, cont *model.StockContext) {
	//启动tag-build任务
	ret := XflowStockTagBuildStart(xflow, cont)
	if !ret {
		slog.Error.Println("XflowStockTagBuildRetry XflowStockTagBuildStart failed:", *xflow.Id)
		StockContextUpdate(cont, global.STOCK_FLOW_TAG_BUILD_FAILED)
		//TODO:通知模块通知管理员
		return
	}
	StockContextUpdate(cont, global.STOCK_FLOW_TAG_BUILD)
}

///////////////////////////////////////////////////////////////////////////////////////////////////////////

func XflowStockArchiveProc(xflow *model.Xflow, cont *model.StockContext) {
	//入库commit上打tag
	go XflowStockArchiveCommitTag(xflow, cont)
	//选跳转状态，保证归档只出发一次
	StockContextUpdate(cont, global.STOCK_FLOW_FINISHED)
	//补充taglist缺失信息
	go XflowStockArchiveUpdateTaglist(xflow, cont)
	//订阅信息处理接口
	go XflowStockArchiveSubsProc(xflow, cont)
}

func XflowStockArchiveUpdateTaglist(xflow *model.Xflow, cont *model.StockContext) {
	//检查产品信息中的缺失信息并填充
	kojiLists := model.Koji.GetKojiListByXflowId(*xflow.Id)
	if len(kojiLists) == 0 {
		slog.Error.Println("XflowStockArchiveUpdateTaglist GetKojiListByXflowId failed", xflow.Id)
		return
	}
	tagList := model.GetTagListById(kojiLists[0].Tag)
	if tagList == nil {
		slog.Error.Println("XflowStockArchiveUpdateTaglist GetTagListById failed", kojiLists[0].Tag)
		return
	}
	if tagList.ProdId == 0 {
		productList := model.GetProductListByTagId(*tagList.Id)
		if productList == nil {
			slog.Error.Println("XflowStockArchiveUpdateTaglist GetProductListByTagId failed", *tagList.Id)
			return
		}
		tagList.ProdId = *productList.Id
		tagList.KojiPlat = productList.ProductKojis
		ret := model.UpdateTagList(tagList)
		if !ret {
			slog.Error.Println("XflowStockArchiveUpdateTaglist UpdateTagList failed", tagList)
			return
		}
	}
}

// 订阅处理函数
func XflowStockArchiveSubsProc(xflow *model.Xflow, cont *model.StockContext) {
	gerritLists := model.GetGerritListByXflowId(*xflow.Id)
	if gerritLists == nil {
		slog.Error.Println("XflowStockArchiveSubsProc GetGerritListByXflowId failed", *xflow.Id)
		return
	}
	subsInfo, ret := task.GetSubsInfoFromGerritList(gerritLists)
	if !ret {
		slog.Error.Println("XflowStockArchiveSubsProc GetSubsInfoFromGerritList failed", *xflow.Id)
		return
	}
	userSubsMap := task.GetUserSubsInfo(subsInfo)
	for userName, subsInfo := range userSubsMap {
		prodSubsMap := task.GetProdSubsInfo(subsInfo)
		task.MsgLxStockToSubscribers(prodSubsMap, *xflow.Id, userName)
	}
}

///////////////////////////////////////////////////////////////////////////////////////////////////////////

func XflowStockRevokeProc(xflow *model.Xflow, cont *model.StockContext) {

}

///////////////////////////////////////////////////////////////////////////////////////////////////////////

func XflowStockFailedProc(xflow *model.Xflow, cont *model.StockContext) {

}

func XflowStockFinishedProc(xflow *model.Xflow, cont *model.StockContext) {
	// 流程结束，将hook检测关联文件删除
	ClearCacheFiles(*xflow.Id)
	//结束整个xflow流程
	model.SafeSetXflowStatus(*xflow.Id, global.XFLOW_STATUS_FINISHED)
	xflow.Status = global.XFLOW_STATUS_FINISHED
	ret := model.UpdateXflow(xflow)
	if !ret {
		slog.Error.Printf("XflowStockFinishedProc UpdateXflow failed %d\n", *xflow.Id)
	}
}

func XflowStockArchiveCommitTag(xflow *model.Xflow, cont *model.StockContext) {
	var commitTag string = "build/"
	var srpmVersion string
	var srpmRelease string

	// 1. 从GerritList中获取产品ID，从产品ID中获取koji-tag
	// 2. 从/opt/files/slam/tmp/src/xflow_id/*.ky11.src.rpm calivergopher/rpm 获取v-r返回
	gerritLists := model.GetGerritListByXflowId(*xflow.Id)
	var kojiLists []*model.KojiList
	// 多个Gerrit链接入库
	for _, gerritList := range gerritLists {
		// 1.先把执行编译的tag获取到
		compileProduct := model.GetProductListById(gerritList.CompileProd)
		tagLists := model.GetTagListById(compileProduct.Tag)
		// 2.获取srcrpm
		var kojiListSRPMs []string
		for _, kojiListId := range gerritList.Kojis {
			currentKojiList := model.Koji.GetKojiListById(kojiListId)
			kojiLists = append(kojiLists, currentKojiList)
			kojiListSRPMs = append(kojiListSRPMs, currentKojiList.Src_name)
		}
		// 3. srcrpm去重
		kojiListSRPMs = utils.RemoveRepeatElement(kojiListSRPMs)
		// repoName重写
		repoName := strings.ReplaceAll(gerritList.GerritRepoName, "/", "%2F")
		if !strings.Contains(repoName, "%2F") {
			repoName = fmt.Sprintf("server%s%s", "%2F", repoName)
		}
		// 4. pkg.p01.ky10.src.rpm 和 pkg.p01.a.ky10.src.rpm分别打tag
		for _, kojiListSRPM := range kojiListSRPMs {
			commitTag = "build/" + tagLists.Target + "/"
			SRPMPath := global.SRC_PACKAGE_DIR + strconv.FormatInt(int64(*xflow.Id), 10) + "/" + kojiListSRPM
			isSRPMExists, err := utils.PathExists(SRPMPath)
			if isSRPMExists {
				srpm, err := rpm.Open(SRPMPath)
				if err != nil {
					slog.Error.Printf("SRPMPath is not a rpm. %v", err)
					StockContextUpdate(cont, global.STOCK_FLOW_FAILED)
					return
				}
				srpmVersion = srpm.Version()
				srpmRelease = srpm.Release()
				commitTag += srpmVersion + "-" + srpmRelease
				commitTag = strings.ReplaceAll(commitTag, "/", "%2F")
				targetBranchRevision, isTargetBranchRevisionExists := task.GetInitialRevisionFromBranchName(repoName, gerritList.GerritBranch, utils.GerritAk, task.G_GerritToken)
				if isTargetBranchRevisionExists {
					commitedTag := task.CreateProjectTag(repoName, commitTag, targetBranchRevision, utils.GerritAk, task.G_GerritToken, kojiLists, *xflow.Id)
					slog.Error.Printf("CreateTag Success, %v:%v commitedTagInfo: %v", repoName, gerritList.GerritBranch, commitedTag)
					continue
				} else {
					slog.Error.Printf("Gerrit Project: %v Branch: %v Not Exist", repoName, gerritList.GerritBranch)
					StockContextUpdate(cont, global.STOCK_FLOW_FAILED)
					return
				}
			} else {
				slog.Error.Printf("SRPM Exist Error, %v", err)
				StockContextUpdate(cont, global.STOCK_FLOW_FAILED)
				return
			}
		}

		// 产品追加打tag
		for _, addProductId := range gerritList.AddProd {
			addProduct := model.GetProductListById(addProductId)
			addProductTag := model.GetTagListById(addProduct.Tag)
			commitTag = "build/" + addProductTag.Target + "/" + srpmVersion + "-" + srpmRelease
			commitTag = strings.ReplaceAll(commitTag, "/", "%2F")
			targetBranchRevision, isTargetBranchRevisionExists := task.GetInitialRevisionFromBranchName(repoName, gerritList.GerritBranch, utils.GerritAk, task.G_GerritToken)
			if isTargetBranchRevisionExists {
				commitedTag := task.CreateProjectTag(repoName, commitTag, targetBranchRevision, utils.GerritAk, task.G_GerritToken, kojiLists, *xflow.Id)
				slog.Error.Printf("CreateTag Success, %v:%v commitedTagInfo: %v", repoName, gerritList.GerritBranch, commitedTag)
				continue
			} else {
				slog.Error.Printf("Gerrit Project: %v Branch: %v Not Exist", repoName, gerritList.GerritBranch)
				StockContextUpdate(cont, global.STOCK_FLOW_FAILED)
				return
			}
		}
	}
}
