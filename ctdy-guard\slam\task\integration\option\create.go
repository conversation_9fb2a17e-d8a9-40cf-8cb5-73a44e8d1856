/*
 * @Author: lwq <EMAIL>
 * @Date: 2025-07-08
 * @LastEditors: lwq <EMAIL>
 * @LastEditTime: 2025-07-29
 * @FilePath: /ctdy-guard/slam/task/integration/option/create.go
 * @Description: 创建集成单
 * @Copyright (c) 2025 by 2022.kylinos.cn, All Rights Reserved.
 */
package option

import (
	"slam/global"
	"slam/model"
	"slam/slog"
	"time"

	"github.com/gin-gonic/gin"
)

// CreateOption 创建集成单操作
type CreateOption struct {
	BaseOption
	igModel      model.IntegrationRepo      // 集成单模型
	igCountModel model.IntegrationCountRepo // 集成单模型
}

type CreateOptionReq struct {
	User *OpUser
}

func NewCreateOption() *CreateOption {
	return &CreateOption{
		BaseOption: BaseOption{
			ID:   "create",
			Name: "创建集成单",
		},
		igModel:      model.NewIntegrationModel(),
		igCountModel: model.NewIntegrationCountModel(),
	}
}

/**
 * @author: lwq <EMAIL>
 * @description: 创建一个紧急集成单，手动创建的都是紧急单
 * @param {*gin.Context} c
 * @param {*OpUser} opUser
 * @param {any} params
 * @return {*}
 */
func (o *CreateOption) Execute(c *gin.Context, opUser *OpUser, params any) (any, error) {
	ig := &model.Integration{
		AliasName:   "集成单" + time.Now().Format(global.DATE_FORMAT),
		Remark:      "临时加急单",
		Urgent:      global.INTEGRATION_URGENT,
		CreatorId:   opUser.UserId,
		CreatorName: opUser.UserName,
		StartAt:     time.Now().Truncate(time.Second),
	}
	igId, err := o.CreateIntegration(ig)
	if err != nil {
		slog.ErrorWithContext(c, "创建集成单失败", err)
		return nil, err
	}
	return igId, nil
}

/**
 * @author: lwq <EMAIL>
 * @description: 具体创建集成单的方法，可提供自动创建使用
 * @param {*model.Integration} ig
 * @return {*}
 */
func (o *CreateOption) CreateIntegration(ig *model.Integration) (int, error) {
	// 创建集成单，默认状态为校对中
	ig.Status = global.INTEGRATION_STATUS_COMPARE
	igId, err := o.igModel.CreateIntegration(ig)
	if err != nil {
		return 0, err
	}

	// 同时创建一条当前状态的统计数据
	count := &model.IntegrationCount{
		IntegrationId: igId,
	}
	if err := o.igCountModel.CreateIntegrationCount(count); err != nil {
		return 0, err
	}
	return igId, nil
}
