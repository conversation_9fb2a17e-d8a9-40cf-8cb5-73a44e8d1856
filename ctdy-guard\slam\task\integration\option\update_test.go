package option

import (
	"slam/model"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
)

func TestUpdateOptionExecute(t *testing.T) {
	NewUpdateOption()
	gin.SetMode(gin.TestMode)

	t.Run("successful update execution", func(t *testing.T) {
		// Mocks
		mockIgModel := new(MockIntegrationRepo)

		// Test data
		req := &EditIgReq{
			IgId:   10,
			IgName: "Updated Integration Name",
			Remark: "Updated remark",
		}

		opUser := &OpUser{
			UserInfo: UserInfo{
				UserId:   1,
				UserName: "testuser",
				Email:    "<EMAIL>",
			},
		}

		// Mock expectations
		mockIgModel.On("UpdateIntegration", mock.MatchedBy(func(ig *model.Integration) bool {
			return ig.Id == 10 && ig.AliasName == req.IgName && ig.Remark == req.Remark
		})).Return(nil)

		// Create UpdateOption instance with mocks
		updateOption := &UpdateOption{
			BaseOption: BaseOption{
				ID:   "update",
				Name: "更新集成单",
			},
			igModel: mockIgModel,
		}

		// Execute
		ctx, _ := gin.CreateTestContext(nil)
		result, err := updateOption.Execute(ctx, opUser, req)

		// Assertions
		assert.NoError(t, err)
		assert.Nil(t, result)
		mockIgModel.AssertExpectations(t)
	})

	t.Run("failed update execution due to model error", func(t *testing.T) {
		// Mocks
		mockIgModel := new(MockIntegrationRepo)

		// Test data
		req := &EditIgReq{
			IgId:   10,
			IgName: "Updated Integration Name",
			Remark: "Updated remark",
		}

		opUser := &OpUser{
			UserInfo: UserInfo{
				UserId:   1,
				UserName: "testuser",
				Email:    "<EMAIL>",
			},
		}

		// Mock expectations
		mockIgModel.On("UpdateIntegration", mock.MatchedBy(func(ig *model.Integration) bool {
			return ig.Id == 10 && ig.AliasName == req.IgName && ig.Remark == req.Remark
		})).Return(assert.AnError)

		// Create UpdateOption instance with mocks
		updateOption := &UpdateOption{
			BaseOption: BaseOption{
				ID:   "update",
				Name: "更新集成单",
			},
			igModel: mockIgModel,
		}

		// Execute
		ctx, _ := gin.CreateTestContext(nil)
		result, err := updateOption.Execute(ctx, opUser, req)

		// Assertions
		assert.Error(t, err)
		assert.Nil(t, result)
		mockIgModel.AssertExpectations(t)
	})
}
