package task

import (
	"bufio"
	"encoding/json"
	"fmt"
	"io"
	"mime/multipart"
	"os"
	"path"
	"slam/global"
	"slam/model"
	"slam/slog"
	"slam/utils"
	"strconv"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
)

// 源码包入库结构体
type StockCenterInfo struct {
	Packages       string                  `label:"源码包数据-接前端数据" binding:"required"`
	Packages_info  []StockCenterPacksInfo  `label:"源码包数据"`
	Cve_info       string                  `label:"cve信息"`
	Evaluators     string                  `label:"指定评估人-接前端数据" binding:"required"`
	Evaluator      []int                   `label:"指定评估人"`
	SupplementInfo string                  `label:"补充信息"`
	TestInfo       string                  `label:"测试信息"`
	Appendix       []*multipart.FileHeader `label:"附件"`
	Appendix_names string                  `label:"重发附件名-接前端数据"`
	Appendix_name  []string                `label:"重发附件名"`
	Origin_xflow   string                  `label:"重发时原流程id"`
}

// 源码包结构体
type StockCenterPacksInfo struct {
	PackName      string   `label:"源码包名称"`
	Developer     string   `label:"来源社区"`
	ProdRelease   string   `label:"来源发行版"`
	BizSoftForm   string   `label:"商业软件包形态"`
	BizSoftType   string   `label:"商业软件包类型"`
	ProdId        int      `label:"目标产品ID"`
	LectoId       int      `label:"源码包对应ID"`
	CompileOption []string `label:"编译参数"`
}

// 源码包入库详情
type StockCenterDetail struct {
	Id              int                 `label:"流程id"`
	StockId         int                 `label:"入库申请静态信息id"`
	StockContextId  int                 `label:"入库申请上下文信息id"`
	CveInfo         string              `label:"cvexinxi"`
	Flow_status     string              `label:"流程状态"`
	Stop_reason     string              `label:"流程终止原因"`
	Create_time     string              `label:"流程创建时间"`
	Create_user     model.User          `label:"流程创建用户"`
	Current_flow    string              `label:"入库申请状态"`
	Packs_info      []SCenterPackDetail `label:"软件包信息"`
	Evaluator       []model.User        `label:"指定评估人姓名"`
	Supplement_info string              `label:"补充信息"`
	Test_info       string              `label:"测试信息"`
	Appendix        []string            `label:"附件"`
	Stock_type      string              `label:"入库方式"`
}

type SCenterPackDetail struct {
	PackName      string            `label:"源码包名称"`
	Developer     string            `label:"开发商"`
	ProdRelease   string            `label:"开发商产品线"`
	BizSoftForm   string            `label:"商业软件包形态"`
	BizSoftType   string            `label:"商业软件包类型"`
	ProdId        int               `label:"入库产品ID"`
	Framwork      []string          `label:"产品对应架构"`
	LectoId       int               `label:"源码包对应软件选型包id"`
	Koji          []StockKojiDetail `label:"关联koji信息"`
	CompileOption []string          `label:"编译参数"`
	Status        string            `label:"文件入库时——当前源码包检测结果"`
	Msg           string            `label:"文件入库时——当前源码包不通过原因"`
}

// 文件入库——上传文件结构体
type SCFilePackInfo struct {
	Developers  string                `json:"developers" label:"开发商" binding:"required"`
	ProdRelease string                `json:"prod_release" label:"开发商产品线" binding:"required"`
	BizSoftForm string                `json:"bizsoft_form" label:"商业软件包形态:源码包，二进制包"`
	BizSoftType string                `json:"bizsoft_type" label:"商业软件包类型"`
	File        *multipart.FileHeader `json:"file" label:"传入文件" binding:"required" `
}

// 文件上传——新增
type SCFileInfo struct {
	Developer      string                  `label:"开发商"`
	ProdRelease    string                  `label:"开发商产品线"`
	BizSoftForm    string                  `label:"商业软件形态:源码包，二进制包"`
	BizSoftType    string                  `label:"商业软件包类型"`
	ProdIdS        string                  `label:"入库产品ID-接前端数据" binding:"required"`
	ProdId         int                     `label:"入库产品ID"`
	File           *multipart.FileHeader   `label:"传入文件"`
	CompileOptions string                  `label:"编译参数-接前端"`
	CompileOption  []string                `label:"编译参数"`
	Evaluators     string                  `label:"指定评估人-接前端数据" binding:"required"`
	Evaluator      []int                   `label:"指定评估人"`
	SupplementInfo string                  `label:"补充信息"`
	TestInfo       string                  `label:"测试信息"`
	Appendix       []*multipart.FileHeader `label:"附件"`
	Appendix_names string                  `label:"重发附件名-接前端数据"`
	Appendix_name  []string                `label:"重发附件名"`
	Origin_xflow   string                  `label:"重发时原流程id"`
	Split          string                  `label:"是否拆分提交"`
}

// 文件入库——详情
type SCFileDetail struct {
	Id              int                 `label:"流程id"`
	StockId         int                 `label:"入库申请静态信息id"`
	StockContextId  int                 `label:"入库申请上下文信息id"`
	Flow_status     string              `label:"流程状态"`
	Stop_reason     string              `label:"流程终止原因"`
	Create_time     string              `label:"流程创建时间"`
	Create_user     model.User          `label:"流程创建用户"`
	Current_flow    string              `label:"入库申请状态"`
	File            []string            `label:"文件"`
	Packs_info      []SCenterPackDetail `label:"软件包信息"`
	Evaluator       []model.User        `label:"指定评估人姓名"`
	Supplement_info string              `label:"补充信息"`
	Test_info       string              `label:"测试信息"`
	Developer       string              `label:"开发商"`
	ProdRelease     string              `label:"开发商产品线"`
	ProdId          int                 `label:"入库产品ID"`
	Framwork        []string            `label:"产品对应架构"`
	Appendix        []string            `label:"附件"`
	Stock_type      string              `label:"入库方式"`
}

// 文件入库拆分新增对应表关系
type SCFileSplitRelation struct {
	PackName     string      `label:"软件报名" json:"pack_name"`
	Xflow        model.Xflow `label:"流程" json:"xflow"`
	XflowId      int         `label:"流程ID" json:"xflow_id"`
	StaticId     int         `label:"静态信息表ID" json:"static_id"`
	EvaluatorsId []int       `label:"评估人ID数组" json:"evaluators_id"`
	ContextId    int         `label:"动态信息表ID" json:"context_id"`
	PackId       int         `label:"软件包表ID" json:"pack_id"`
	Status       string      `label:"当前源码包数据插入结果" json:"status"`
	Msg          string      `label:"当前源码包数据插入失败原因" json:"msg"`
}

type SCenterPackageVerify struct {
	Developers     string   `json:"developers" label:"开发商"`
	ProdRelease    string   `json:"prod_release" label:"开发商产品线"`
	BizSoftForm    string   `json:"bizsoft_form" label:"商业软件包形态:源码包，二进制包"`
	BizSoftType    string   `json:"bizsoft_type" label:"商业软件包类型"`
	PackNameS      string   `label:"源码包名称" binding:"required"`
	PackName       []string `json:"pack_name" label:"源码包名称"`
	ProdIdS        string   `label:"入库产品ID-接前端数据" binding:"required"`
	ProdId         int      `json:"prod_id" label:"入库产品ID"`
	CompileOptionS string   `label:"编译参数"`
	CompileOption  []string `json:"compile_option" label:"编译参数"`
}

// /////////////////////////////////////////////////////////////////////////////////////////////////////////
// 源码包入库——新增
func CreateStockCenter(t string, stock *StockCenterInfo, c *gin.Context) (bool, string) {
	userRes, userMsg, userItem := GetUserInfo(c)
	if !userRes {
		return false, userMsg
	}

	//总流程信息整合
	res, xflow := CreateSCenterXflow(t, *userItem.Id)
	if !res {
		slog.Error.Println("源码包入库新增——新增流程信息报错")
		UpdateXflowStatusFailed("stockcenter——新增流程信息报错", xflow)
		return false, global.STR_ADD_FLOWADD
	}

	//插入源码包信息
	res, packIds := CreateSCenterPacks(stock.Packages_info, *xflow.Id)
	if !res {
		slog.Error.Println("源码包入库新增——新增软件包信息报错")
		UpdateXflowStatusFailed("stockcenter——新增软件包信息报错", xflow)
		return false, "您提交的软件包创建异常，请联系管理员协助您处理"
	}
	// 入库申请附件存储
	// 存储文件类型附件
	SaveSCenterAppendix(stock.Appendix, *xflow.Id, c)
	if len(stock.Appendix_name) > 0 {
		// 存储文件名类型附件
		res = TransferSCenterAppendix(stock.Appendix_name, *xflow.Id, stock.Origin_xflow)
		if !res {
			slog.Error.Println("源码包入库新增——入库申请附件文件信息转存异常")
			UpdateXflowStatusFailed("stockcenter——入库申请附件转存报错", xflow)
			return false, global.STR_ADD_APPENDIX
		}
	}
	//插入静态信息
	res, stockList := CreateSCenterStatic(*xflow.Id, packIds, stock)
	if !res {
		slog.Error.Println("源码包入库新增——入库申请静态信息新增报错")
		UpdateXflowStatusFailed("stockcenter——入库申请静态信息新增报错", xflow)
		return false, global.STR_ADD_STOCKSTATIC
	}
	// 入库申请评估人信息整合
	evaluatorIds := CreateEvalutes(stock.Evaluator, *xflow.Id)
	if evaluatorIds == nil {
		slog.Error.Println("源码包入库新增——入库申请信息新增对应评估人报错")
		UpdateXflowStatusFailed("stockcenter——入库申请信息新增对应评估人报错", xflow)
		return false, global.STR_ADD_EVALUATE
	}
	//插入动态信息
	res, stockContext := CreateSCenterContext(*xflow.Id, evaluatorIds)
	if !res {
		slog.Error.Println("源码包入库新增——入库申请动态信息新增报错")
		UpdateXflowStatusFailed("stockcenter——入库申请动态信息新增报错", xflow)
		return false, global.STR_ADD_STOCKCONTEXT
	}
	//用动态与静态信息更新流程表
	res = UpdateXlowRelatedList(*stockList.Id, *stockContext.Id, xflow)
	if !res {
		slog.Error.Println("源码包入库新增——更新流程信息报错")
		UpdateXflowStatusFailed("stockcenter——更新流程信息报错", xflow)
		return false, global.STR_ADD_FLOWUPDATE
	}

	//表数据已全部插入
	return true, ""
}

// 创建流程信息
func CreateSCenterXflow(stockType string, userId int) (bool, *model.Xflow) {
	xflow := model.Xflow{
		Type:    stockType,
		Status:  global.XFLOW_STATUS_PENDING,
		User_id: userId,
	}
	return model.CreateXflow(&xflow), &xflow
}

// 创建软件包信息
func CreateSCenterPacks(packs []StockCenterPacksInfo, xflowId int) (bool, []int) {
	var packIds []int
	for _, pack := range packs {
		stockPack := model.StockCenterPackList{
			Xflow_id:    xflowId,
			PackName:    pack.PackName,
			Developer:   pack.Developer,
			ProdRelease: pack.ProdRelease,
			BizSoftForm: pack.BizSoftForm,
			BizSoftType: pack.BizSoftType,
			ProdId:      pack.ProdId,
			Kojis:       []int{},
			LectoId:     pack.LectoId,
			CmdOption:   pack.CompileOption,
		}
		res := model.CreateStcPackList(&stockPack)
		if !res {
			slog.Error.Println("源码包入库新增——软件包新增报错")
			return false, packIds
		}
		packIds = append(packIds, *stockPack.Id)
	}
	return true, packIds
}

// 创建流程静态信息
func CreateSCenterStatic(xflow_id int, packIds []int, stock *StockCenterInfo) (bool, *model.StockCenterList) {
	stockCenterStatic := model.StockCenterList{
		Xflow_id:        xflow_id,
		Packs:           packIds,
		Supplement_info: stock.SupplementInfo,
		Test_info:       stock.TestInfo,
		Appendix:        GetAppendixName(stock.Appendix, stock.Appendix_name),
		CveInfo:         stock.Cve_info,
		CreatedAt:       time.Now(),
		UpdatedAt:       time.Now(),
		Stock_type:      global.STOCK_TYPE_CenterS,
	}
	return model.CreateStockCenterList(&stockCenterStatic), &stockCenterStatic
}

// 创建流程动态信息
func CreateSCenterContext(xflow_id int, evaluatorIds []int) (bool, *model.StockCenterContext) {
	stockCenterContext := model.StockCenterContext{
		Xflow_id:     xflow_id,
		Current_flow: global.STOCK_CENTER_FLOW_SUBMIT,
		Hook_status:  global.HOOK_TESTING,
		Review_list:  evaluatorIds,
		CreatedAt:    time.Now(),
		UpdatedAt:    time.Now(),
	}
	return model.CreateStockCenterContext(&stockCenterContext), &stockCenterContext
}

// 存储入库申请附件到指定目录
func SaveSCenterAppendix(appendix []*multipart.FileHeader, xflow_id int, c *gin.Context) {
	for _, file := range appendix {
		// 上传文件到指定的目录
		// 设置文件需要保存的指定位置并设置保存的文件名字
		dst := path.Join(global.STOCK_CENTER_APPENDIX_REPO, strconv.Itoa(xflow_id), file.Filename)
		c.SaveUploadedFile(file, dst)
	}
}

// 转存文件到指定目录
func TransferSCenterAppendix(appendix []string, xflow_id int, origin_flow_id string) bool {
	for _, filename := range appendix {
		originFileName := global.STOCK_CENTER_APPENDIX_REPO + origin_flow_id + "/" + filename
		bytesFile, err := os.ReadFile(originFileName)
		if err != nil {
			slog.Error.Println("TransferSCenterAppendix failed:", err)
			return false
		}
		saveDirPath := global.STOCK_CENTER_APPENDIX_REPO + strconv.Itoa(xflow_id)
		_, err = os.Stat(saveDirPath) // err==nil表示文件或文件夹存在，如果os.IsNotExist返回true，说明文件或文件夹不存在
		if os.IsNotExist(err) {
			// 创建文件夹
			err = os.Mkdir(saveDirPath, 0755)
			if err != nil {
				slog.Error.Println("TransferSCenterAppendix failed:", "为流程", strconv.Itoa(xflow_id), "创建文件目录失败,", err)
				return false
			}
		}
		aimFileName := saveDirPath + "/" + filename
		err = os.WriteFile(aimFileName, bytesFile, 0600)
		if err != nil {
			slog.Error.Println("TransferSCenterAppendix failed:", err)
			return false
		}
	}
	return true
}

// /////////////////////////////////////////////////////////////////////////////////////////////////////////
// 获取源码包入库详情
func GetStockCenterDetail(id int) *StockCenterDetail {
	xflow := model.GetXflowById(id)
	var stockDetail StockCenterDetail
	stockList := model.GetStockCenterListByXflowId(id)
	stockContext := model.GetStockCenterContextByXflowId(id)
	if xflow == nil || stockList == nil || stockContext == nil || *xflow == (model.Xflow{}) {
		slog.Error.Println("GetStockCenterDetail failed")
		return nil
	}
	// 创建用户信息绑定
	user := model.GetUserById(xflow.User_id)
	if user == nil || *user == (model.User{}) {
		user = &model.User{}
	}
	packsInfo := GetStockDetailPacks(stockList.Packs)

	stockDetail = StockCenterDetail{
		Id:              *xflow.Id,
		StockId:         *stockList.Id,
		StockContextId:  *stockContext.Id,
		CveInfo:         stockList.CveInfo,
		Flow_status:     xflow.Status,
		Stop_reason:     xflow.Stop_reason,
		Create_time:     xflow.CreatedAt.Format(global.TIME_FORMAT),
		Create_user:     *user,
		Current_flow:    stockContext.Current_flow,
		Packs_info:      packsInfo,
		Evaluator:       GetDetailEvaluators(id),
		Supplement_info: stockList.Supplement_info,
		Test_info:       stockList.Test_info,
		Appendix:        stockList.Appendix,
		Stock_type:      stockList.Stock_type,
	}
	return &stockDetail
}

// 获取软件包详情
func GetStockDetailPacks(packs []int) []SCenterPackDetail {
	var packList []SCenterPackDetail
	for _, packId := range packs {
		packItem := model.GetStcListById(packId)
		if packItem != nil && packItem != &(model.StockCenterPackList{}) {
			stockDetailPack := SCenterPackDetail{
				PackName:      packItem.PackName,
				Developer:     packItem.Developer,
				ProdRelease:   packItem.ProdRelease,
				BizSoftForm:   packItem.BizSoftForm,
				BizSoftType:   packItem.BizSoftType,
				ProdId:        packItem.ProdId,
				Framwork:      GetArchsByProductId(packItem.ProdId),
				LectoId:       packItem.LectoId,
				Koji:          GetStockDetailKojis(packItem.Kojis),
				CompileOption: packItem.CmdOption,
			}
			packList = append(packList, stockDetailPack)
		} else {
			return []SCenterPackDetail{}
		}
	}
	return packList
}

// /////////////////////////////////////////////////////////////////////////////////////////////////////////
// 批量拷贝中心仓软件包到流程目录
func CopyPackFromCenter(xflow *model.Xflow, pack *model.StockCenterPackList) bool {
	//判断rpm包是否已存在且完整
	filePath := fmt.Sprintf("%s/%d/%s", global.SRC_PACKAGE_DIR, pack.Xflow_id, pack.PackName)
	if utils.SrcRpmCheck(filePath) {
		return true
	}

	//拼接软件包在中心仓目录
	var centerPackDir string
	if len(pack.Developer) > 0 {
		centerPackDir = fmt.Sprintf("%sserver/%s/%s/", global.LECTOTYPE_REPO, pack.Developer, pack.ProdRelease)
	} else {
		centerPackDir = fmt.Sprintf("%sbusiness/%s/%s/", global.LECTOTYPE_REPO, pack.BizSoftForm, pack.BizSoftType)
	}

	//拷贝软件包
	dstDir := fmt.Sprintf("%s/%d/", global.SRC_PACKAGE_DIR, *xflow.Id)
	err := utils.CopyFile(pack.PackName, pack.PackName, centerPackDir, dstDir)
	if err != nil {
		slog.Error.Println("CopyPackFromCenter failed, PackName:", pack.PackName)
		return false
	}
	return true
}

// /////////////////////////////////////////////////////////////////////////////////////////////////////////
// 文件入库——分析上传源码包文件
func AnalysisSCenterPacks(scFile *SCFilePackInfo) (bool, []SCenterPackDetail, string, []CveInfo) {
	var scenterPacks []SCenterPackDetail
	// 1 解析文件上传字段，判断是否只有src.rpm结尾字段，并获取上传源码包名集合
	res, packs, cveInfos := AnalysisSFFiles(scFile)
	if !res {
		return false, scenterPacks, "文件解析失败", cveInfos
	}
	// 2 逐个判断源码包名是否已入中心仓
	// 2.1先查出所有符合开发商+开发商产品线的数据，之后拿数据进行对比
	res, packsInDB := model.Lectotype.SearchPacksBaseDev(scFile.Developers, scFile.ProdRelease, scFile.BizSoftForm, scFile.BizSoftType, "")
	if !res {
		slog.Error.Println("文件入库——获取所有符合开发商+开发商产品线数据失败")
		return false, scenterPacks, "数据查询失败", cveInfos
	}

	// 2.2 判断是否对应源码包已进中心仓
	ValidateCentralPack(packs, packsInDB, &scenterPacks)
	// 3 逐个判断源码包是否已经在入库流程中
	// 3.1先查询指定发行商与发行版本对应的流程信息
	resSC, packsInDBSC := model.SearchSCPacksBaseDev(scFile.Developers, scFile.ProdRelease, scFile.BizSoftForm, scFile.BizSoftType, "")
	if !resSC {
		slog.Error.Println("文件入库——获取所有符合开发商+开发商产品线入库数据失败")
		return false, scenterPacks, "数据查询失败", cveInfos
	}
	// 3.2判断是否对应源码包已在入库流程中
	sFilePacks := ValidateSFFileFlow(packsInDBSC, scenterPacks)

	return true, sFilePacks, "", cveInfos
}

// 解析文件入库上传文件
func AnalysisSFFiles(scFile *SCFilePackInfo) (bool, []string, []CveInfo) {
	packs := []string{}
	cveInfos := []CveInfo{}
	open, err := scFile.File.Open() //  可以通过一个*multipart.FileHeader 的open() 方法的获取一个multipart.File文件 它实现了io.Reader接口,可作为excelize.OpenReader的参数
	if err != nil {
		slog.Error.Println("AnalysisSFFiles failed:文件入库——文件解析失败")
		return false, packs, cveInfos
	}
	buffer := bufio.NewReader(open)
	for {
		l, err := buffer.ReadString('\n') //读到换行
		// 将一行中内容通过空格分割
		normalized := strings.ReplaceAll(l, "｜", "|")
		normalized = strings.TrimSpace(normalized)
		packCveInfo := strings.Split(normalized, "|")
		if len(packCveInfo) > 0 && len(packCveInfo) < 2 && strings.HasSuffix(packCveInfo[0], ".src.rpm") {
			packs = append(packs, packCveInfo[0])
		} else if len(packCveInfo) == 4 {
			packs = append(packs, packCveInfo[0])
			cveIds := strings.Split(packCveInfo[1], ",")
			for _, cveId := range cveIds {
				cveInfo := CveInfo{
					Package_name: packCveInfo[0],
					Cve_id:       cveId,
					Arch:         packCveInfo[2],
					Op_fix:       packCveInfo[3],
				}
				cveInfos = append(cveInfos, cveInfo)
			}
		}
		//for i := range packCveInfo {
		//	name := strings.TrimSpace(packNames[i])
		//	if len(name) > 0 && strings.HasSuffix(name, ".src.rpm") {
		//		packs = append(packs, name)
		//	}
		//}
		if err == io.EOF { //如果读到末尾就会进入
			break
		}
	}
	return true, packs, cveInfos
}

// 判断是否对应源码包已进中心仓
func ValidateCentralPack(packs []string, packsInDB []model.LectoPacksSearchRes, scenterPacks *[]SCenterPackDetail) {
	// 将查出的数据转为map，便于提高查询效率，键值为源码包名，值为结构体
	var packMap = make(map[string]model.LectoPacksSearchRes)
	for _, pack := range packsInDB {
		packMap[pack.PackFullname] = pack
	}
	for _, name := range packs {
		var scenterPack SCenterPackDetail
		if _, ok := packMap[name]; ok { // 2.1 判断是否存在符合开发商+开发商产品线+源码包名的数据
			// 2.2 判断是否对应源码包已进中心仓
			if packMap[name].FlowStatus == global.XFLOW_STATUS_FINISHED || packMap[name].XflowId == 0 { // 已入中心仓
				scenterPack = SCenterPackDetail{
					PackName:    name,
					LectoId:     packMap[name].XflowId,
					Developer:   packMap[name].Developers,
					ProdRelease: packMap[name].ProdRelease,
					BizSoftForm: packMap[name].BizSoftForm,
					BizSoftType: packMap[name].BizSoftType,
					Status:      global.STR_PASS,
					Msg:         "通过",
				}
			} else {
				scenterPack = SCenterPackDetail{
					PackName: name,
					LectoId:  packMap[name].XflowId,
					Status:   global.STR_NO_PASS,
					Msg:      "当前源码包尚未入中心仓",
				}
			}
		} else {
			scenterPack = SCenterPackDetail{
				PackName: name,
				Status:   global.STR_NO_PASS,
				Msg:      "未找到开发商与产品线对应源码包",
			}
		}
		*scenterPacks = append(*scenterPacks, scenterPack)
	}
}

// 判断是否已经处在入库流程中
func ValidateSFFileFlow(packsInDB []model.SCPacksSearchRes, scenterPacks []SCenterPackDetail) []SCenterPackDetail {
	res := []SCenterPackDetail{}
	// 将查出的数据转为map，便于提高查询效率，键值为源码包名，值为结构体
	var packMap = make(map[string]model.SCPacksSearchRes)
	for _, pack := range packsInDB {
		packMap[pack.PackName] = pack
	}
	// 判断软件包是否已入库
	for _, packItem := range scenterPacks {
		flag := 1
		// 仅判断为通过状态的信息
		if packItem.Status == global.STR_PASS {
			if _, ok := packMap[packItem.PackName]; ok {
				res = append(res, SCenterPackDetail{
					PackName:      packItem.PackName,
					LectoId:       packItem.LectoId,
					Status:        global.STR_NO_PASS,
					Developer:     packItem.Developer,
					ProdRelease:   packItem.ProdRelease,
					BizSoftForm:   packItem.BizSoftForm,
					BizSoftType:   packItem.BizSoftType,
					ProdId:        packItem.ProdId,
					CompileOption: packItem.CompileOption,
					Msg:           "当前源码包已在入库流程中",
				})
				flag = 2
			}
		}
		if flag == 1 {
			res = append(res, packItem)
		}
	}
	return res
}

// /////////////////////////////////////////////////////////////////////////////////////////////////////////
// 文件入库——新增
func CreateSCFile(stock *SCFileInfo, c *gin.Context) (bool, string) {
	userRes, userMsg, userItem := GetUserInfo(c)
	if !userRes {
		return false, userMsg
	}

	//总流程信息整合
	res, xflow := CreateSCenterXflow(global.XFLOW_TYPE_STOCK_CENTER, *userItem.Id)
	if !res {
		slog.Error.Println("文件入库新增——新增流程信息报错")
		UpdateXflowStatusFailed("stockcenterfile——新增流程信息报错", xflow)
		return false, global.STR_ADD_FLOWADD
	}

	//插入源码包信息
	res, packIds, msg, cveInfos := CreateACFilePacks(stock, *xflow.Id)
	if !res {
		slog.Error.Println("文件入库新增——新增软件包信息报错")
		UpdateXflowStatusFailed("stockcenterfile——新增软件包信息报错", xflow)
		return false, msg
	}

	// 入库申请附件存储
	// 存储文件类型附件
	SaveSCenterAppendix(stock.Appendix, *xflow.Id, c)
	if len(stock.Appendix_name) > 0 {
		// 存储文件名类型附件
		res = TransferSCenterAppendix(stock.Appendix_name, *xflow.Id, stock.Origin_xflow)
		if !res {
			slog.Error.Println("文件入库新增——入库申请附件文件信息转存异常")
			UpdateXflowStatusFailed("stockcenterfile——入库申请附件转存报错", xflow)
			return false, global.STR_ADD_APPENDIX
		}
	}
	//插入静态信息
	res, stockList := CreateSCFileStatic(*xflow.Id, packIds, stock, &cveInfos)
	if !res {
		slog.Error.Println("文件入库新增——入库申请静态信息新增报错")
		UpdateXflowStatusFailed("stockcenterfile——入库申请静态信息新增报错", xflow)
		return false, global.STR_ADD_STOCKSTATIC
	}
	// 入库申请评估人信息整合
	evaluatorIds := CreateEvalutes(stock.Evaluator, *xflow.Id)
	if evaluatorIds == nil {
		slog.Error.Println("文件入库新增——入库申请信息新增对应评估人报错")
		UpdateXflowStatusFailed("stockcenterfile——入库申请信息新增对应评估人报错", xflow)
		return false, global.STR_ADD_EVALUATE
	}
	//插入动态信息
	res, stockContext := CreateSCenterContext(*xflow.Id, evaluatorIds)
	if !res {
		slog.Error.Println("文件入库新增——入库申请动态信息新增报错")
		UpdateXflowStatusFailed("stockcenterfile——入库申请动态信息新增报错", xflow)
		return false, global.STR_ADD_STOCKCONTEXT
	}
	//用动态与静态信息更新流程表
	res = UpdateXlowRelatedList(*stockList.Id, *stockContext.Id, xflow)
	if !res {
		slog.Error.Println("文件入库新增——更新流程信息报错")
		UpdateXflowStatusFailed("stockcenterfile——更新流程信息报错", xflow)
		return false, global.STR_ADD_FLOWUPDATE
	}
	//表数据已全部插入
	return true, ""
}

// 文件入库——新增软件包信息
func CreateACFilePacks(stock *SCFileInfo, xflowId int) (bool, []int, string, []CveInfo) {
	var packIds []int
	scFilePack := SCFilePackInfo{
		Developers:  stock.Developer,
		ProdRelease: stock.ProdRelease,
		BizSoftForm: stock.BizSoftForm,
		BizSoftType: stock.BizSoftType,
		File:        stock.File,
	}
	_, scFilePacks, _, cveInfos := AnalysisSCenterPacks(&scFilePack)
	if len(scFilePacks) <= 0 {
		slog.Error.Println("文件入库新增——上传文件中未检测到软件包名称")
		return false, packIds, "未在您上传的文件中检测到软件包名称，请确认您的上传文件内容是否正常", cveInfos
	}
	for _, item := range scFilePacks {
		if item.Status == global.STR_PASS {
			stockPack := model.StockCenterPackList{
				Xflow_id:    xflowId,
				PackName:    item.PackName,
				Kojis:       []int{},
				LectoId:     item.LectoId,
				Developer:   item.Developer,
				ProdRelease: item.ProdRelease,
				BizSoftForm: item.BizSoftForm,
				BizSoftType: item.BizSoftType,
				ProdId:      stock.ProdId,
				CmdOption:   stock.CompileOption,
			}
			res := model.CreateStcPackList(&stockPack)
			if !res {
				slog.Error.Println("文件入库新增——软件包新增报错")
				return false, packIds, "您提交的软件包创建异常，请联系管理员协助您处理", cveInfos
			}
			packIds = append(packIds, *stockPack.Id)
		} else {
			slog.Error.Println("文件入库新增——分析入库文件发现未通过检测软件包")
			return false, packIds, "您上传的文件中，存在不可以提交入库的软件包名称，请确认您的上传文件内容", cveInfos
		}
	}

	return true, packIds, "", cveInfos
}

// 文件入库——创建流程静态信息
func CreateSCFileStatic(xflow_id int, packIds []int, stock *SCFileInfo, cveInfos *[]CveInfo) (bool, *model.StockCenterList) {
	cveJson, _ := json.Marshal(cveInfos)
	stockCenterStatic := model.StockCenterList{
		Xflow_id:        xflow_id,
		Packs:           packIds,
		Supplement_info: stock.SupplementInfo,
		Test_info:       stock.TestInfo,
		Appendix:        GetAppendixName(stock.Appendix, stock.Appendix_name),
		CveInfo:         string(cveJson),
		CreatedAt:       time.Now(),
		UpdatedAt:       time.Now(),
		Stock_type:      global.STOCK_TYPE_CenterF,
	}
	return model.CreateStockCenterList(&stockCenterStatic), &stockCenterStatic
}

// /////////////////////////////////////////////////////////////////////////////////////////////////////////
/*
	文件入库——新增按拆分创建
*/

// 新增方法
func CreateSCFileSplit(stock *SCFileInfo, c *gin.Context) (bool, []SCFileSplitRelation, string) {
	relation := []SCFileSplitRelation{}
	userRes, userMsg, userItem := GetUserInfo(c)
	if !userRes {
		return false, relation, userMsg
	}

	// 先整合解析文件，获取所有源码包
	scFilePack := SCFilePackInfo{
		Developers:  stock.Developer,
		ProdRelease: stock.ProdRelease,
		BizSoftForm: stock.BizSoftForm,
		BizSoftType: stock.BizSoftType,
		File:        stock.File,
	}
	_, scFilePacks, _, cveInfos := AnalysisSCenterPacks(&scFilePack)
	if len(scFilePacks) <= 0 {
		slog.Error.Println("文件入库拆分新增新增——上传文件中未检测到软件包名称")
		return false, relation, "您上传的文件中不存在可入库软件包信息"
	}

	// 创建总流程
	xflowList := CreateSFXflowBacth(*userItem.Id, scFilePacks)
	if len(xflowList) <= 0 {
		return false, relation, "数据库操作失败，请联系管理员"
	}
	// 创建包列表
	for i := range scFilePacks {
		scFilePacks[i].ProdId = stock.ProdId
		scFilePacks[i].CompileOption = stock.CompileOption
	}
	relation = CreateSFPackBacth(xflowList, scFilePacks)

	// 存储附件
	SaveSCenterAppendixBatch(stock, &relation, c)

	// 插入静态表信息
	for i := range cveInfos {
		cveInfos[i].Product_id = strconv.Itoa(stock.ProdId)
	}
	CreateSFStaticBacth(stock, &relation, &cveInfos)

	// 插入评估人信息
	CreateSFEvaluators(stock.Evaluator, &relation)

	// 插入动态表信息
	CreateSFContextBatch(&relation)

	// 用动态与静态信息更新流程表
	UpdateSFXflowBatch(&relation)
	return true, relation, ""
}

// 批量创建流程
func CreateSFXflowBacth(userId int, packLists []SCenterPackDetail) []model.Xflow {
	xflowLists := []model.Xflow{}
	for _, item := range packLists {
		// 整理批量插入数据，仅插入已通过检测的数据
		if item.Status == global.STR_PASS {
			// 整理批量插入主流程数组
			xflowLists = append(xflowLists, model.Xflow{
				Type:    global.XFLOW_TYPE_STOCK_CENTER,
				Status:  global.XFLOW_STATUS_PENDING,
				User_id: userId,
			})
		}
	}
	// 批量插入流程列表
	model.CreateXflowBatch(&xflowLists)
	return xflowLists
}

func CreateSFPackBacth(xflowLists []model.Xflow, packLists []SCenterPackDetail) []SCFileSplitRelation {
	relation := []SCFileSplitRelation{}
	scPackLists := []model.StockCenterPackList{}
	index := 0
	// 整理插入包列表
	// 整理批量插入包列表数组
	for _, item := range packLists {
		relationItem := SCFileSplitRelation{}
		// 已通过检测且存在对应流程编号
		if item.Status == global.STR_PASS && len(xflowLists) > index {
			stockPack := model.StockCenterPackList{
				Xflow_id:    *xflowLists[index].Id,
				PackName:    item.PackName,
				Kojis:       []int{},
				LectoId:     item.LectoId,
				Developer:   item.Developer,
				ProdRelease: item.ProdRelease,
				BizSoftForm: item.BizSoftForm,
				BizSoftType: item.BizSoftType,
				ProdId:      item.ProdId,
				CmdOption:   item.CompileOption,
			}
			scPackLists = append(scPackLists, stockPack)
			relationItem = SCFileSplitRelation{
				PackName: item.PackName,
				XflowId:  *xflowLists[index].Id,
				Xflow:    xflowLists[index],
			}
			index++
		} else if item.Status == global.STR_NO_PASS { // 未通过检测数据
			relationItem = SCFileSplitRelation{
				PackName: item.PackName,
				Status:   global.STR_NO_PASS,
				Msg:      item.Msg,
			}
		} else { // 通过检测但不存在对应流程ID
			relationItem = SCFileSplitRelation{
				PackName: item.PackName,
				Status:   global.STR_NO_PASS,
				Msg:      "流程创建失败",
			}
		}
		relation = append(relation, relationItem)
	}
	// 批量创建包数据
	if err := model.CreateStcPackListBacth(&scPackLists); err != nil {
		slog.Error.Println(err)
		fmt.Println(err)
		return nil
	}
	// 整理插入表格数据
	// 将查出的数据转为map，便于提高查询效率，键值为源码包名，值为结构体
	var packMap = make(map[string]model.StockCenterPackList)
	for _, pack := range scPackLists {
		packMap[pack.PackName] = pack
	}
	// 将包信息整合到relation中
	for i, relationItem := range relation {
		// 若map中存在对应包，则将relation中packid字段进行更新，否则设置对应信息为失败，失败原因为“新增软件包信息报错”
		if _, ok := packMap[relationItem.PackName]; ok {
			relation[i].PackId = *packMap[relationItem.PackName].Id
		} else if relationItem.Status == global.STR_PASS {
			relation[i].Status = global.STR_NO_PASS
			relation[i].Msg = "新增软件包信息报错"
		}
	}
	return relation
}
func SaveSCenterAppendixBatch(stock *SCFileInfo, relation *[]SCFileSplitRelation, c *gin.Context) {
	for _, item := range *relation {
		if item.Status != global.STR_NO_PASS && item.XflowId > 0 {
			for _, file := range stock.Appendix {
				// 上传文件到指定的目录
				// 设置文件需要保存的指定位置并设置保存的文件名字
				dst := path.Join(global.STOCK_CENTER_APPENDIX_REPO, strconv.Itoa(item.XflowId), file.Filename)
				c.SaveUploadedFile(file, dst)
			}
			dst := path.Join(global.STOCK_CENTER_APPENDIX_REPO, strconv.Itoa(item.XflowId), stock.File.Filename)
			c.SaveUploadedFile(stock.File, dst)
		}
	}
}
func CreateSFStaticBacth(stock *SCFileInfo, relation *[]SCFileSplitRelation, cveInfos *[]CveInfo) {
	stockStaticList := []model.StockCenterList{}
	appendix := GetAppendixName(stock.Appendix, stock.Appendix_name)

	cveMap := make(map[string]string)
	for _, item := range *cveInfos {
		cveJson, _ := json.Marshal(item)
		cveMap[item.Package_name] = string(cveJson)
	}
	for _, item := range *relation {
		if item.Status != global.STR_NO_PASS {
			stockStaticList = append(stockStaticList, model.StockCenterList{
				Xflow_id:        item.XflowId,
				Packs:           []int{item.PackId},
				Supplement_info: stock.SupplementInfo,
				Test_info:       stock.TestInfo,
				Appendix:        appendix,
				CveInfo:         cveMap[item.PackName],
				CreatedAt:       time.Now(),
				UpdatedAt:       time.Now(),
				Stock_type:      global.STOCK_TYPE_CenterF,
			})
		}
	}
	model.CreateStockCenterListBatch(&stockStaticList)
	// 将查出的数据转为map，便于提高查询效率，键值为源码包名，值为结构体
	var staticMap = make(map[int]int)
	for _, item := range stockStaticList {
		staticMap[item.Xflow_id] = *item.Id
	}
	// 将包信息整合到relation中
	for relationIndex, relationItem := range *relation {
		// 若map中存在对应流程，则将relation中staticid字段进行更新，否则设置对应信息为失败，失败原因为“入库申请静态信息新增报错”
		if _, ok := staticMap[relationItem.XflowId]; ok {
			(*relation)[relationIndex].StaticId = staticMap[relationItem.XflowId]
		} else if relationItem.Status == global.STR_PASS {
			(*relation)[relationIndex].Status = global.STR_NO_PASS
			(*relation)[relationIndex].Msg = "入库申请静态信息新增报错"
		}
	}
}
func CreateSFEvaluators(evalutors []int, relation *[]SCFileSplitRelation) {
	evaluatorList := []model.EvaluateList{}
	for _, item := range *relation {
		if item.Status != global.STR_NO_PASS {
			for _, evaluator := range evalutors {
				evaluatorList = append(evaluatorList, model.EvaluateList{
					Evaluate_type: global.EVALUATE_TYPE_EVALUATE,
					Xflow_id:      item.XflowId,
					User_id:       evaluator,
					Opinion:       "",
					Msg:           "",
					CommentMsg:    "",
					CreatedAt:     time.Now(),
					UpdatedAt:     time.Now(),
				})
			}
		}
	}
	model.CreateEvaluateListBatch(&evaluatorList)
	// 将查出的数据转为map，便于提高查询效率，键值为源码包名，值为结构体
	var evaluatorsMap = make(map[int][]int)
	for _, item := range evaluatorList {
		if _, ok := evaluatorsMap[item.Xflow_id]; ok {
			evaluatorsMap[item.Xflow_id] = append(evaluatorsMap[item.Xflow_id], *item.Id)
		} else {
			evaluatorsMap[item.Xflow_id] = []int{*item.Id}
		}
	}
	// 将包信息整合到relation中
	for relationIndex, relationItem := range *relation {
		// 若map中存在对应流程，则将relation中Evaluators字段进行更新，否则设置对应信息为失败，失败原因为“入库申请信息新增对应评估人报错”
		if _, ok := evaluatorsMap[relationItem.XflowId]; ok {
			(*relation)[relationIndex].EvaluatorsId = evaluatorsMap[relationItem.XflowId]
		} else if relationItem.Status == global.STR_PASS {
			(*relation)[relationIndex].Status = global.STR_NO_PASS
			(*relation)[relationIndex].Msg = "入库申请信息新增对应评估人报错"
		}
	}
}
func CreateSFContextBatch(relation *[]SCFileSplitRelation) {
	contextLists := []model.StockCenterContext{}
	for _, item := range *relation {
		if item.Status != global.STR_NO_PASS {
			contextLists = append(contextLists, model.StockCenterContext{
				Xflow_id:     item.XflowId,
				Current_flow: global.STOCK_CENTER_FLOW_SUBMIT,
				Hook_status:  global.HOOK_TESTING,
				Review_list:  item.EvaluatorsId,
				CreatedAt:    time.Now(),
				UpdatedAt:    time.Now(),
			})
		}
	}
	model.CreateStockCenterContextBatch(&contextLists)
	// 将查出的数据转为map，便于提高查询效率，键值为源码包名，值为结构体
	var contextMap = make(map[int]int)
	for _, item := range contextLists {
		contextMap[item.Xflow_id] = *item.Id
	}
	// 将包信息整合到relation中
	for relationIndex, relationItem := range *relation {
		// 若map中存在对应流程，则将relation中Evaluators字段进行更新，否则设置对应信息为失败，失败原因为“入库申请动态信息新增报错”
		if _, ok := contextMap[relationItem.XflowId]; ok {
			(*relation)[relationIndex].ContextId = contextMap[relationItem.XflowId]
		} else if relationItem.Status == global.STR_PASS {
			(*relation)[relationIndex].Status = global.STR_NO_PASS
			(*relation)[relationIndex].Msg = "入库申请动态信息新增报错"
		}
	}
}
func UpdateSFXflowBatch(relation *[]SCFileSplitRelation) {
	// 整理更新数据
	for _, item := range *relation {
		if item.Status != global.STR_NO_PASS {
			xflow := item.Xflow
			xflow.Table_id = item.StaticId
			xflow.Context_id = item.ContextId
			xflow.Status = global.XFLOW_STATUS_ACTIVE
			model.UpdateXflow(&xflow)
		}
	}
}

// /////////////////////////////////////////////////////////////////////////////////////////////////////////
// 文件入库——详情
func GetScFileDetail(id int) *SCFileDetail {
	xflow := model.GetXflowById(id)
	var scFileDetail SCFileDetail
	stockList := model.GetStockCenterListByXflowId(id)
	stockContext := model.GetStockCenterContextByXflowId(id)
	if xflow == nil || stockList == nil || stockContext == nil || *xflow == (model.Xflow{}) {
		slog.Error.Println("GetScFileDetail failed")
		return nil
	}
	// 创建用户信息绑定
	user := model.GetUserById(xflow.User_id)
	packsInfo := GetStockDetailPacks(stockList.Packs)
	scFileDetail = SCFileDetail{
		Id:              *xflow.Id,
		StockId:         *stockList.Id,
		StockContextId:  *stockContext.Id,
		Flow_status:     xflow.Status,
		Stop_reason:     xflow.Stop_reason,
		Create_time:     xflow.CreatedAt.Format(global.TIME_FORMAT),
		Create_user:     *user,
		Current_flow:    stockContext.Current_flow,
		Packs_info:      packsInfo,
		Evaluator:       GetDetailEvaluators(id),
		Supplement_info: stockList.Supplement_info,
		Test_info:       stockList.Test_info,
		Appendix:        stockList.Appendix,
		Developer:       packsInfo[0].Developer,
		ProdRelease:     packsInfo[0].ProdRelease,
		ProdId:          packsInfo[0].ProdId,
		Framwork:        GetArchsByProductId(packsInfo[0].ProdId),
		File:            stockList.Appendix,
		Stock_type:      stockList.Stock_type,
	}
	return &scFileDetail
}

// /////////////////////////////////////////////////////////////////////////////////////////////////////////
// 文件入库/源码包入库——回撤
func WithDrawSCenter(id int) (bool, string) {
	xflow := model.GetXflowById(id)
	// 判断当前流程是否处于可撤回状态，若入库申请处于已审核之后的状态或者流程处于失败或成功状态
	if xflow.Status == global.XFLOW_STATUS_REVOKE {
		return false, "当前申请已经被撤回，请再次确认"
	} else if xflow.Status == global.XFLOW_STATUS_FINISHED || xflow.Status == global.XFLOW_STATUS_FAILED {
		return false, "当前申请不能再撤回，请确认当前申请流转状态"
	}
	stockContext := model.GetStockCenterContextByXflowId(id)
	if stockContext == nil {
		return false, "未找到当前申请流转状态信息"
	}
	//判断当前状态是否可以撤回
	if stockContext.Current_flow == global.STOCK_CENTER_FLOW_BUILD ||
		stockContext.Current_flow == global.STOCK_CENTER_FLOW_IMPORT ||
		stockContext.Current_flow == global.STOCK_CENTER_FLOW_ARCHIVE {
		return false, "当前状态无法撤回"
	}

	stockContext.Current_flow = global.STOCK_CENTER_FLOW_REVOKE
	if !model.UpdateStockCenterContext(stockContext) {
		return false, "当前申请未成功撤回，请稍后再试"
	}

	xflow.Status = global.XFLOW_STATUS_REVOKE
	if !model.UpdateXflow(xflow) {
		return false, "当前申请未成功撤回，请稍后再试"
	} else {
		return true, ""
	}
}

// /////////////////////////////////////////////////////////////////////////////////////////////////////////
// 启动Hook检测
func FireSCenterHook(xflow *model.Xflow, cont *model.StockCenterContext, authorId int) (bool, string) {
	SetHookPartitionContSCenter(cont)
	if xflow.Type != global.XFLOW_TYPE_STOCK_CENTER {
		return false, "当前申请不是中心仓入库申请，请确认"
	}
	stockList := model.GetStockCenterListByXflowId(*xflow.Id)
	if stockList == nil {
		return false, "未找到当前中心仓入库申请详细信息，请确认"
	}
	rules := GetStockRulesSCenter()
	if len(rules) <= 0 {
		return false, "未找到当前中心仓入库申请对应检测规则，请确认"
	}
	kojis := GetSCenterKojis(stockList)
	for _, koji := range kojis {
		kojiIp := koji.Koji_Ip
		kojiIp = strings.Replace(kojiIp, "http://", "", -1)
		kojiIp = strings.Replace(kojiIp, "https://", "", -1)
		if authorId == 0 {
			authorId = xflow.User_id
		}
		// 转换commit_type
		res, commitType := GetCommitType(global.XFLOW_TYPE_STOCK_CENTER, "")
		if !res {
			return false, "当前入库申请组件来源未正确解析，请确认"
		}
		hookParams := model.HookParams{
			Id:           *xflow.Id,
			Author_id:    authorId,
			Koji_id:      koji.Koji_id,
			Koji_ip:      kojiIp,
			Tags:         "",
			Package_name: koji.Src_name,
			Platform:     1,
			Commit_type:  commitType,
			Koji_info: model.KojiCelery{
				Package_path:  fmt.Sprintf("%s/%d/%s", global.SRC_PACKAGE_DIR, *xflow.Id, koji.Src_name),
				Src_name:      koji.Src_name,
				Task_id:       koji.Koji_taskid,
				Id:            strconv.Itoa(*xflow.Id),
				Koji_ip:       kojiIp,
				Tags:          "",
				Is_del:        "0",
				Update_on:     koji.UpdatedAt,
				Pid:           strconv.Itoa(*xflow.Id),
				Commit_type:   commitType,
				Koji_url:      koji.Koji_url,
				Tag:           "",
				Created_on:    koji.CreatedAt,
				Warehouse_url: koji.Stock_url,
				Version_id:    "",
				Package_name:  koji.Src_name,
				Name:          koji.Src_name,
			},
			Info_detail: "{\"cve_info\": [],\"bug_info\": []}",
		}
		if !model.SendSCenterHook(hookParams, rules, cont) {
			return false, "发起Hook检测失败，请确认"
		}
	}

	return true, ""
}

// 分割历史hook检测与本次检测
func SetHookPartitionContSCenter(cont *model.StockCenterContext) {
	// 将hook——id放入对应入库申请检测结果上下文中
	cont.Hook_id = append(cont.Hook_id, "0")
	model.UpdateStockCenterContext(cont)
}

// 获取当前中心仓入库申请对应的检测规则
func GetStockRulesSCenter() []string {
	var rules []string
	form := model.GetFormListByName("开源软件入库申请")
	if form == nil { // 若为空数组，则返回报错
		return rules
	}
	ruleForms := model.GetRuleFormsByFormId(*form.Id)
	for _, ruleForm := range ruleForms {
		if ruleForm != (model.RuleFormList{}) && ruleForm.Is_del == 0 {
			rule := model.GetRuleById(ruleForm.Rule_id)
			if rule != nil {
				rules = append(rules, rule.Rule_id)
			}
		}
	}
	return rules
}

// 获取中心仓入库申请关联的所有Kojis地址
func GetSCenterKojis(stockList *model.StockCenterList) []StockKojiDetail {
	var stockKojis []StockKojiDetail
	for _, pack := range stockList.Packs {
		packItem := model.GetStcListById(pack)
		if packItem != nil && packItem != &(model.StockCenterPackList{}) {
			stockKojis = *MergeStockKojis(stockKojis, GetStockDetailKojis(packItem.Kojis))
		} else {
			return []StockKojiDetail{}
		}
	}
	return stockKojis
}

// /////////////////////////////////////////////////////////////////////////////////////////////////////////

// 检测单个包是否符合规范 new
func VerifyStockCenterPackage(search *SCenterPackageVerify) (bool, string) {
	if global.SwitchStockCenterCheck == global.SWITCH_OFF {
		//配置文件关闭入口检测
		return true, ""
	}
	// 检查当前源码包是否已入中心仓
	if ret, packs := CheckSCenterPackageExist(search); !ret {
		reason := "源码包尚未入中心仓:" + strings.Join(packs, ",")
		return false, reason
	}
	// 检查当前包是否已入库或在其他运行流程中，包含产品继承关系
	res, reason := CheckSCenterPackageStock(search)
	if !res {
		return false, reason
	}
	return true, ""
}

// 检验当前软件包是否已入中心仓 new
func CheckSCenterPackageExist(search *SCenterPackageVerify) (bool, []string) {
	var packs []string
	// 在软件选型模块查找对应源码包
	for _, packName := range search.PackName {
		lectPack := model.LectotypePackageList{
			PackFullname: packName,
			ProdRelease:  search.ProdRelease,
			Developers:   search.Developers,
			BizSoftForm:  search.BizSoftForm,
			BizSoftType:  search.BizSoftType,
		}
		lectList := model.Lectotype.GetLectotypePackageListByNDR(&lectPack)
		if lectList == nil {
			packs = append(packs, packName)
		}
	}
	if len(packs) != 0 {
		return false, packs
	}
	return true, []string{}
}

// 检验当前软件包是否已在其余入库申请流程中 new
func CheckSCenterPackageStock(search *SCenterPackageVerify) (bool, string) {
	var reasons []string
	if search == nil {
		reason := fmt.Sprintf("参数传递有误")
		slog.Error.Println(reason)
		return false, reason
	}
	// 查询入库产品所在koji平台
	productList := model.GetProductListById(search.ProdId)
	if productList == nil {
		reason := fmt.Sprintf("产品信息查询异常:%d", search.ProdId)
		slog.Error.Println(reason)
		return false, reason
	}
	//查询产品所在Koji平台信息
	kojiPlatforms := model.Koji.GetKojiPlatformByIds(productList.ProductKojis)
	if kojiPlatforms == nil {
		reason := fmt.Sprintf("koji平台信息查询异常:%v", productList.ProductKojis)
		slog.Error.Println(reason)
		return false, reason
	}
	//查询产品tag信息
	tagList := model.GetTagListById(productList.Tag)
	if tagList == nil {
		reason := fmt.Sprintf("产品tag信息查询异常:%v", productList.Tag)
		slog.Error.Println(reason)
		return false, reason
	}
	//循环所有koji平台，查询软件包是否已入库
	for i := range kojiPlatforms {
		koji, ret := GetPackageAlreadyStock(kojiPlatforms[i].Addr, tagList.Target, search.PackName, search.CompileOption)
		if !ret {
			reason := fmt.Sprintf("产品入库信息查询异常:%v, %s, %s", search.PackName, kojiPlatforms[i].Addr, tagList.Target)
			slog.Error.Println(reason)
			return false, reason
		}
		if len(koji) != 0 {
			//将每个平台的入库包都显示出来
			var packs []string
			for i := range koji {
				packs = append(packs, koji[i].Src_name_origin)
			}
			reason := fmt.Sprintf("%s:%v", kojiPlatforms[i].Addr, packs)
			reasons = append(reasons, reason)
		}
	}
	if len(reasons) != 0 {
		reason := fmt.Sprintf("软件包已入库:%v", reasons)
		slog.Error.Println(reason)
		return false, reason
	}
	//循环所有koji平台，查询软件包是否在其他流程中
	for i := range kojiPlatforms {
		koji, ret := GetPackageStocking(kojiPlatforms[i].Addr, tagList.Target, search.PackName, search.CompileOption)
		if !ret {
			reason := fmt.Sprintf("产品入库信息查询异常:%v, %s, %s", search.PackName, kojiPlatforms[i].Addr, tagList.Target)
			slog.Error.Println(reason)
			return false, reason
		}
		if len(koji) != 0 {
			//将每个平台的正在流程中的包都显示出来
			var packs []string
			for i := range koji {
				packs = append(packs, koji[i].Src_name_origin)
			}
			reason := fmt.Sprintf("%s:%v", kojiPlatforms[i].Addr, packs)
			reasons = append(reasons, reason)
		}
	}
	if len(reasons) != 0 {
		reason := fmt.Sprintf("软件包在其他流程中正在入库:%v", reasons)
		slog.Error.Println(reason)
		return false, reason
	}
	return true, ""
}

// /////////////////////////////////////////////////////////////////////////////////////////////////////////
// 重启
func RebuildStockC(xflowId int) bool {
	scContext := model.GetStockCenterContextByXflowId(xflowId)
	if scContext == nil {
		return false
	}
	if scContext.Current_flow == global.STOCK_CENTER_FLOW_SCRATCH_BUILD_FAILED {
		scContext.Current_flow = global.STOCK_CENTER_FLOW_SCRATCH_BUILD_RETRY
	}
	if scContext.Current_flow == global.STOCK_CENTER_FLOW_BUILD_FAILED {
		scContext.Current_flow = global.STOCK_CENTER_FLOW_BUILD_RETRY
	}
	if scContext.Current_flow == global.STOCK_CENTER_FLOW_IMPORT_FAILED {
		scContext.Current_flow = global.STOCK_CENTER_FLOW_IMPORT_RETRY
	}
	if !model.UpdateStockCenterContext(scContext) {
		return false
	}
	return true
}
