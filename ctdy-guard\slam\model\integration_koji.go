/*
 * @Author: lwq <EMAIL>
 * @Date: 2025-05-18
 * @LastEditors: lwq <EMAIL>
 * @LastEditTime: 2025-07-22
 * @FilePath: /ctdy-guard/slam/model/integration_koji.go
 * @Description: 集成单koji构建数据模型
 * @Copyright (c) 2025 by 2022.kylinos.cn, All Rights Reserved.
 */
package model

import (
	"time"

	"gorm.io/gorm"
)

type IntegrationKoji struct {
	Id            int       `gorm:"column:id;primaryKey;autoIncrement:true;type:serial4;not null" json:"id,omitempty" label:"序号，主键"`
	IntegrationId int       `gorm:"column:integration_id;not null" json:"integration_id" label:"集成单ID"`
	IpAddress     string    `gorm:"column:ip_address;type:varchar(255);not null;default:''" json:"ip_address" label:"IP地址"`
	Nvr           string    `gorm:"column:nvr;type:varchar(255);not null;default:''" json:"nvr" label:"NVR信息"`
	PackageName   string    `gorm:"column:package_name;type:varchar(255);not null;default:''" json:"package_name" label:"软件包信息"`
	CreatedAt     time.Time `gorm:"column:create_time;not null" json:"create_time,omitempty" label:"创建时间"`
	UpdatedAt     time.Time `gorm:"column:update_time;not null" json:"update_time,omitempty" label:"更新时间"`
}

// TableName 表名
func (*IntegrationKoji) TableName() string {
	return "slam_integration_koji"
}

// IntegrationKojiRepo
type IntegrationKojiRepo interface {
	BatchCreateKoji(igKojis []*IntegrationKoji) error
}

type IntegrationKojiModel struct {
	db *gorm.DB
}

func NewIntegrationKojiModel() IntegrationKojiRepo {
	return &IntegrationKojiModel{
		db: GetDB(),
	}
}

func (m *IntegrationKojiModel) BatchCreateKoji(igKojis []*IntegrationKoji) error {
	return m.db.Create(igKojis).Error
}
