/*
 * @Author: lwq <EMAIL>
 * @Date: 2025-05-08
 * @LastEditors: lwq <EMAIL>
 * @LastEditTime: 2025-08-01
 * @FilePath: /ctdy-guard/slam/api/v1/integration.go
 * @Description: 集成单Api接口
 * @Copyright (c) 2025 by 2022.kylinos.cn, All Rights Reserved.
 */
package v1

import (
	"fmt"
	"net/http"
	"os"
	"path/filepath"
	"slam/task/integration"
	"slam/task/integration/option"
	"slam/utils"
	"strconv"

	"github.com/gin-gonic/gin"
)

// IntegrationController 集成单Api接口
type IntegrationController struct {
	igTask integration.IntegrationInterface
}

// 请求参数结构体
type ListReq struct {
	Id       int `json:"id" form:"id" binding:"required,min=1"`
	Page     int `json:"page" form:"page" binding:"required,min=1"`
	PageSize int `json:"page_size" form:"page_size" binding:"required,min=1,max=100"`
}

type CheckReq struct {
	Ids         []int  `json:"ids" form:"ids" binding:"required,min=1"`
	IgId        int    `json:"igid" form:"igid" binding:"required,min=1"`
	CheckResult int    `json:"check_result" form:"check_result" binding:"required,oneof=1 2"`
	CheckAdvice string `json:"check_advice" form:"check_advice" binding:"required"`
	CheckType   int    `json:"check_type" form:"check_type" binding:"required,oneof=1 2"`
}

type FlagReq struct {
	Id          int    `json:"id" form:"id" binding:"required,min=1"`
	IgId        int    `json:"igid" form:"igid" binding:"required,min=1"`
	CheckResult int    `json:"check_result" form:"check_result" binding:"required,oneof=1 2"`
	CheckAdvice string `json:"check_advice" form:"check_advice" binding:"required"`
	CheckType   int    `json:"check_type" form:"check_type" binding:"required,oneof=1 2"`
}

type EditIgReq struct {
	IgId   int    `json:"id" form:"id" binding:"required,min=1"`
	IgName string `json:"alias_name" form:"alias_name"`
	Remark string `json:"remark" form:"remark"`
}

type LockIgReq struct {
	IgId int `json:"id" form:"id" binding:"required,min=1"`
	Lock int `json:"lock" form:"lock" binding:"oneof=1 2"`
}

type ReplenishReq struct {
	IgId    int    `json:"ig_id"`
	SrcName string `json:"src_name"`
	BugInfo string `json:"bug_info"`
	CveInfo string `json:"cve_info"`
}

type AddPackageReq struct {
	IgId    int `json:"igid" form:"igid" binding:"required,min=1"`
	XflowId int `json:"xflow_id" form:"xflow_id" binding:"required,min=1"`
}

/**
 * @author: lwq <EMAIL>
 * @description: 集成单Api接口构造函数
 * @return {*IntegrationController}
 */
func NewIntegrationController() *IntegrationController {
	return &IntegrationController{
		igTask: integration.NewIntegration(),
	}
}

/**
 * @author: lwq <EMAIL>
 * @description: 创建集成单
 * @return {*}
 */
func (s *IntegrationController) HandleCreateIntegration(c *gin.Context) {
	_, err := s.igTask.CreateIntegration(c)
	if err != nil {
		response := utils.NewResponseFailed()
		response.Msg = err.Error()
		utils.ResponseFaied(c, response)
	} else {
		response := utils.NewResponseSuccess()
		utils.ResponseSuccess(c, response)
	}
}

/**
 * @author: lwq <EMAIL>
 * @description: 编辑集成单名称和备注
 * @param {*gin.Context} c
 * @return {*}
 */
func (s *IntegrationController) HandleEditIntegration(c *gin.Context) {
	var req EditIgReq
	if err := c.ShouldBind(&req); err != nil {
		response := utils.NewResponseFailed()
		response.Msg = err.Error()
		utils.ResponseFaied(c, response)
		return
	}
	editIgReq := option.EditIgReq(req)

	_, err := s.igTask.EditIntegration(c, &editIgReq)
	if err != nil {
		response := utils.NewResponseFailed()
		response.Msg = err.Error()
		utils.ResponseFaied(c, response)
	} else {
		response := utils.NewResponseSuccess()
		utils.ResponseSuccess(c, response)
	}
}

/**
 * @author: lwq <EMAIL>
 * @description: 手动触发集成单状态流转
 * @param {*gin.Context} c
 * @return {*}
 */
func (s *IntegrationController) HandleRotateIntegration(c *gin.Context) {
	igid, _ := strconv.Atoi(c.Param("igid"))

	_, err := s.igTask.RotateStatus(c, igid)
	if err != nil {
		response := utils.NewResponseFailed()
		response.Msg = err.Error()
		utils.ResponseFaied(c, response)
	} else {
		response := utils.NewResponseSuccess()
		utils.ResponseSuccess(c, response)
	}
}

/**
 * @author: lwq <EMAIL>
 * @description: 加解锁集成单
 * @param {*gin.Context} c
 * @return {*}
 */
func (s *IntegrationController) HandleLockIntegration(c *gin.Context) {
	var req LockIgReq
	if err := c.ShouldBind(&req); err != nil {
		response := utils.NewResponseFailed()
		response.Msg = err.Error()
		utils.ResponseFaied(c, response)
		return
	}

	_, err := s.igTask.LockIntegration(c, req.IgId, req.Lock)
	if err != nil {
		response := utils.NewResponseFailed()
		response.Msg = err.Error()
		utils.ResponseFaied(c, response)
	} else {
		response := utils.NewResponseSuccess()
		utils.ResponseSuccess(c, response)
	}
}

/**
 * @author: lwq <EMAIL>
 * @description: HandleIntegrationList 分页集成单列表
 * @param {*gin.Context} c
 * @return {*}
 */
func (s *IntegrationController) HandleIntegrationList(c *gin.Context) {
	igid, _ := strconv.Atoi(c.Query("igid"))
	urgent, _ := strconv.Atoi(c.Query("urgent"))
	status, _ := strconv.Atoi(c.Query("status"))
	pager := utils.GetPageInfo(c)
	req := integration.PacksListReq{
		IgId:      igid,
		SrcName:   c.Query("src_name"),
		StartDate: c.Query("start_date"),
		EndDate:   c.Query("end_date"),
		Urgent:    urgent,
		Status:    status,
	}

	res, err := s.igTask.GetIntegrationList(pager, req)
	if err != nil {
		response := utils.NewResponseFailed()
		response.Msg = err.Error()
		utils.ResponseFaied(c, response)
	} else {
		response := utils.NewResponseSuccess()
		response.Data = res
		utils.ResponseSuccess(c, response)
	}
}

/**
 * @author: lwq <EMAIL>
 * @description: 集成单详情
 * @param {*gin.Context} c
 * @return {*}
 */
func (s *IntegrationController) HandleIntegrationDetail(c *gin.Context) {
	igid, _ := strconv.Atoi(c.Param("igid"))

	res, err := s.igTask.GetIntegrationById(igid)
	if err != nil {
		response := utils.NewResponseFailed()
		response.Msg = err.Error()
		utils.ResponseFaied(c, response)
	} else {
		response := utils.NewResponseSuccess()
		response.Data = res
		utils.ResponseSuccess(c, response)
	}
}

/**
 * @author: lwq <EMAIL>
 * @description: 分页集成单列表-bug
 * @param {*gin.Context} c
 * @return {*}
 */
func (s *IntegrationController) HandleIntegrationBugList(c *gin.Context) {
	igid, _ := strconv.Atoi(c.Param("igid"))
	srcName := c.Query("src_name")
	mine, _ := strconv.Atoi(c.Query("mine"))
	pager := utils.GetPageInfo(c)
	req := integration.PacksListReq{
		IgId:    igid,
		Uid:     mine,
		SrcName: srcName,
	}

	res, err := s.igTask.GetIntegrationBugList(c, req, pager)
	if err != nil {
		response := utils.NewResponseFailed()
		response.Msg = err.Error()
		utils.ResponseFaied(c, response)
	} else {
		response := utils.NewResponseSuccess()
		response.Data = res
		utils.ResponseSuccess(c, response)
	}
}

/**
 * @author: lwq <EMAIL>
 * @description: 分页集成单列表-cve
 * @param {*gin.Context} c
 * @return {*}
 */
func (s *IntegrationController) HandleIntegrationCveList(c *gin.Context) {
	igid, _ := strconv.Atoi(c.Param("igid"))
	srcName := c.Query("src_name")
	mine, _ := strconv.Atoi(c.Query("mine"))
	pager := utils.GetPageInfo(c)
	req := integration.PacksListReq{
		IgId:    igid,
		Uid:     mine,
		SrcName: srcName,
	}

	res, err := s.igTask.GetIntegrationCveList(c, req, pager)
	if err != nil {
		response := utils.NewResponseFailed()
		response.Msg = err.Error()
		utils.ResponseFaied(c, response)
	} else {
		response := utils.NewResponseSuccess()
		response.Data = res
		utils.ResponseSuccess(c, response)
	}
}

/**
 * @author: lwq <EMAIL>
 * @description: 初步审核集成单（产品架构负责人）
 * @param {*gin.Context} c
 * @return {*}
 */
func (s *IntegrationController) HandleReviewIntegration(c *gin.Context) {
	var req CheckReq
	if err := c.ShouldBind(&req); err != nil {
		response := utils.NewResponseFailed()
		response.Msg = err.Error()
		utils.ResponseFaied(c, response)
		return
	}
	checkReq := integration.CheckReq(req)
	res, err := s.igTask.ReviewIntegration(c, &checkReq)
	if err != nil {
		response := utils.NewResponseFailed()
		response.Msg = err.Error()
		utils.ResponseFaied(c, response)
	} else {
		response := utils.NewResponseSuccess()
		response.Data = res
		utils.ResponseSuccess(c, response)
	}
}

/**
 * @author: lwq <EMAIL>
 * @description: 查询当前集成单需要补录的包
 * @return {*}
 */
func (s *IntegrationController) HandleReplenishPackages(c *gin.Context) {
	igid, _ := strconv.Atoi(c.Param("igid"))
	srcName := c.Query("src_name")
	packages := s.igTask.ReplenishPackages(c, igid, srcName)
	response := utils.NewResponseSuccess()
	response.Data = packages
	utils.ResponseSuccess(c, response)
}

/**
 * @author: lwq <EMAIL>
 * @description: 补录（bug/cve）数据
 * @description: 门禁没有，koji有的，即未通过门禁入库的数据
 * @param {*gin.Context} c
 * @return {*}
 */
func (s *IntegrationController) HandleReplenishData(c *gin.Context) {
	var req ReplenishReq
	if err := c.ShouldBind(&req); err != nil {
		response := utils.NewResponseFailed()
		response.Msg = err.Error()
		utils.ResponseFaied(c, response)
		return
	}
	repReq := option.ReplenishReq(req)
	res, err := s.igTask.ReplenishData(c, &repReq)
	if err != nil {
		response := utils.NewResponseFailed()
		response.Msg = err.Error()
		utils.ResponseFaied(c, response)
	} else {
		response := utils.NewResponseSuccess()
		response.Data = res
		utils.ResponseSuccess(c, response)
	}
}

/**
 * @author: lwq <EMAIL>
 * @description: 二审审核集成单（技术经理）
 * @param {*gin.Context} c
 * @return {*}
 */
func (s *IntegrationController) HandleCheckIntegration(c *gin.Context) {
	var req CheckReq
	if err := c.ShouldBind(&req); err != nil {
		response := utils.NewResponseFailed()
		response.Msg = err.Error()
		utils.ResponseFaied(c, response)
		return
	}
	tCheckReq := integration.CheckReq(req)

	res, err := s.igTask.CheckIntegration(c, &tCheckReq)
	if err != nil {
		response := utils.NewResponseFailed()
		response.Msg = err.Error()
		utils.ResponseFaied(c, response)
		return
	}

	response := utils.NewResponseSuccess()
	response.Data = res
	utils.ResponseSuccess(c, response)
}

/**
 * @author: lwq <EMAIL>
 * @description: 添加软件包测试结果
 * @param {*gin.Context} c
 * @return {*}
 */
func (s *IntegrationController) HandleAddTestResult(c *gin.Context) {
	var req CheckReq
	if err := c.ShouldBind(&req); err != nil {
		response := utils.NewResponseFailed()
		response.Msg = err.Error()
		utils.ResponseFaied(c, response)
		return
	}
	tCheckReq := integration.CheckReq(req)
	res, err := s.igTask.AddTestResult(c, &tCheckReq)
	if err != nil {
		response := utils.NewResponseFailed()
		response.Msg = err.Error()
		utils.ResponseFaied(c, response)
		return
	}
	response := utils.NewResponseSuccess()
	response.Data = res
	utils.ResponseSuccess(c, response)
}

/**
 * @author: lwq <EMAIL>
 * @description: 复核集成单（项目经理）
 * @param {*gin.Context} c
 * @return {*}
 */
func (s *IntegrationController) HandleRecheckIntegration(c *gin.Context) {
	var req CheckReq
	if err := c.ShouldBind(&req); err != nil {
		response := utils.NewResponseFailed()
		response.Msg = err.Error()
		utils.ResponseFaied(c, response)
		return
	}
	tCheckReq := integration.CheckReq(req)
	res, err := s.igTask.RecheckIntegration(c, &tCheckReq)
	if err != nil {
		response := utils.NewResponseFailed()
		response.Msg = err.Error()
		utils.ResponseFaied(c, response)
	} else {
		response := utils.NewResponseSuccess()
		response.Data = res
		utils.ResponseSuccess(c, response)
	}
}

/**
 * @author: lwq <EMAIL>
 * @description: 标记软件包发布状态
 * @param {*gin.Context} c
 * @return {*}
 */
func (s *IntegrationController) HandleFlagIntegration(c *gin.Context) {
	var req FlagReq
	if err := c.ShouldBind(&req); err != nil {
		response := utils.NewResponseFailed()
		response.Msg = err.Error()
		utils.ResponseFaied(c, response)
		return
	}
	tCheckReq := option.CheckReq(req)
	res, err := s.igTask.FlagPublishStatus(c, &tCheckReq)
	if err != nil {
		response := utils.NewResponseFailed()
		response.Msg = err.Error()
		utils.ResponseFaied(c, response)
	} else {
		response := utils.NewResponseSuccess()
		response.Data = res
		utils.ResponseSuccess(c, response)
	}
}

/**
 * @author: lwq <EMAIL>
 * @description:
 * @param {*gin.Context} c
 * @return {*}
 */
func (s *IntegrationController) HandleConfirmIntegration(c *gin.Context) {
	igid, _ := strconv.Atoi(c.Param("igid"))
	res, err := s.igTask.ConfirmIntegration(c, igid)
	if err != nil {
		response := utils.NewResponseFailed()
		response.Msg = err.Error()
		utils.ResponseFaied(c, response)
	} else {
		response := utils.NewResponseSuccess()
		response.Data = res
		utils.ResponseSuccess(c, response)
	}
}

/**
 * @author: lwq <EMAIL>
 * @description: HandleDownloadIntegration 下载集成单Excel
 * @param {*gin.Context} c
 * @return {*}
 */
func (s *IntegrationController) HandleDownloadIntegration(c *gin.Context) {
	igid, _ := strconv.Atoi(c.Param("igid"))

	filePath, err := s.igTask.ExportIntegration(c, igid)
	if err != nil {
		response := utils.NewResponseFailed()
		response.Msg = err.Error()
		utils.ResponseFaied(c, response)
	} else {
		excelFile, err := os.ReadFile(filePath)
		if err != nil {
			response := utils.NewResponseFailed()
			utils.ResponseFaied(c, response)
			return
		}

		// excelFile 文件路径中获取文件名
		fileName := filepath.Base(filePath)
		c.Writer.WriteHeader(http.StatusOK)
		c.Header("Content-Disposition",
			fmt.Sprintf("attachment; filename=\"%s\"", fileName),
		)
		c.Header("Content-Type", "text/plain; charset=utf-8")
		c.Header("Accept-Length", fmt.Sprintf("%d", len(excelFile)))
		c.Writer.Write(excelFile)
	}
}

/**
 * @author: lwq <EMAIL>
 * @description: 集成单操作日志
 * @param {*gin.Context} c
 * @return {*}
 */
func (s *IntegrationController) HandleIntegrationLogs(c *gin.Context) error {
	return nil
}

/**
 * @author: lwq <EMAIL>
 * @description: 可添加包列表接口
 * @param {*gin.Context} c
 * @return {*}
 */
func (s *IntegrationController) HandlePackageList(c *gin.Context) {
	srcName := c.Query("src_name")
	xflowId, _ := strconv.Atoi(c.Query("xflow_id"))
	pager := utils.GetPageInfo(c)
	req := integration.PacksListReq{
		SrcName: srcName,
		XflowId: xflowId,
	}
	res, err := s.igTask.GetPackageList(c, pager, req)
	if err != nil {
		response := utils.NewResponseFailed()
		response.Msg = err.Error()
		utils.ResponseFaied(c, response)
	} else {
		response := utils.NewResponseSuccess()
		response.Data = res
		utils.ResponseSuccess(c, response)
	}
}

/**
 * @author: lwq <EMAIL>
 * @description: 需要添加包接口
 * @param {*gin.Context} c
 * @return {*}
 */
func (s *IntegrationController) HandleAddPackage(c *gin.Context) {
	var req AddPackageReq
	err := c.ShouldBindJSON(&req)
	if err != nil {
		response := utils.NewResponseFailed()
		response.Msg = err.Error()
		utils.ResponseFaied(c, response)
		return
	}
	res, err := s.igTask.AddPackage(c, req.IgId, req.XflowId)
	if err != nil {
		response := utils.NewResponseFailed()
		response.Msg = err.Error()
		utils.ResponseFaied(c, response)
	} else {
		response := utils.NewResponseSuccess()
		response.Data = res
		utils.ResponseSuccess(c, response)
	}
}

/**
 * @author: lwq <EMAIL>
 * @description: 删除包接口
 * @param {*gin.Context} c
 * @return {*}
 */
func (s *IntegrationController) HandleRemovePackage(c *gin.Context) {
	var req option.RemoveReq
	err := c.ShouldBindJSON(&req)
	if err != nil {
		response := utils.NewResponseFailed()
		response.Msg = err.Error()
		utils.ResponseFaied(c, response)
		return
	}
	err = s.igTask.RemovePackage(c, &req)
	if err != nil {
		response := utils.NewResponseFailed()
		response.Msg = err.Error()
		utils.ResponseFaied(c, response)
	} else {
		response := utils.NewResponseSuccess()
		utils.ResponseSuccess(c, response)
	}
}

/**
 * @author: lwq <EMAIL>
 * @description: 查看bug或者cve的审核记录接口
 * @param {*gin.Context} c
 * @return {*}
 */
func (s *IntegrationController) HandleCheckLogs(c *gin.Context) {
	id, _ := strconv.Atoi(c.Query("id"))
	ctype, _ := strconv.Atoi(c.Query("type"))
	logs, err := s.igTask.CheckList(c, id, ctype)
	if err != nil {
		response := utils.NewResponseFailed()
		response.Msg = err.Error()
		utils.ResponseFaied(c, response)
	} else {
		response := utils.NewResponseSuccess()
		response.Data = logs
		utils.ResponseSuccess(c, response)
	}
}
