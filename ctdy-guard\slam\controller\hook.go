package controller

import (
	"errors"
	"fmt"
	"math/rand"
	"os"
	"path"
	"slam/global"
	"slam/model"
	"slam/slog"
	"slam/task"
	"slam/utils"
	"strconv"
	"strings"
	"time"
)

// HookProcessRun 传入参数 启动hook检测
func HookProcessRun(xflowId int, xflowType string) error {
	taskId := CreateRandStr(10)
	xflowInfoList, err := task.GetXflowInfoByXflowId(xflowId)
	if err != nil {
		slog.Error.Println("get xflow info err, reason" + err.Error())
		return err
	}
	for _, xflowInfo := range xflowInfoList {
		hookIdList := model.GetHookByXflow(xflowInfo.XflowType)
		for _, hookId := range hookIdList {
			hookLevel, hookMsg := selectHookLevelMsg(hookId)
			hookTask := model.HookTaskList{
				TaskId:     taskId,
				Status:     global.HOOK_TASK_STATE_OPEN,
				XflowType:  xflowInfo.XflowType,
				XflowId:    xflowInfo.XflowId,
				HookId:     hookId,
				HookLevel:  hookLevel,
				HookMsg:    hookMsg,
				GerritInfo: xflowInfo.GerritAddr,
				KojiAddr:   xflowInfo.KojiAddr,
				Tag:        xflowInfo.Tag,
				TagAction:  xflowInfo.TagAction,
				TagKoji:    xflowInfo.TagKoji,
				SrpmName:   xflowInfo.SrpmName,
				ProductId:  xflowInfo.Product,
				HashMethod: xflowInfo.HashMethod,
				HashValue:  xflowInfo.HashValue,
				IsCommerce: xflowInfo.IsCommerce,
				CreatedAt:  time.Time{},
				UpdatedAt:  time.Time{},
			}
			id := model.InsertHookTask(hookTask)
			if id == 0 {
				slog.Error.Println("数据插入失败")
				return errors.New("数据插入失败")
			} else if id == -1 {
				slog.Error.Println("数据重复写入" + xflowInfo.SrpmName)
				continue
			}
		}
	}
	return nil
}

func CreateRandStr(strLen int) string {
	rand.Seed(time.Now().UnixNano())
	chars := "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ1234567890"
	// 生成十个随机字符拼接成一个十位的字符串
	randomString := ""
	for i := 0; i < strLen; i++ {
		randomIndex := rand.Intn(len(chars))
		randomString += string(chars[randomIndex])
	}
	return randomString
}

// ClearCacheFiles hook检测完成后，清理以来数据
func ClearCacheFiles(xflowId int) {
	packageTmpDir := path.Join(global.PACKAGE_TMP_PATH, strconv.Itoa(xflowId))
	err := os.RemoveAll(packageTmpDir)
	if err != nil {
		slog.Error.Println("Error deleting folder:", err)
		return
	}
	slog.Info.Println("Folder deleted successfully.")
}

// Retry hookTaskId hook任务表id hookId hook任务类型
func Retry(hookTaskId int, hookId string) error {
	hookTask := model.GetHookTaskById(hookTaskId)
	res, msg := task.CheckHookCanRetry(hookTask.XflowId, hookTask.XflowType, "retry")
	if !res {
		return errors.New(msg)
	}
	var filePath string
	var srpmPackage utils.SrpmPackageList
	srpmPackage, err := checkHookIsFinish(*hookTask, filePath, srpmPackage)
	if err != nil {
		return err
	}
	hookResult := model.HookTaskList{
		Id:          hookTask.Id,
		Status:      global.HOOK_TASK_STATE_READY,
		HookResult:  "",
		HookDetails: "",
		Remark:      "",
	}
	uhStatus := model.UpdateHookStatus(hookResult)
	if !uhStatus {
		return errors.New("重置hook流程状态失败")
	}
	return nil
}

func RetryAll(xflowId int) error {
	hookTaskList := model.GetHookTaskByXflowId(xflowId)
	if len(hookTaskList) == 0 {
		return errors.New("hook检测任务不存在，请确认流程是否正确")
	}
	xflowType := hookTaskList[0].XflowType
	res, msg := task.CheckHookCanRetry(xflowId, xflowType, "retry")
	if !res {
		return errors.New(msg)
	}
	for _, hookTask := range hookTaskList {
		hookResult := model.HookTaskList{
			Id:          hookTask.Id,
			Status:      global.HOOK_TASK_STATE_READY,
			HookResult:  "",
			HookDetails: "",
			Remark:      "",
		}
		model.UpdateHookStatus(hookResult)
	}
	return nil
}

// ScanHookTask 获取hook任务执行结果
func ScanHookTask(xflowId int) string {
	result := model.GetHookTaskStatusListByXflowId(xflowId)
	if len(result) == 0 {
		return global.HOOK_TESTING
	}
	resultStr := strings.Join(result, ",")
	if strings.Contains(resultStr, global.HOOK_TASK_STATE_OPEN) {
		return global.HOOK_TESTING
	}
	if strings.Contains(resultStr, global.HOOK_TASK_STATE_READY) {
		return global.HOOK_TESTING
	}
	if strings.Contains(resultStr, global.HOOK_TASK_STATE_UNZIP) {
		return global.HOOK_TESTING
	}
	if strings.Contains(resultStr, global.HOOK_TASK_STATE_RUNNING) {
		return global.HOOK_TESTING
	}
	if strings.Contains(resultStr, global.HOOK_TASK_STATE_CANCELED) {
		return global.HOOK_FAILED
	}
	if strings.Contains(resultStr, global.HOOK_TASK_STATE_FAILED) {
		return global.HOOK_FAILED
	}

	hookResult := model.GetHookTaskHookResultListByXflowId(xflowId)
	hookResultStr := strings.Join(hookResult, ",")
	if strings.Contains(hookResultStr, "禁止合入") {
		return global.HOOK_FAILED
	}
	if strings.Contains(hookResultStr, "超时") {
		return global.HOOK_FAILED
	}
	return global.HOOK_SUCCECD
}

// 查看hook流程是否完成
func checkHookIsFinish(hookTask model.HookTaskList, filePath string, srpmPackage utils.SrpmPackageList) (utils.SrpmPackageList, error) {
	if hookTask.XflowType == "tagbuild" || hookTask.XflowType == "untag" {
		if hookTask.Status == global.HOOK_TASK_STATE_CLOSED && hookTask.HookResult == global.HOOK_RESULT_PASS {
			return srpmPackage, errors.New("已完成的hook检测无法再次重新进行检测")
		}
	} else {
		filePath = path.Join(global.PACKAGE_TMP_PATH, strconv.Itoa(hookTask.XflowId), hookTask.SrpmName)
		_, err := os.Stat(filePath)
		if os.IsNotExist(err) && hookTask.Status == global.HOOK_TASK_STATE_CLOSED && hookTask.HookResult == global.HOOK_RESULT_PASS {
			fmt.Printf("Path %s does not exist\n", filePath)
			return srpmPackage, errors.New("已完成的hook检测无法再次重新进行检测")
		}
		srpmPackage, err = utils.GetPackInfo(filePath)
	}
	return srpmPackage, nil
}

func selectHookLevelMsg(hookId string) (string, string) {
	var hookLevel string
	var hookMsg string
	switch hookId {
	case "HookLicenseCheck":
		hookLevel = global.HOOK_LEVEL_FORBIDDEN
		hookMsg = "软件包许可证信息检测"
	case "HookLicenseFileCheck":
		hookLevel = global.HOOK_LEVEL_FORBIDDEN
		hookMsg = "引入开源软件必须包含开源许可信息（LICENSE或COPYING文件）"
	case "HookPackageBlackCheck":
		hookLevel = global.HOOK_LEVEL_FORBIDDEN
		hookMsg = "引入软件包未在黑名单中"
	case "HookPackageUrlCheck":
		hookLevel = global.HOOK_LEVEL_WARNING
		hookMsg = "必须提供引入软件包的官方源码下载地址"
	case "HookSpecFileCheck":
		hookLevel = global.HOOK_LEVEL_TIPS
		hookMsg = "spec文件符合规范"
	case "HookSourcePackageBinaryCheck":
		hookLevel = global.HOOK_LEVEL_WARNING
		hookMsg = "引入软件包内不能有二进制文件"
	case "HookCompatibilityCheck":
		hookLevel = global.HOOK_LEVEL_WARNING
		hookMsg = "软件包兼容性检测"
	case "HookCheckOpenSrpmDownloadUrl":
		hookLevel = global.HOOK_LEVEL_FORBIDDEN
		hookMsg = "引入开源软件包必须为原生社区下载地址，不得由软件托管平台（maven、pip等）处下载"
	case "HookCheckIntegrity":
		hookLevel = global.HOOK_LEVEL_FORBIDDEN
		hookMsg = "引入软件包必须通过完整性校验（MD5、SHA1、SHA256)"
	case "HookSonarqubeCheck":
		hookLevel = global.HOOK_LEVEL_WARNING
		hookMsg = "Sonarqube检测"
	case "HookBCTCheck":
		hookLevel = global.HOOK_LEVEL_WARNING
		hookMsg = "BCT检测"
	case "HookFortifyCheck":
		hookLevel = global.HOOK_LEVEL_WARNING
		hookMsg = "fotify检测"
	case "HookQAXCodeScanCheck":
		hookLevel = global.HOOK_LEVEL_WARNING
		hookMsg = "病毒扫描-奇安信代码扫描"
	case "HookSpecSudoCheck":
		hookLevel = global.HOOK_LEVEL_WARNING
		hookMsg = "SPEC文件sudo权限检测"
	case "HookSpecProfilesCheck":
		hookLevel = global.HOOK_LEVEL_WARNING
		hookMsg = "SPEC文件profile文件修改检测"
	case "HookSensitiveCheck":
		hookLevel = global.HOOK_LEVEL_FORBIDDEN
		hookMsg = "敏感词检测"
	case "HookLifeCycleCheck":
		hookLevel = global.HOOK_LEVEL_FORBIDDEN
		hookMsg = "软件包生命周期检测"
	case "HookRpmTestCheck":
		hookLevel = global.HOOK_LEVEL_FORBIDDEN
		hookMsg = "软件包安装检测"
	case "HookRpmlintCheck":
		hookLevel = global.HOOK_LEVEL_FORBIDDEN
		hookMsg = "软件包健全性检测"
	}
	return hookLevel, hookMsg
}
