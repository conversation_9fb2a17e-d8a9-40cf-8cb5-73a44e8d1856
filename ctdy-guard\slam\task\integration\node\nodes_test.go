package node

import (
	"slam/global"
	"testing"
)

func TestBaseNodeGetID(t *testing.T) {
	node := &BaseNode{
		ID:   1,
		Name: "TestNode",
	}

	expectedID := 1
	actualID := node.GetID()

	if actualID != expectedID {
		t.<PERSON><PERSON><PERSON>("Expected ID %d, 1but got %d", expectedID, actualID)
	}
}

func TestBaseNodeGetName(t *testing.T) {
	node := &BaseNode{
		ID:   1,
		Name: "Test1Node",
	}

	expectedName := "Test1Node"
	actualName := node.GetName()

	if actualName != expectedName {
		t.<PERSON><PERSON><PERSON>("Expected Name %s, 2but got %s", expectedName, actualName)
	}
}

func TestBaseNodeCheckAccess(t *testing.T) {
	node := &BaseNode{
		ID:             1,
		Name:           "Test Node",
		AllowedOptions: []int{1, 2, 3},
	}

	// Test allowed option
	if !node.CheckAccess(2) {
		t.Error("Expected option 2 to be allowed, but it was denied")
	}

	// Test denied option
	if node.CheckAccess(4) {
		t.<PERSON><PERSON>r("Expected option 4 to be denied, but it was allowed")
	}

	// Test with empty allowed options
	emptyNode := &BaseNode{
		ID:             1,
		Name:           "Empty Node",
		AllowedOptions: []int{},
	}

	if emptyNode.CheckAccess(1) {
		t.Error("Expected all options to be denied for empty allowed options list")
	}
}

func TestGetNodes(t *testing.T) {
	nodes := GetNodes()

	// Check that all expected nodes are present
	expectedNodeIDs := []int{
		global.INTEGRATION_STATUS_COMPARE,
		global.INTEGRATION_STATUS_CREATING,
		global.INTEGRATION_STATUS_CHECKING,
		global.INTEGRATION_STATUS_TESTING,
		global.INTEGRATION_STATUS_RECHECKING,
		global.INTEGRATION_STATUS_RELEASING,
		global.INTEGRATION_STATUS_PUBLISHED,
	}

	if len(nodes) != len(expectedNodeIDs) {
		t.Errorf("Expected %d nodes, 3but got %d", len(expectedNodeIDs), len(nodes))
	}

	for _, id := range expectedNodeIDs {
		if node, exists := nodes[id]; !exists {
			t.Errorf("Expected node with ID %d to exist", id)
		} else if node.GetID() != id {
			t.Errorf("Node ID mismatch. Expected %d, got %d", id, node.GetID())
		}
	}
}

func TestNodeComparing(t *testing.T) {
	node := NewNodeComparing()

	if node.GetID() != global.INTEGRATION_STATUS_COMPARE {
		t.Errorf("Expected ID %d, 4but got %d", global.INTEGRATION_STATUS_COMPARE, node.GetID())
	}
}

func TestNodeCreating(t *testing.T) {
	node := NewNodeCreating()

	if node.GetID() != global.INTEGRATION_STATUS_CREATING {
		t.Errorf("Expected ID %d, 5but got %d", global.INTEGRATION_STATUS_CREATING, node.GetID())
	}
}

func TestNodeChecking(t *testing.T) {
	node := NewNodeChecking()

	if node.GetID() != global.INTEGRATION_STATUS_CHECKING {
		t.Errorf("Expected ID %d, 6but got %d", global.INTEGRATION_STATUS_CHECKING, node.GetID())
	}
}

func TestNodeTesting(t *testing.T) {
	node := NewNodeTesting()

	if node.GetID() != global.INTEGRATION_STATUS_TESTING {
		t.Errorf("Expected ID %d, 7but got %d", global.INTEGRATION_STATUS_TESTING, node.GetID())
	}
}

func TestNodeRechecking(t *testing.T) {
	node := NewNodeRechecking()

	if node.GetID() != global.INTEGRATION_STATUS_RECHECKING {
		t.Errorf("Expected ID %d, 8but got %d", global.INTEGRATION_STATUS_RECHECKING, node.GetID())
	}

	if node.GetName() != "复核中" {
		t.Errorf("Expected name '复核中', but got '%s'", node.GetName())
	}
}

func TestNodeReleasing(t *testing.T) {
	node := NewNodeReleasing()

	if node.GetID() != global.INTEGRATION_STATUS_RELEASING {
		t.Errorf("Expected ID %d, 9but got %d", global.INTEGRATION_STATUS_RELEASING, node.GetID())
	}

	if node.GetName() != "发布中" {
		t.Errorf("Expected name '发布中', but got '%s'", node.GetName())
	}
}

func TestNodePublished(t *testing.T) {
	node := NewNodePublished()

	if node.GetID() != global.INTEGRATION_STATUS_PUBLISHED {
		t.Errorf("Expected ID %d, but got %d", global.INTEGRATION_STATUS_PUBLISHED, node.GetID())
	}

	if node.GetName() != "已发布" {
		t.Errorf("Expected name '已发布', but got '%s'", node.GetName())
	}
}
