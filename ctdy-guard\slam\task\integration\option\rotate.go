/*
 * @Author: lwq <EMAIL>
 * @Date: 2025-07-08
 * @LastEditors: lwq <EMAIL>
 * @LastEditTime: 2025-07-31
 * @FilePath: /ctdy-guard/slam/task/integration/option/rotate.go
 * @Description: 集成操作
 * @Copyright (c) 2025 by 2022.kylinos.cn, All Rights Reserved.
 */
package option

import (
	"errors"
	"slam/global"
	"slam/model"
	"slam/slog"
	"time"

	"github.com/gin-gonic/gin"
)

// RotateOption 流转集成单操作
type RotateOption struct {
	BaseOption
	igModel      model.IntegrationRepo
	igCountModel model.IntegrationCountRepo
	bugModel     model.IntegrationRowsRepo
	cveModel     model.IntegrationRowsRepo
}

func NewRotateOption() *RotateOption {
	return &RotateOption{
		BaseOption: BaseOption{
			ID:   "rotate",
			Name: "流转集成单",
		},
		igModel:      model.NewIntegrationModel(),
		igCountModel: model.NewIntegrationCountModel(),
		bugModel:     model.NewIntegrationBugModel(),
		cveModel:     model.NewIntegrationCveModel(),
	}
}

func (o *RotateOption) Execute(c *gin.Context, opUser *OpUser, params any) (any, error) {
	igInfo := opUser.OpIgInfo

	boolRes, igCount := o.CanRotate(c, igInfo.Id, igInfo.Status, params)
	if !boolRes {
		return nil, errors.New("当前集成单状态不允许流转,请检查集成单是否完成审核")
	}
	// 执行流转操作,把当前集成单状态流转到下一个状态，目前可以直接状态值加1
	igInfo.Status += 1

	if err := o.igModel.UpdateIntegration(igInfo); err != nil {
		slog.ErrorWithContext(c, "更新集成单状态失败", err)
		return nil, err
	}
	// 重置cve的审核结果字段
	if err := o.cveModel.BatchUpdateWithMapByIgid(igInfo.Id, map[string]any{"check_res": 0}); err != nil {
		slog.ErrorWithContext(c, "更新cve审核状态失败", err)
		return nil, err
	}

	// 重置bug的审核结果字段
	if err := o.bugModel.BatchUpdateWithMapByIgid(igInfo.Id, map[string]any{"check_res": 0}); err != nil {
		slog.ErrorWithContext(c, "更新bug审核状态失败", err)
		return nil, err
	}

	// 在复核阶段之后，是没有当前状态的统计数据的
	// 如果igCount
	if igCount != nil {
		//创建集成单统计数据
		count := &model.IntegrationCount{
			IntegrationId:  igInfo.Id,
			IgStatus:       igInfo.Status,
			TotalCount:     igCount.TotalCount,
			TotalBugCount:  igCount.TotalBugCount,
			TotalCveCount:  igCount.TotalCveCount,
			ReviewBugCount: 0,
			ReviewCveCount: 0,
			UnpublishCount: igCount.UnpublishCount,
			CreatedAt:      time.Now().Truncate(time.Second),
		}
		count.UpdatedAt = count.CreatedAt
		if err := o.igCountModel.CreateIntegrationCount(count); err != nil {
			slog.ErrorWithContext(c, "创建集成单统计数据失败", err)
			return nil, err
		}
	}
	// 需要添加消息通知队列

	return nil, nil
}

// 判断当前集成单是否可以流转
func (o *RotateOption) CanRotate(c *gin.Context, igId, status int, params any) (bool, *model.IntegrationCount) {
	// 如果是已经发布，就不能流转了
	if status == global.INTEGRATION_STATUS_PUBLISHED {
		return false, nil
	}

	// 如果是发布中状态，可以流转
	if status == global.INTEGRATION_STATUS_RELEASING {
		return true, nil
	}

	// 获取集成单统计数据
	igCount, err := o.igCountModel.GetIntegrationCount(igId, status)
	if err != nil {
		slog.ErrorWithContext(c, "查询统计数据报错")
		return false, nil
	}
	// 如果传了跳过，视为可以流转，如一键确认操作场景
	if skip, ok := params.(bool); ok {
		if skip {
			return true, igCount
		}
	}
	// 如果是校对中状态，可以流转
	if status == global.INTEGRATION_STATUS_COMPARE {
		return true, igCount
	}

	// 判断当前是否都完成审核
	if igCount.ReviewBugCount < igCount.TotalBugCount || igCount.ReviewCveCount < igCount.TotalCveCount {
		slog.ErrorWithContext(c, "当前集成单数据未完成审核，无法流转")
		return false, nil
	}

	return true, igCount
}
