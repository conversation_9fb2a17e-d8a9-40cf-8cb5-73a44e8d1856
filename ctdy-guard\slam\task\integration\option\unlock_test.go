package option

import (
	"slam/global"
	"slam/model"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
)

func TestUnlockOptionExecute(t *testing.T) {
	NewUnlockOption()
	gin.SetMode(gin.TestMode)

	t.Run("successful unlock execution", func(t *testing.T) {
		// Mocks
		mockIgModel := new(MockIntegrationRepo)

		// Test data
		opUser := &OpUser{
			UserInfo: UserInfo{
				UserId:   1,
				UserName: "testuser",
				Email:    "<EMAIL>",
			},
			OpIgInfo: &model.Integration{
				Id:         10,
				Status:     1,
				LockStatus: global.INTEGRATION_LOCKED, // 初始状态为已锁定
			},
		}

		// Mock expectations
		mockIgModel.On("UpdateIntegration", mock.MatchedBy(func(ig *model.Integration) bool {
			return ig.Id == 10 && ig.LockStatus == global.INTEGRATION_UNLOCK
		})).Return(nil)

		// Create UnlockOption instance with mocks
		unlockOption := &UnlockOption{
			BaseOption: BaseOption{
				ID:   "unlock",
				Name: "解锁",
			},
			igModel: mockIgModel,
		}

		// Execute
		ctx, _ := gin.CreateTestContext(nil)
		result, err := unlockOption.Execute(ctx, opUser, nil)

		// Assertions
		assert.NoError(t, err)
		assert.Nil(t, result)
		assert.Equal(t, global.INTEGRATION_UNLOCK, opUser.OpIgInfo.LockStatus)
		mockIgModel.AssertExpectations(t)
	})
}
