/*
 * @Author: lwq <EMAIL>
 * @Date: 2025-04-30
 * @LastEditors: lwq <EMAIL>
 * @LastEditTime: 2025-07-31
 * @FilePath: /ctdy-guard/slam/model/integration_bug.go
 * @Description: 集成单模型
 * @Copyright (c) 2025 by 2022.kylinos.cn, All Rights Reserved.
 */
package model

import (
	"errors"
	"slam/global"
	"slam/utils"
	"time"

	"gorm.io/gorm"
)

var igidQueryString = "integration_id = ?"

type IntegrationBugWithRpms struct {
	IntegrationBug
	Rpms      []*Rpm `gorm:"rpms;serializer:json" json:"rpms" label:"二进制包列表"`
	IsUntag   int    `gorm:"column:is_untag" json:"is_untag" label:"是否被untag 1-是，0-否"`
	StockType string `gorm:"column:stock_type" json:"stock_type" label:"类型"`
}

type IntegrationBug struct {
	Id            int       `gorm:"column:id;primaryKey;autoIncrement:true;type:serial4;not null" json:"id,omitempty" label:"序号，主键"`
	IntegrationId int       `gorm:"column:integration_id;not null" json:"integration_id" label:"集成单ID"`
	XflowId       int       `gorm:"column:xflow_id;not null" json:"xflow_id" label:"xflow ID"`
	SubmiterId    int       `gorm:"column:submiter_id;not null;default:0" json:"submiter_id" label:"提交人id"`
	SubmiterName  string    `gorm:"column:submiter_name;type:varchar(50);not null;default:0" json:"submiter_name" label:"提交人姓名"`
	SrcName       string    `gorm:"column:src_name;type:varchar(255);not null;default:''" json:"src_name" label:"源包名称"`
	ProductId     int       `gorm:"column:product_id;not null" json:"product_id" label:"产品id"`
	ArchInfo      string    `gorm:"column:arch_info;type:varchar(255);not null;default:''" json:"arch_info" label:"架构信息"`
	TaskType      string    `gorm:"column:task_type;type:varchar(255);not null;default:''" json:"task_type" label:"任务类型"`
	TestInfo      string    `gorm:"column:test_info;type:varchar(255);not null;default:''" json:"test_info" label:"测试方法"`
	ZentaoNo      string    `gorm:"column:zentao_no;type:varchar(255);not null;default:''" json:"zentao_no" label:"禅道编号"`
	BugType       string    `gorm:"column:bug_type;not null;default:''" json:"bug_type" label:"Bug类型1 2"`
	Level         string    `gorm:"column:level;type:varchar(25);not null;default:''" json:"level" label:"紧急级别"`
	Describtion   string    `gorm:"column:description;type:text;not null;default:''" json:"description" label:"描述"`
	ReplayMethod  string    `gorm:"column:replay_method;type:text;not null;default:''" json:"replay_method" label:"复现方法"`
	RepairMethod  string    `gorm:"column:repair_method;type:text;not null;default:''" json:"repair_method" label:"修复方法"`
	CheckRes      int       `gorm:"column:check_res;not null;default:0" json:"check_res" label:"当前状态下审核结果 0-未审核 1-通过，2-不通过"`
	IsPublish     int       `gorm:"column:is_publish;not null;default:1" json:"is_publish" label:"最终是否发布 1-发布 2-不发布"`
	Source        int       `gorm:"column:source;not null;default:1" json:"source" label:"数据来源 1-入库流程 2-历史遗留 3-手动添加 4-补录"`
	IsDel         int       `gorm:"column:is_del;not null;default:0" json:"is_del" label:"是否删除"`
	CreatedAt     time.Time `gorm:"column:create_time;not null" json:"create_time,omitempty" label:"创建时间"`
	UpdatedAt     time.Time `gorm:"column:update_time;not null" json:"update_time,omitempty" label:"更新时间"`
}

// TableName 表名
func (*IntegrationBug) TableName() string {
	return "slam_integration_bug"
}

func (i IntegrationBugWithRpms) GetID() int {
	return i.Id
}

// GetProductID 实现 IntegrationItem 接口
func (i IntegrationBugWithRpms) GetProductID() int {
	return i.ProductId
}

// GetXflowID 实现 IntegrationItem 接口
func (i IntegrationBugWithRpms) GetXflowID() int {
	return i.XflowId
}

func (i IntegrationBugWithRpms) GetRpms() []*Rpm {
	return i.Rpms
}

// integrationBugModel 集成单审核模型
type integrationBugModel struct {
	db *gorm.DB
}

// IntegrationBugRepo 集成单审核接口
type IntegrationRowsRepo interface {
	/**
	 * @author: lwq <EMAIL>
	 * @description: BatchInsertIntegrationBug 批量插入集成单审核
	 * @param {[]*IntegrationBug} integrationBugs
	 * @return {error}
	 */
	BatchInsertIntegration(rows any) error

	/**
	 * @author: lwq <EMAIL>
	 * @description: GetIntegrationList 分页查询bug或Cve列表数据
	 * @param {int} igId
	 * @param {utils.Pager} pager
	 * @return {[]*IntegrationBugWithRpms, int64, error}
	 */
	GetIntegrationList(req *PacksListReq, pager utils.Pager) (any, int64, error)

	/**
	 * @author: lwq <EMAIL>
	 * @description: GetXflowIdsByIgid 根据集成单id查询所有xflowid
	 * @param {int} igId
	 * @return {[]int, error}
	 */
	GetXflowIdsByIgid(igid int) ([]int, error)

	/**
	 * @author: lwq <EMAIL>
	 * @description: UpdateWithMap 更新集成单审核
	 * @param {int} id
	 * @param {map[string]any} umap
	 * @return {*}
	 */
	UpdateWithMap(id int, umap map[string]any) (int, error)

	/**
	 * @author: lwq <EMAIL>
	 * @description: 以集成单维度更新所有记录
	 * @param {int} igid
	 * @param {map[string]any} umap
	 * @return {*}
	 */
	BatchUpdateWithMapByIgid(igid int, umap map[string]any) error

	/**
	 * @author: lwq <EMAIL>
	 * @description: RemoveRows 删除集成单审核
	 * @description: 只能删除手动添加的
	 * @param {[]int} ids
	 * @return {*}
	 */
	RemoveRows(ids []int) (int64, error)

	/**
	 * @author: lwq <EMAIL>
	 * @description: GetProductListByIgid 根据bug/cve记录id查询产品列表
	 * @return {*}
	 */
	GetProductListByIgid(igid int) (map[int]int, error)

	/**
	 * @author: lwq <EMAIL>
	 * @description: UpdateFromHistoryToNewIg 把历史遗留的数据转移到新集成单中
	 * @return {*}
	 */
	UpdateFromHistoryToNewIg(igid int) (int, error)
}

/**
 * @author: lwq <EMAIL>
 * @description: NewIntegrationBugModel 初始化集成单审核模型
 * @return {*integrationBugModel}
 */
func NewIntegrationBugModel() IntegrationRowsRepo {
	return &integrationBugModel{
		db: GetDB(),
	}
}

// BatchInsertIntegrationBug 批量插入slam_integration_bug表
func (i *integrationBugModel) BatchInsertIntegration(rows any) error {
	integrationBugs, ok := rows.([]*IntegrationBug)
	if !ok {
		return errors.New("rows类型断言失败")
	}
	return i.db.Create(integrationBugs).Error
}

// GetIntegrationBugList 分页查询集成单bug列表
func (i *integrationBugModel) GetIntegrationList(req *PacksListReq, pager utils.Pager) (any, int64, error) {
	var integrationBugs []*IntegrationBugWithRpms
	var total int64
	query := i.db.Table("slam_integration_bug as ib").
		Select("ib.*,COALESCE(cl.stock_type, '') as stock_type, "+
			"json_agg(CASE WHEN ir.nvr IS NOT NULL THEN json_build_object('src_name', concat_ws('.', ir.nvr, ir.arch_info, 'rpm'), 'signd_sha256', ir.signd_sha256) ELSE NULL END) FILTER (WHERE ir.nvr IS NOT NULL) as rpms").
		Joins("LEFT JOIN slam_integration_rpm as ir "+
			"ON ib.xflow_id = ir.xflow_id AND ib.src_name = ir.src_name AND ib.arch_info = ir.arch_info").
		Joins("LEFT JOIN slam_stockcenterlist as cl "+
			"ON ib.xflow_id = cl.xflow_id").
		Where("ib.integration_id = ?", req.IgId).
		Where("ib.is_del = ?", global.NUMBER_FALSE)

	if req.SrcName != "" {
		query = query.Where("ib.src_name LIKE ?", "%"+req.SrcName+"%")
	}

	// 如果与我相关
	if req.Uid > 0 {
		query = query.Joins("LEFT JOIN slam_products as sp ON ib.product_id = sp.id").
			Where("sp.architecture_manager = ? or sp.technical_manager = ? or sp.project_manager = ?", req.Uid, req.Uid, req.Uid)
	}

	err := query.Count(&total).Offset(pager.Offset).
		Limit(pager.Size).Group("ib.id, ib.src_name, cl.stock_type").
		Order("ib.id desc").Find(&integrationBugs).Error
	return integrationBugs, total, err
}

// 根据igid查询所有xflowid
func (i *integrationBugModel) GetXflowIdsByIgid(igid int) ([]int, error) {
	var xflowIds []int
	err := i.db.Model(&IntegrationBug{}).
		Select("xflow_id").Distinct().
		Where(igidQueryString, igid).
		Pluck("xflow_id", &xflowIds).Error

	return xflowIds, err
}

// 更新bug发布状态
func (i *integrationBugModel) UpdateWithMap(id int, umap map[string]any) (int, error) {
	result := i.db.Model(&IntegrationBug{}).
		Where("id = ?", id).
		Where("is_del = ?", global.NUMBER_FALSE).
		UpdateColumns(umap)
	return int(result.RowsAffected), result.Error
}

func (i *integrationBugModel) BatchUpdateWithMapByIgid(igid int, umap map[string]any) error {
	return i.db.Model(&IntegrationBug{}).
		Where(igidQueryString, igid).
		UpdateColumns(umap).Error
}

// 逻辑删除记录
func (i *integrationBugModel) RemoveRows(ids []int) (int64, error) {
	result := i.db.Model(&IntegrationBug{}).
		Where("id in ?", ids).
		Where("source = ?", global.INTEGRATION_SOURCE_MANUAL).
		Where("is_del", global.NUMBER_FALSE).
		Update("is_del", global.NUMBER_TRUE)
	return result.RowsAffected, result.Error
}

// 根据igid获取bug中产品Id列表
func (i *integrationBugModel) GetProductListByIgid(igid int) (map[int]int, error) {
	var bugs []IntegrationBug
	err := i.db.Model(&IntegrationBug{}).
		Select("id, product_id").
		Where(igidQueryString, igid).
		Find(&bugs).Error
	if err != nil {
		return nil, err
	}
	result := make(map[int]int)
	for _, bug := range bugs {
		result[bug.Id] = bug.ProductId
	}
	return result, nil
}

func (i *integrationBugModel) UpdateFromHistoryToNewIg(igid int) (int, error) {
	query := i.db.Model(&IntegrationBug{}).
		Where("is_publish = ?", IntegrationCheckUnPass).
		Where("is_del = ?", global.NUMBER_FALSE).
		UpdateColumns(map[string]any{
			"integration_id": igid,
			"is_publish":     IntegrationCheckPass,
			"check_res":      global.NUMBER_FALSE,
			"source":         global.INTEGRATION_SOURCE_HISTORY,
		})
	return int(query.RowsAffected), query.Error
}
