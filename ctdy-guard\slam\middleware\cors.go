package middleware

import (
	"net/http"
	"slam/slog"

	"github.com/gin-gonic/gin"
)

func Cors(context *gin.Context) {
	traceID := context.GetHeader("X-Trace-Id")
	if traceID == "" {
		traceID = slog.GenerateTraceID()
	}
	context.Set("traceid", traceID)
	context.Header("X-Trace-Id", traceID)

	method := context.Request.Method
	context.Header("Access-Control-Allow-Origin", context.GetHeader("Origin"))
	context.Header("Access-Control-Allow-Methods", "POST, GET, PUT, DELETE, OPTIONS")
	context.Header("Access-Control-Allow-Headers", "Content-Type, cb, withcredentials, Content-Length, Access-Token, Authorization")
	context.Header("Access-Control-Expose-Headers", "Access-Control-Allow-Headers, Token")
	context.Header("Access-Control-Allow-Credentials", "true")
	context.Header("Access-Control-Max-Age", "86400")
	if method == http.MethodOptions {
		context.AbortWithStatus(http.StatusNoContent)
		return
	}
	context.Next()
}
