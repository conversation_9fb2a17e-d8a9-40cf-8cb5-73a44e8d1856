package option

import (
	"slam/global"
	"slam/model"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
)

func TestLockOptionExecute(t *testing.T) {
	NewLockOption()
	gin.SetMode(gin.TestMode)

	t.Run("successful lock execution", func(t *testing.T) {
		// Mocks
		mockIgModel := new(MockIntegrationRepo)

		// Test data
		opUser := &OpUser{
			UserInfo: UserInfo{
				UserId:   1,
				UserName: "testuser",
				Email:    "<EMAIL>",
			},
			OpIgInfo: &model.Integration{
				Id:         10,
				Status:     1,
				LockStatus: global.INTEGRATION_UNLOCK, // 初始状态为未锁定
			},
		}

		// Mock expectations
		mockIgModel.On("UpdateIntegration", mock.MatchedBy(func(ig *model.Integration) bool {
			return ig.Id == 10 && ig.LockStatus == global.INTEGRATION_LOCKED
		})).Return(nil)

		// Create LockOption instance with mocks
		lockOption := &LockOption{
			BaseOption: BaseOption{
				ID:   "lock",
				Name: "加锁",
			},
			igModel: mockIgModel,
		}

		// Execute
		ctx, _ := gin.CreateTestContext(nil)
		result, err := lockOption.Execute(ctx, opUser, nil)

		// Assertions
		assert.NoError(t, err)
		assert.Nil(t, result)
		assert.Equal(t, global.INTEGRATION_LOCKED, opUser.OpIgInfo.LockStatus)
		mockIgModel.AssertExpectations(t)
	})

	t.Run("failed lock execution due to model error", func(t *testing.T) {
		// Mocks
		mockIgModel := new(MockIntegrationRepo)

		// Test data
		opUser := &OpUser{
			UserInfo: UserInfo{
				UserId:   1,
				UserName: "testuser",
				Email:    "<EMAIL>",
			},
			OpIgInfo: &model.Integration{
				Id:         10,
				Status:     1,
				LockStatus: global.INTEGRATION_UNLOCK,
			},
		}

		// Mock expectations
		mockIgModel.On("UpdateIntegration", mock.MatchedBy(func(ig *model.Integration) bool {
			return ig.Id == 10 && ig.LockStatus == global.INTEGRATION_LOCKED
		})).Return(assert.AnError)

		// Create LockOption instance with mocks
		lockOption := &LockOption{
			BaseOption: BaseOption{
				ID:   "lock",
				Name: "加锁",
			},
			igModel: mockIgModel,
		}

		// Execute
		ctx, _ := gin.CreateTestContext(nil)
		result, err := lockOption.Execute(ctx, opUser, nil)

		// Assertions
		assert.Error(t, err)
		assert.Nil(t, result)
		assert.Equal(t, global.INTEGRATION_LOCKED, opUser.OpIgInfo.LockStatus) // 状态应该已经更新
		mockIgModel.AssertExpectations(t)
	})
}
