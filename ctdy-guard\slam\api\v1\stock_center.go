package v1

import (
	"encoding/json"
	"fmt"
	"net/http"
	"os"
	"slam/global"
	"slam/model"
	"slam/slog"
	"slam/task"
	"slam/utils"
	"strconv"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/go-playground/validator/v10"
)

type StockCenterController struct {
}

// 源码包入库
func (controller *StockCenterController) HandleStockCenterAdd(c *gin.Context) {
	StockAddByType(global.XFLOW_TYPE_STOCK_CENTER, c)
}

// 按类型创建入库流程
func StockAddByType(t string, c *gin.Context) {
	var br task.StockCenterInfo
	err := c.ShouldBind(&br)
	flag := 0
	if err != nil {
		errs, ok := err.(validator.ValidationErrors)
		if ok {
			slog.Error.Println("参数列表存在必传字段未填", errs)
			response := utils.NewResponseFailed()
			response.Msg = "存在必传参数未填写，请确认后重新填写"
			utils.ResponseFaied(c, response)
			return
		}
		slog.Error.Println(global.STR_PARA_ERROR, err)
		response := utils.NewResponseFailed()
		response.Msg = global.STR_PARA_ERROR
		utils.ResponseFaied(c, response)
		return
	}
	if len(br.Packages) > 0 {
		var packs []task.StockCenterPacksInfo
		err = json.Unmarshal([]byte(br.Packages), &packs)
		if err != nil {
			slog.Error.Println("源码包数据解析失败", err)
			flag = 1
		}
		br.Packages_info = packs
	}
	if len(br.Evaluators) > 0 {
		var evaluator []int
		err = json.Unmarshal([]byte(br.Evaluators), &evaluator)
		if err != nil {
			slog.Error.Println(global.STR_PARA_EVALUATORS, err)
			flag = 1
		}
		br.Evaluator = evaluator
	}
	if len(br.Appendix_names) > 0 {
		var appendix []string
		err = json.Unmarshal([]byte(br.Appendix_names), &appendix)
		if err != nil {
			slog.Error.Println(global.STR_PARA_APPENDIX, err)
			flag = 1
		}
		br.Appendix_name = appendix
	}
	if flag == 1 {
		response := utils.NewResponseFailed()
		response.Msg = global.STR_PARA_ERROR
		utils.ResponseFaied(c, response)
		return
	}
	res, resultText := task.CreateStockCenter(t, &br, c)
	if !res {
		slog.Error.Println("入库申请发布失败")
		response := utils.NewResponseFailed()
		response.Msg = resultText
		utils.ResponseFaied(c, response)
	} else {
		response := utils.NewResponseSuccess()
		response.Msg = "入库申请发布成功"
		utils.ResponseSuccess(c, response)
	}
}

// 中心仓入库详情
func (controller *StockCenterController) HandleStockCenterDetail(c *gin.Context) {
	id, err := strconv.Atoi(c.Query("id"))
	if err != nil {
		slog.Error.Println(global.PARAMENT_PASSING_FAILURE, err)
		response := utils.NewResponseFailed()
		response.Msg = global.PARAMENT_PASSING_FAILURE
		utils.ResponseFaied(c, response)
		return
	}
	res := task.GetStockCenterDetail(id)
	if res == nil {
		slog.Error.Println("入库申请详情加载失败")
		response := utils.NewResponseFailed()
		response.Msg = "入库申请详情加载失败"
		utils.ResponseFaied(c, response)
	} else {
		response := utils.NewResponseSuccess()
		response.Data = *res
		response.Msg = "入库申请详情加载成功"
		utils.ResponseSuccess(c, response)
	}
}

// 中心仓入库软件包单包整体检查接口 new
func (controller *StockCenterController) HandleVerifySCenterPack(c *gin.Context) {
	var br task.SCenterPackageVerify
	err := c.ShouldBind(&br)
	if err != nil {
		slog.Error.Println("HandleVerifySCenterPack 参数列表解析失败", err)
		response := utils.NewResponseFailed()
		response.Msg = global.STR_PARA_ERROR
		utils.ResponseFaied(c, response)
		return
	}
	br.ProdId, err = strconv.Atoi(br.ProdIdS)
	if err != nil {
		slog.Error.Println(global.STR_PARA_PRODANAL, err)
		response := utils.NewResponseFailed()
		response.Msg = global.STR_PARA_ERROR
		utils.ResponseFaied(c, response)
		return
	}
	//解析参数
	err = json.Unmarshal([]byte(br.PackNameS), &br.PackName)
	if err != nil {
		slog.Error.Println("PackName数据解析失败", err)
		response := utils.NewResponseFailed()
		response.Msg = global.STR_PARA_ERROR
		utils.ResponseFaied(c, response)
		return
	}
	if len(br.CompileOptionS) > 0 {
		err = json.Unmarshal([]byte(br.CompileOptionS), &br.CompileOption)
		if err != nil {
			slog.Error.Println("CompileOption数据解析失败", err)
			response := utils.NewResponseFailed()
			response.Msg = global.STR_PARA_ERROR
			utils.ResponseFaied(c, response)
			return
		}
	}
	res, resultText := task.VerifyStockCenterPackage(&br)
	if !res {
		response := utils.NewResponseFailed()
		response.Msg = resultText
		utils.ResponseFaied(c, response)
	} else {
		response := utils.NewResponseSuccess()
		response.Msg = "校验成功"
		utils.ResponseSuccess(c, response)
	}
}

// 中心仓软件前台列表
func (controller *StockCenterController) HandleGetCenterList(c *gin.Context) {
	page_size, _ := strconv.Atoi(c.DefaultQuery("page_size", "10"))
	page_index, _ := strconv.Atoi(c.DefaultQuery("page_index", "1"))
	page_size = task.CheckPageSize(page_size)
	bq := model.SCenterListQuery{
		Developer:   c.Query("developer"),
		ProdRelease: c.Query("prod_release"),
		Product:     c.Query("prod"),
		StartDate:   c.Query("start_date"),
		EndDate:     c.Query("end_date"),
		CurrentFlow: c.Query("current_flow"),
		Key:         c.Query("key"),
		SortColumn:  c.Query("sort_colunm"),
		SortOrder:   c.Query("sort_order"),
	}
	res := model.GetSCenterSummaryLists(global.STOCK_TYPE_CenterS, page_size, page_index, bq)
	if res == nil {
		slog.Error.Println("获取入库概要信息列表失败")
		response := utils.NewResponseFailed()
		response.Msg = "获取入库概要信息列表失败"
		utils.ResponseFaied(c, response)
	} else {
		response := utils.NewResponseSuccess()
		response.Data = *res
		utils.ResponseSuccess(c, response)
	}
}

// 评估与审核列表 中心仓入库
func (controller *StockCenterController) HandleGetCenterEvaluateList(c *gin.Context) {
	page_size, _ := strconv.Atoi(c.DefaultQuery("page_size", "10"))
	page_index, _ := strconv.Atoi(c.DefaultQuery("page_index", "1"))
	page_size = task.CheckPageSize(page_size)

	userRes, _, userItem := task.GetUserInfo(c)
	if !userRes {
		slog.Error.Println("获取用户信息失败")
		response := utils.NewResponseFailed()
		response.Msg = "获取用户信息失败"
		utils.ResponseFaied(c, response)
		return
	}

	bq := model.SCenterEvaluateListQuery{
		Platform:    c.Query("platform"),
		Developer:   c.Query("developer"),
		ProdRelease: c.Query("prod_release"),
		Product:     c.Query("prod"),
		StartDate:   c.Query("start_date"),
		EndDate:     c.Query("end_date"),
		CurrentFlow: c.Query("current_flow"),
		Key:         c.Query("key"),
		EvaluatorId: *userItem.Id,
		SortColumn:  c.Query("sort_colunm"),
		SortOrder:   c.Query("sort_order"),
	}
	res := model.GetSCenterEvaluateList(global.STOCK_TYPE_CenterS, page_size, page_index, bq)
	if res == nil {
		slog.Error.Println("获取入库概要信息列表失败")
		response := utils.NewResponseFailed()
		response.Msg = "获取入库概要信息列表失败"
		utils.ResponseFaied(c, response)
	} else {
		response := utils.NewResponseSuccess()
		response.Data = *res
		utils.ResponseSuccess(c, response)
	}
}

/*
文件批量入库
*/
// /////////////////////////////////////////////////////////////////////////////////////////////////////////

// 文件上传
func (controller *StockCenterController) HandleStockBatchUpload(c *gin.Context) {
	var br task.SCFilePackInfo
	err := c.ShouldBind(&br)
	if err != nil {
		slog.Error.Println(global.PARAMENT_PARSING_FAILURE, err)
		response := utils.NewResponseFailed()
		response.Msg = global.PARAMENT_PARSING_FAILURE
		utils.ResponseFaied(c, response)
		return
	}
	filenameArr := strings.Split(br.File.Filename, ".")
	if len(filenameArr) <= 0 || (filenameArr[len(filenameArr)-1] != "txt") {
		response := utils.NewResponseFailed()
		response.Msg = "您传入的文件格式错误，请传入txt文件"
		utils.ResponseFaied(c, response)
		return
	}
	res, obj, msg, _ := task.AnalysisSCenterPacks(&br)
	if res {
		response := utils.NewResponseSuccess()
		response.Data = obj
		utils.ResponseSuccess(c, response)
		return
	} else {
		response := utils.NewResponseFailed()
		response.Msg = msg
		utils.ResponseFaied(c, response)
		return
	}
}

// 文件模板下载
func (controller *StockCenterController) DownlowadTemp(c *gin.Context) {
	var build strings.Builder
	build.WriteString(global.COMMON_TEMPLATE_REPO)
	build.WriteString("文件入库——上传模板.txt")
	file, err := os.ReadFile(build.String())
	if err != nil {
		slog.Error.Println("模板下载失败", err)
		response := utils.NewResponseFailed()
		response.Msg = "模板下载失败"
		utils.ResponseFaied(c, response)
	} else {
		c.Writer.WriteHeader(http.StatusOK)
		c.Header("Content-Disposition", fmt.Sprintf("attachment; filename=\"%s\"", "文件入库——上传模板.txt"))
		c.Header("Content-Type", "text/plain; charset=utf-8")
		c.Header("Accept-Length", fmt.Sprintf("%d", len(file)))
		c.Writer.Write([]byte(file))
	}
}

// 文件入库发布
func (controller *StockCenterController) BatchAdd(c *gin.Context) {
	var br task.SCFileInfo
	err := c.ShouldBind(&br)
	flag := 0
	if err != nil {
		errs, ok := err.(validator.ValidationErrors)
		if ok {
			slog.Error.Println("参数列表存在必传字段未填", errs)
			response := utils.NewResponseFailed()
			response.Msg = "存在必传参数未填写，请确认后重新填写"
			utils.ResponseFaied(c, response)
			return
		}
		slog.Error.Println(global.STR_PARA_ERROR, err)
		response := utils.NewResponseFailed()
		response.Msg = global.STR_PARA_ERROR
		utils.ResponseFaied(c, response)
		return
	}
	filenameArr := strings.Split(br.File.Filename, ".")
	if len(filenameArr) <= 0 || (filenameArr[len(filenameArr)-1] != "txt") {
		response := utils.NewResponseFailed()
		response.Msg = "您传入的文件格式错误，请传入txt文件"
		utils.ResponseFaied(c, response)
		return
	}
	br.ProdId, err = strconv.Atoi(br.ProdIdS)
	if err != nil {
		slog.Error.Println(global.STR_PARA_PRODANAL, err)
		flag = 1
	}
	if len(br.CompileOptions) > 0 {
		var compileOption []string
		err = json.Unmarshal([]byte(br.CompileOptions), &compileOption)
		if err != nil {
			slog.Error.Println("编译参数列表解析失败", err)
			flag = 1
		}
		br.CompileOption = compileOption
	}
	if len(br.Evaluators) > 0 {
		var evaluator []int
		err = json.Unmarshal([]byte(br.Evaluators), &evaluator)
		if err != nil {
			slog.Error.Println("评估人参数列表解析失败", err)
			flag = 1
		}
		br.Evaluator = evaluator
	}
	if len(br.Appendix_names) > 0 {
		var appendix []string
		err = json.Unmarshal([]byte(br.Appendix_names), &appendix)
		if err != nil {
			slog.Error.Println(global.STR_PARA_APPENDIX, err)
			flag = 1
		}
		br.Appendix_name = appendix
	}
	if flag == 1 {
		response := utils.NewResponseFailed()
		response.Msg = global.STR_PARA_ERROR
		utils.ResponseFaied(c, response)
		return
	}
	if br.Split == "true" {
		// 拆分提交
		res, data, resultText := task.CreateSCFileSplit(&br, c)
		if !res {
			slog.Error.Println("文件入库——入库申请发布失败")
			response := utils.NewResponseFailed()
			response.Msg = resultText
			response.Data = data
			utils.ResponseFaied(c, response)
		} else {
			response := utils.NewResponseSuccess()
			response.Msg = "入库申请发布成功"
			response.Data = data
			utils.ResponseSuccess(c, response)
		}
	} else {
		// 不拆分提交
		res, resultText := task.CreateSCFile(&br, c)
		if !res {
			slog.Error.Println("文件入库——入库申请发布失败")
			response := utils.NewResponseFailed()
			response.Msg = resultText
			utils.ResponseFaied(c, response)
		} else {
			response := utils.NewResponseSuccess()
			response.Msg = "入库申请发布成功"
			utils.ResponseSuccess(c, response)
		}
	}
}

// 文件批量导入详情
func (controller *StockCenterController) HandleGetBatchDetail(c *gin.Context) {
	id, err := strconv.Atoi(c.Query("id"))
	if err != nil {
		slog.Error.Println(global.PARAMENT_PASSING_FAILURE, err)
		response := utils.NewResponseFailed()
		response.Msg = global.PARAMENT_PASSING_FAILURE
		utils.ResponseFaied(c, response)
		return
	}
	res := task.GetScFileDetail(id)
	if res == nil {
		slog.Error.Println("入库申请详情加载失败")
		response := utils.NewResponseFailed()
		response.Msg = "入库申请详情加载失败"
		utils.ResponseFaied(c, response)
	} else {
		response := utils.NewResponseSuccess()
		response.Data = *res
		response.Msg = "入库申请详情加载成功"
		utils.ResponseSuccess(c, response)
	}
}

// 入库申请附件下载
func (controller *StockCenterController) HandleGetSCAppendix(c *gin.Context) {
	id, err := strconv.Atoi(c.Query("id"))
	fileName := c.Query("file_name")
	if err != nil || len(fileName) == 0 {
		slog.Error.Println(global.PARAMENT_PASSING_FAILURE, err)
		response := utils.NewResponseFailed()
		response.Msg = global.PARAMENT_PASSING_FAILURE
		utils.ResponseFaied(c, response)
		return
	}
	// 组合文件下载路径
	var build strings.Builder
	build.WriteString(global.STOCK_CENTER_APPENDIX_REPO)
	build.WriteString(strconv.Itoa(id))
	build.WriteString("/")
	build.WriteString(fileName)
	file, err := os.ReadFile(build.String())
	if err != nil {
		slog.Error.Println("附件下载失败", err)
		response := utils.NewResponseFailed()
		response.Msg = "附件下载失败"
		utils.ResponseFaied(c, response)
	} else {
		c.Writer.WriteHeader(http.StatusOK)
		c.Header("Content-Disposition", fmt.Sprintf("attachment; filename=\"%s\"", fileName))
		c.Header("Content-Type", "text/plain; charset=utf-8")
		c.Header("Accept-Length", fmt.Sprintf("%d", len(file)))
		c.Writer.Write([]byte(file))
	}
}

// 回撤入库申请
func (controller *StockCenterController) HandleSCWithDraw(c *gin.Context) {
	id, err := strconv.Atoi(c.Query("id"))
	if err != nil {
		slog.Error.Println(global.PARAMENT_PASSING_FAILURE, err)
		response := utils.NewResponseFailed()
		response.Msg = global.PARAMENT_PASSING_FAILURE
		utils.ResponseFaied(c, response)
		return
	}

	res, resMsg := task.WithDrawSCenter(id)
	if !res {
		slog.Error.Println("入库申请回撤失败")
		response := utils.NewResponseFailed()
		response.Msg = resMsg
		utils.ResponseFaied(c, response)
	} else {
		response := utils.NewResponseSuccess()
		response.Msg = "入库申请回撤成功"
		utils.ResponseSuccess(c, response)
	}
}

// 校验是否能重新发布
func (controller *StockCenterController) HandleSCRetry(c *gin.Context) {
	xflow_id, _ := strconv.Atoi(c.DefaultQuery("id", "-1"))
	if xflow_id == -1 {
		response := utils.NewResponseFailed()
		response.Msg = "参数传递异常,流程id未传递"
		utils.ResponseFaied(c, response)
		slog.Error.Println("参数传递异常,流程id未传递")
		return
	}
	stockDetail := task.GetStockCenterDetail(xflow_id)
	if stockDetail == nil {
		response := utils.NewResponseFailed()
		response.Msg = "后台获取流程信息失败"
		utils.ResponseFaied(c, response)
		slog.Error.Println("后台获取流程信息失败")
		return
	}
	kojis := model.Koji.GetTaskKojisByXflowId(xflow_id)
	if kojis == nil {
		response := utils.NewResponseFailed()
		response.Msg = "后台获取koji信息失败"
		utils.ResponseFaied(c, response)
		slog.Error.Println("后台获取koji信息失败")
		return
	}
	reason := ""
	for _, koji := range kojis {
		ret, str := task.KojiInputCheck(&koji)
		if !ret {
			reason = reason + " " + str
		}
	}
	if reason != "" {
		response := utils.NewResponseFailed()
		response.Msg = reason
		utils.ResponseFaied(c, response)
		slog.Error.Println(reason)
		return
	}
	response := utils.NewResponseSuccess()
	response.Data = *stockDetail
	utils.ResponseSuccess(c, response)
}

// 重启接口（编译失败或入库失败后重新启动）
func (controller *StockCenterController) HandleGetSCRebuild(c *gin.Context) {
	xflow_id, _ := strconv.Atoi(c.DefaultQuery("id", "-1"))
	if xflow_id == -1 {
		response := utils.NewResponseFailed()
		response.Msg = global.STR_PARA_NOID
		utils.ResponseFaied(c, response)
		return
	}
	res := task.RebuildStockC(xflow_id)
	if res {
		response := utils.NewResponseSuccess()
		response.Msg = "重新入库成功"
		utils.ResponseSuccess(c, response)
	} else {
		response := utils.NewResponseFailed()
		response.Msg = "重新入库失败"
		utils.ResponseFaied(c, response)
	}
}

// /////////////////////////////////////////////////////////////////////////////////////////////////////////
//商业软件入库接口

// 商业软件入库
func (controller *StockCenterController) HandleStockBizAdd(c *gin.Context) {
	StockAddByType(global.XFLOW_TYPE_STOCK_BIZ, c)
}

// 商业软件前台列表
func (controller *StockCenterController) HandleGetBizList(c *gin.Context) {
	page_size, _ := strconv.Atoi(c.DefaultQuery("page_size", "10"))
	page_index, _ := strconv.Atoi(c.DefaultQuery("page_index", "1"))
	page_size = task.CheckPageSize(page_size)
	bq := model.SCenterListQuery{
		BizSoftForm: c.Query("bizsoft_form"),
		BizSoftType: c.Query("bizsoft_type"),
		Product:     c.Query("prod"),
		StartDate:   c.Query("start_date"),
		EndDate:     c.Query("end_date"),
		CurrentFlow: c.Query("current_flow"),
		Key:         c.Query("key"),
		SortColumn:  c.Query("sort_colunm"),
		SortOrder:   c.Query("sort_order"),
	}
	res := model.GetSCenterSummaryLists(global.STOCK_TYPE_BUSINESS, page_size, page_index, bq)
	if res == nil {
		slog.Error.Println("获取入库概要信息列表失败")
		response := utils.NewResponseFailed()
		response.Msg = "获取入库概要信息列表失败"
		utils.ResponseFaied(c, response)
	} else {
		response := utils.NewResponseSuccess()
		response.Data = *res
		utils.ResponseSuccess(c, response)
	}
}

// 评估与审核列表 商业软件入库
func (controller *StockCenterController) HandleGetBizEvaluateList(c *gin.Context) {
	page_size, _ := strconv.Atoi(c.DefaultQuery("page_size", "10"))
	page_index, _ := strconv.Atoi(c.DefaultQuery("page_index", "1"))
	page_size = task.CheckPageSize(page_size)

	userRes, _, userItem := task.GetUserInfo(c)
	if !userRes {
		slog.Error.Println("获取用户信息失败")
		response := utils.NewResponseFailed()
		response.Msg = "获取用户信息失败"
		utils.ResponseFaied(c, response)
		return
	}

	bq := model.SCenterEvaluateListQuery{
		BizSoftForm: c.Query("bizsoft_form"),
		BizSoftType: c.Query("bizsoft_type"),
		Product:     c.Query("prod"),
		StartDate:   c.Query("start_date"),
		EndDate:     c.Query("end_date"),
		CurrentFlow: c.Query("current_flow"),
		Key:         c.Query("key"),
		EvaluatorId: *userItem.Id,
		SortColumn:  c.Query("sort_colunm"),
		SortOrder:   c.Query("sort_order"),
	}
	res := model.GetSCenterEvaluateList(global.STOCK_TYPE_BUSINESS, page_size, page_index, bq)
	if res == nil {
		slog.Error.Println("获取入库概要信息列表失败")
		response := utils.NewResponseFailed()
		response.Msg = "获取入库概要信息列表失败"
		utils.ResponseFaied(c, response)
	} else {
		response := utils.NewResponseSuccess()
		response.Data = *res
		utils.ResponseSuccess(c, response)
	}
}
