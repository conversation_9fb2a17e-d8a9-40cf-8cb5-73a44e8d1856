package controller

import (
	"slam/global"
	"slam/task"
	"slam/zentao"

	"github.com/robfig/cron/v3"
)

func InitTimer() {
	cronTab := cron.New(cron.WithSeconds())
	//按10秒间隔循环调用
	spec1 := "*/10 * * * * *"
	cronTab.AddFunc(spec1, Scheduler)
	cronTab.AddFunc(spec1, global.RefreshConfigYaml)
	//按每天12点循环调用
	spec2 := "0 0 0 * * *"
	cronTab.AddFunc(spec2, TempFilesRecycl)
	cronTab.AddFunc(spec2, CleanHookTmpFile)
	cronTab.AddFunc(spec2, TimeOutXflowProc)
	//按每天凌晨1点循环调用
	spec3 := "0 0 1 * * *"
	cronTab.AddFunc(spec3, AutoBackupPostgresSql)
	cronTab.AddFunc(spec3, task.UpdateTagsInheritanceArray)
	//按每小时循环调用
	spec4 := "0 0 * * * *"
	cronTab.AddFunc(spec4, RefreshApptoken)
	//按每天下午2点循环调用
	spec5 := "0 0 14 * * *"
	cronTab.AddFunc(spec5, task.MsgLxScheduledToUser)
	cronTab.AddFunc(spec5, task.MsgLxScheduledToReview)

	// cronTab.AddFunc(utils.TimerAutoStockListen, ListenUpdate) // 监听更新
	// cronTab.AddFunc(utils.TimerAutoStock, AutoStock)          // 监听更新自动入库
	// cronTab.AddFunc(utils.TimerAutoStockFlush, FlushSha256)   // 刷新软件包的sha256

	// // 五分钟一次
	// spec9 := "* */5 * * * *"
	// cronTab.AddFunc(spec9, FlushBuildInfo) // 定时刷新集成单构建信息

	spec10 := "0 0 */3 * * *"
	cronTab.AddFunc(spec10, zentao.RefreshZentaoToken)

	cronTab.Start()
}

func InitKojiScanner() {
	go ScanUpdateKojiTask()
	go ScanUpdateKojiBuild()
	go ScanUpdateKojiTagBuild()
}
