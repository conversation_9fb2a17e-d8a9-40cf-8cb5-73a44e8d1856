/*
 * @Author: lwq <EMAIL>
 * @Date: 2025-05-20
 * @LastEditors: lwq <EMAIL>
 * @LastEditTime: 2025-07-09
 * @FilePath: /ctdy-guard/slam/controller/integration.go
 * @Description: 集成单控制器 用于定时任务
 * @Copyright (c) 2025 by 2022.kylinos.cn, All Rights Reserved.
 */
package controller

// func FlushBuildInfo() {
// 	flushTask := task.NewIntegration()
// 	ids, err := flushTask.GetNotExistBuildRpmIds()
// 	if err != nil {
// 		slog.Error.Println("FlushBuildInfo failed, err:", err)
// 		return
// 	}

// 	for _, id := range ids {
// 		flushTask.FlushBuildInfo(id)
// 	}
// }
