/*
 * @Author: lwq <EMAIL>
 * @Date: 2025-04-30
 * @LastEditors: lwq <EMAIL>
 * @LastEditTime: 2025-07-30
 * @FilePath: /ctdy-guard/slam/model/integration.go
 * @Description: 集成单模型
 * @Copyright (c) 2025 by 2022.kylinos.cn, All Rights Reserved.
 */
package model

import (
	"slam/global"
	"slam/utils"
	"strconv"
	"time"

	"gorm.io/gorm"
)

var queryByid = "id = ?"

// Integration 集成单模型
type Integration struct {
	Id          int       `gorm:"column:id;primaryKey;autoIncrement:true;type:serial4;not null" json:"id,string,omitempty" label:"序号，主键"`
	AliasName   string    `gorm:"column:alias_name;type:varchar(255);not null;default:''" json:"alias_name" label:"别名"`
	Remark      string    `gorm:"column:remark;type:text;not null;default:''" json:"remark" label:"备注"`
	Urgent      int       `gorm:"column:urgent;type:int2;not null;default:1" json:"urgent" label:"紧急程度1：普通 2：紧急"`
	Status      int       `gorm:"column:status;type:int2;not null;default:1" json:"status" label:"状态"`
	LockStatus  int       `gorm:"column:lock_status;type:int2;not null;default:1" json:"lock_status" label:"锁定状态1未锁定2已锁定"`
	CreatorId   int       `gorm:"column:creator_id;not null;default:0" json:"creator_id" label:"创建人ID"`
	CreatorName string    `gorm:"column:creator_name;type:varchar(255);not null;default:''" json:"creator_name" label:"创建人名称"`
	StartAt     time.Time `gorm:"column:start_time;not null" json:"start_time,omitempty" label:"开始时间"`
	EnddAt      time.Time `gorm:"column:end_time;not null" json:"end_time,omitempty" label:"结束时间"`
	CreatedAt   time.Time `gorm:"column:create_time;not null" json:"create_time,omitempty" label:"创建时间"`
	UpdatedAt   time.Time `gorm:"column:update_time;not null" json:"update_time,omitempty" label:"更新时间"`
}

// TableName 表名
func (*Integration) TableName() string {
	return "slam_integration"
}

// Rpm
type Rpm struct {
	SrcName string `json:"src_name"`
	Sha256  string `json:"signd_sha256"`
}

type StockBugCveList struct {
	XflowId    int       `gorm:"column:xflow_id" json:"id"`
	UserId     int       `gorm:"column:user_id" json:"user_id"`
	UserName   string    `gorm:"column:nickname" json:"nickname"`
	CreateTime time.Time `gorm:"column:create_time" json:"create_time"`
	Type       string    `gorm:"column:type" json:"type"`
	TestInfo   string    `gorm:"column:test_info" json:"test_info"`
	CveInfo    []CveInfo `gorm:"column:cve_info;serializer:json" json:"cve_info"`
	BugInfo    []BugInfo `gorm:"column:bug_info;serializer:json" json:"bug_info"`
	Packs      []int     `gorm:"column:packs;serializer:json" json:"packs"`
}

type CveInfo struct {
	CveID            string `json:"cve_id"`
	KveID            string `json:"kve_id"`
	ProductID        string `json:"product_id"`
	Arch             string `json:"arch"`
	ZentaoURL        string `json:"zentao_url"`
	PackageName      string `json:"package_name"`
	OpFix            string `json:"op_fix"`
	OpFixInfo        string `json:"op_fix_info"`
	InstallStatement string `json:"install_statement"`
}

// ProductRelation 产品关系结构体
type ProductRelation struct {
	FixState  string `json:"fix_state"`
	ProductID string `json:"product_id"`
	State     string `json:"state"`
}

// SrpmRelation SRPM关系结构体
type SrpmRelation struct {
	ProductID       string `json:"product_id"`
	Framework       string `json:"framework"`
	PackageSrpmName string `json:"package_srpm_name"`
	ProductName     string `json:"product_name"`
}

// BugInfo Bug信息结构体
type BugInfo struct {
	Item            string            `json:"item"`
	Title           string            `json:"title"`
	Emergency       string            `json:"emergency"`
	BugSource       string            `json:"bug_source"`
	BugDetail       string            `json:"bug_detail"`
	ProductRelation []ProductRelation `json:"product_relation"`
	IsDz            string            `json:"isdz"`
	SrpmRelation    []SrpmRelation    `json:"srpm_relation"`
	FixStatus       string            `json:"fix_status"`
	UserTag         string            `json:"user_tag"`
	PublishDate     string            `json:"publish_date"`
	ReplayMethod    string            `json:"replay_method"`
	AnalysisCourse  string            `json:"analysis_course"`
	AnalysisResult  string            `json:"analysis_result"`
	RepairMethod    string            `json:"repair_method"`
	VerifyMethod    string            `json:"verify_method"`
	BugFile         any               `json:"bug_file"`
	BugFileInvalid  bool              `json:"bug_file_invalid"`
	Zid             string            `json:"zid"`
	BugType         string            `json:"bug_type"`
}

// BuildMapping 构建映射
type BuildMapping struct {
	IpAddress string `gorm:"column:ip"`
	XflowId   int    `gorm:"column:xflow_id"`
	SrcName   string `gorm:"column:src_name"`
	TagId     int    `gorm:"column:tag_id"`
	TagName   string `gorm:"column:tag_name"`
}

// StockCenterPacks 库存包列表
type StockCenterPacks struct {
	XflowId    int
	Packs      []int
	UserId     int
	UserName   string
	CreateTime time.Time
	Type       string
}

type Evaluator struct {
	Id           int    `gorm:"column:id" json:"id"`
	XflowId      int    `gorm:"column:xflow_id" json:"xflow_id"`
	Nickname     string `gorm:"column:nickname;type:varchar(128)" json:"nickname"`
	Email        string `gorm:"column:e_mail;type:varchar(32)" json:"e_mail"`
	EvaluateType string `gorm:"column:evaluate_type;type:varchar(32)" json:"evaluate_type"`
}

type BugCveListReq struct {
	IgId      int
	XflowId   int
	StartDate string
	EndDate   string
	Source    int
}

type PacksListReq struct {
	IgId      int
	XflowId   int
	Uid       int
	SrcName   string
	StartDate string
	EndDate   string
	Urgent    int
	Status    int
}

type PacksListResp struct {
	XflowId int    `gorm:"column:xflow_id" json:"xflow_id"`
	Type    string `gorm:"column:type" json:"type"`
	SrcName string `gorm:"column:src_name" json:"src_name"`
}

// IntegrationItem 集成单项接口 待定是否还继续使用
type IntegrationItem interface {
	/**
	 * @author: lwq <EMAIL>
	 * @description: GetID 获取id
	 * @return {int}
	 */
	GetID() int
	/**
	 * @author: lwq <EMAIL>
	 * @description: GetProductID 获取产品id
	 * @return {int}
	 */
	GetProductID() int
	/**
	 * @author: lwq <EMAIL>
	 * @description: GetXflowID 获取xflowid
	 * @return {*}
	 */
	GetXflowID() int
	/**
	 * @author: lwq <EMAIL>
	 * @description: GetRpms 获取构建二进制包列表
	 * @return {*}
	 */
	GetRpms() []*Rpm
}

// IntegrationRepo 集成单仓库接口
type IntegrationRepo interface {
	/**
	 * @author: lwq <EMAIL>
	 * @description: CreateIntegration 创建集成单
	 * @param {*Integration} integration
	 * @return {int, error} 返回新插入记录的ID和错误信息
	 */
	CreateIntegration(integration *Integration) (int, error)

	/**
	 * @author: lwq <EMAIL>
	 * @description:GetIntegrationList 分页查询集成单列表
	 * @param {utils.Pager} pager
	 * @return {[]*Integration, int64, error}
	 */
	GetIntegrationList(pager utils.Pager, req *PacksListReq) (*Data_List, error)

	/**
	 * @author: lwq <EMAIL>
	 * @description: GetIntegrationById 根据id查询集成单
	 * @return {*}
	 */
	GetIntegrationById(id int) (*Integration, error)

	/**
	 * @author: lwq <EMAIL>
	 * @description:GetNotExistBuildRpmIds 获取未构建的集成单id
	 * @return {[]int, error}
	 * @return {*}
	 */
	GetNotExistBuildRpmIds() ([]int, error)

	/**
	 * @author: lwq <EMAIL>
	 * @description: UpdateIntegration 更新集成单
	 * @return {*}
	 */
	UpdateIntegration(req *Integration) error
}

// integrationModel 集成单模型
type integrationModel struct {
	db *gorm.DB
}

/**
 * @author: lwq <EMAIL>
 * @description:  NewIntegrationModel 初始化集成单模型
 * @return {*integrationModel}
 */
func NewIntegrationModel() IntegrationRepo {
	return &integrationModel{
		db: GetDB(),
	}
}

// CreateIntegration 创建集成单
func (i *integrationModel) CreateIntegration(integration *Integration) (int, error) {
	if err := i.db.Create(integration).Error; err != nil {
		return 0, err
	}
	return integration.Id, nil
}

// 更新集成单
func (i *integrationModel) UpdateIntegration(req *Integration) error {
	if err := i.db.Model(&Integration{}).
		Where(queryByid, req.Id).
		Updates(req).Error; err != nil {
		return err
	}
	return nil
}

// GetIntegrationList 分页查询集成单列表
func (i *integrationModel) GetIntegrationList(pager utils.Pager, req *PacksListReq) (*Data_List, error) {
	var integrations []*Integration
	var total int64
	query := i.db.Model(&Integration{})

	// 开始时间
	if req.StartDate != "" {
		query = query.Where("start_time >= ?", req.StartDate)
	}
	if req.EndDate != "" {
		query = query.Where("end_time <= ?", req.EndDate)
	}

	// 模糊查询
	if req.SrcName != "" {
		query = query.Where("alias_name LIKE ?", "%"+req.SrcName+"%")
	}
	// 是否紧急、集成单状态 集成单ID 软件包名
	if req.Urgent > 0 {
		query = query.Where("urgent = ?", req.Urgent)
	}
	if req.IgId > 0 {
		query = query.Where(queryByid, req.IgId)
	}
	if req.Status > 0 {
		query = query.Where("status = ?", req.Status)
	}

	err := query.
		Limit(pager.Size).Offset(pager.Offset).Find(&integrations).
		Limit(-1).Offset(-1).Count(&total).Error
	if err != nil {
		return nil, err
	}
	return &Data_List{
		List: integrations,
		Page: PageRes{
			Page_index: pager.Index,
			Page_size:  pager.Size,
			Total:      total,
		},
	}, nil
}

// GetIntegrationById 根据id查询集成单, 连表查询一下统计信息
func (i *integrationModel) GetIntegrationById(id int) (*Integration, error) {
	var integration Integration
	err := i.db.Where(queryByid, id).First(&integration).Error
	if err != nil {
		return nil, err
	}
	return &integration, nil
}

// GetNotExistBuildRpmIds 获取未构建的集成单id
func (i *integrationModel) GetNotExistBuildRpmIds() ([]int, error) {
	var ids []int
	err := i.db.Model(&Integration{}).Select("id").
		Where("NOT EXISTS (SELECT 1 FROM slam_integration_rpm where integration_id = slam_integration.id)").
		Pluck("id", &ids).Error

	if err != nil {
		return nil, err
	}
	return ids, nil
}

// XfllowModelRepo 工作流模型接口
type XfllowModelRepo interface {
	/**
	 * @author: lwq <EMAIL>
	 * @description: GetStockBugCveList 获取库存bug和cve列表
	 * @param {*BugCveListReq} bq
	 * @return {[]*IntegrationBug}
	 * @return {[]*IntegrationCve}
	 * @return {error}
	 */
	GetStockBugCveList(bq *BugCveListReq) ([]*StockBugCveList, []*IntegrationBug, []*IntegrationCve, error)

	/**
	 * @author: lwq <EMAIL>
	 * @description: GetBuildInfosByXflowIds 根据xflowIds获取构建信息
	 * @param {[]int} xflowIds
	 * @return {[]*BuildMapping, error}
	 */
	GetBuildInfosByXflowIds(xflowIds []int) ([]*BuildMapping, error)

	/**
	 * @author: lwq <EMAIL>
	 * @description: GetStockCenterPacksByIds 根据id获取库存包列表
	 * @param {[]int} ids
	 * @return {[]*StockCenterPackList}
	 */
	GetStockCenterPacksByIds(ids []int) []*StockCenterPackList

	/**
	 * @author: lwq <EMAIL>
	 * @description: GetPackageList 查询所有包信息
	 * @param {utils.Pager} pager
	 * @param {PacksListReq} req
	 * @return {*}
	 */
	GetPackageList(pager utils.Pager, req PacksListReq) (*Data_List, error)

	/**
	 * @author: lwq <EMAIL>
	 * @description: GetNeedReplenishPackages 查询需要补录的软件包
	 * @param {int} igid
	 * @param {[]int} xflowIds
	 * @return {[]string, error}
	 */
	GetNeedReplenishPackages(igid int, xflowIds []int, packageName string) ([]string, error)
}

// xfllowModel 工作流模型
type xfllowModel struct {
	db *gorm.DB
}

// NewXfllowModel 初始化工作流模型
func NewXfllowModel() XfllowModelRepo {
	return &xfllowModel{
		db: GetDB(),
	}
}

// GetStockBugCveList 获取库存bug和cve列表
func (x *xfllowModel) GetStockBugCveList(bq *BugCveListReq) ([]*StockBugCveList, []*IntegrationBug, []*IntegrationCve, error) {
	rows, err := x.getStockList(bq)
	if err != nil {
		return nil, nil, nil, err
	}
	var bugs []*IntegrationBug
	var cves []*IntegrationCve
	for _, v := range rows {
		if len(v.BugInfo) > 0 {
			bugs = append(bugs, GetIntegrationBugList(v, bq)...)
		}

		if len(v.CveInfo) > 0 {
			cves = append(cves, GetIntegrationCveList(v, bq)...)
		}
	}

	return rows, bugs, cves, err
}

// GetBuildInfosByXflowIds 根据xflowid查询所有buildid
func (x *xfllowModel) GetBuildInfosByXflowIds(xflowIds []int) ([]*BuildMapping, error) {
	var buildIds []*BuildMapping
	err := x.db.Model(&KojiList{}).
		Select("xflow_id, ip, src_name, slam_taglist.id as tag_id, slam_taglist.name as tag_name").
		Joins("left join slam_taglist on slam_kojilist.tag = slam_taglist.id").
		Where("xflow_id IN (?)", xflowIds).
		Where("type = ?", global.KOJI_TYPE_BUILD).
		Find(&buildIds).Error
	if err != nil {
		return nil, err
	}
	return buildIds, nil
}

// GetStockCenterPacksByIds 根据id获取库存包列表
func (x *xfllowModel) GetStockCenterPacksByIds(ids []int) []*StockCenterPackList {
	// 先查pack列表，连表查到product的arch信息
	var packList []*StockCenterPackList
	x.db.Model(&StockCenterPackList{}).
		Where("id in (?)", ids).
		Find(&packList)

	return packList
}

// GetArchsByIds 根据archIds获取arch列表
func (x *xfllowModel) GetArchsByIds(archIds []int) map[int]string {
	type ArchResult struct {
		ID   int    `gorm:"column:id"`
		Name string `gorm:"column:name"`
	}
	var results []*ArchResult
	// 查询arch列表
	x.db.Model(&Framework{}).
		Select("id, name").
		Where("id IN (?)", archIds).
		Find(&results)

	// 转换为map
	archMap := make(map[int]string)
	for _, r := range results {
		archMap[r.ID] = r.Name
	}
	return archMap
}

// GetEvaluatorsByXflowId 根据xflowId查询评估人
func (x *xfllowModel) GetEvaluatorsByXflowIds(xflowIds []int) map[int][]*Evaluator {
	if len(xflowIds) == 0 {
		return make(map[int][]*Evaluator)
	}

	evaluatorList := []*Evaluator{}
	evaluatorMap := map[int][]*Evaluator{}
	x.db.Table("slam_evaluatedetail as e").
		Select("e.id, e.xflow_id, e.evaluate_type, u.nickname, u.e_mail").
		Joins("left join \"user\" as u on u.id = e.\"userId\"").
		Where("e.xflow_id in (?)", xflowIds).
		Find(&evaluatorList)

	for _, evaluator := range evaluatorList {
		evaluatorMap[evaluator.XflowId] = append(evaluatorMap[evaluator.XflowId], evaluator)
	}

	return evaluatorMap
}

// GetPackageList 获取包列表
func (x *xfllowModel) GetPackageList(pager utils.Pager, req PacksListReq) (*Data_List, error) {
	var infos []*PacksListResp
	var total int64
	query := x.db.Table("slam_xflow").
		Select("slam_xflow.id as xflow_id,slam_xflow.type," +
			"array_to_string( ARRAY_AGG ( DISTINCT COALESCE(slam_kojilist.src_name, slam_stocktagpacklist.package_name) ), ',' ) AS src_name").
		Joins("left join slam_kojilist on slam_kojilist.xflow_id = slam_xflow.id").
		Joins("left join slam_stocktagpacklist ON slam_stocktagpacklist.xflow_id = slam_xflow.id")

	if req.SrcName != "" {
		query = query.Where("slam_kojilist.src_name like ? or slam_stocktagpacklist.package_name like ?", req.SrcName+"%", req.SrcName+"%")
	}

	if req.XflowId > 0 {
		query = query.Where("slam_xflow.id = ?", req.XflowId)
	}

	query.Where("slam_xflow.status", global.XFLOW_STATUS_FINISHED).
		Where("slam_xflow.\"type\" in (?)", []string{
			global.XFLOW_TYPE_STOCK, global.XFLOW_TYPE_STOCK_CENTER, global.XFLOW_TYPE_STOCK_BIZ,
			global.XFLOW_TYPE_STOCK_TAG, global.XFLOW_TYPE_STOCK_UNTAG,
		}).
		Group("slam_xflow.id").Order("slam_xflow.id desc").
		Limit(pager.Size).Offset(pager.Offset).
		Find(&infos).Count(&total)

	return &Data_List{
		List: infos,
		Page: PageRes{
			Page_index: pager.Index,
			Page_size:  pager.Size,
			Total:      total,
		},
	}, query.Error
}

// GetNeedReplenishPackages 查询需要补货的包名
func (x *xfllowModel) GetNeedReplenishPackages(igid int, xflowIds []int, packageName string) ([]string, error) {
	k := (&KojiList{}).TableName()
	ik := (&IntegrationKoji{}).TableName()
	var packages []string

	subQuery := x.db.Table(k+" as sk").Select("1").
		Where("sk.xflow_id IN (?) ", xflowIds).
		Where("ik.package_name = sk.src_name")

	query := x.db.Table(ik + " as ik")
	if packageName != "" {
		query.Where("ik.package_name like ?", packageName+"%")
	}
	err := query.Where("ik.integration_id = ?", igid).
		Where("NOT EXISTS (?)", subQuery).
		Pluck("ik.package_name", &packages).Error

	return packages, err
}

/**
 * @author: lwq <EMAIL>
 * @description: getStockList 获取入信息库列表
 * @param {*StockListQuery} bq
 * @return {*}
 */
func (x *xfllowModel) getStockList(bq *BugCveListReq) ([]*StockBugCveList, error) {
	var infos []*StockBugCveList
	query := x.db.Table("slam_xflow").
		Select("slam_xflow.id as xflow_id, slam_xflow.user_id, \"user\".nickname, slam_xflow.create_time, " +
			"COALESCE(slam_stocklist.bug_info, slam_stocktaglist.bug_info, '[]') as bug_info, " +
			"COALESCE(slam_stocklist.cve_info, slam_stocktaglist.cve_info, '[]') as cve_info, " +
			"COALESCE(slam_stocklist.test_info, slam_stocktaglist.test_info, slam_stockcenterlist.test_info) as test_info, " +
			"COALESCE(slam_stockcenterlist.packs, '[]') as packs, slam_xflow.type").
		Joins("left join slam_stocklist on slam_stocklist.xflow_id = slam_xflow.id").
		Joins("left join slam_stockcenterlist on slam_stockcenterlist.xflow_id = slam_xflow.id").
		Joins("left join slam_stocktaglist ON slam_stocktaglist.xflow_id = slam_xflow.id").
		Joins("left join \"user\" on \"user\".id = slam_xflow.user_id")

	if len(bq.StartDate) > 0 {
		query = query.Where("slam_xflow.create_time >= ?", bq.StartDate)
	}
	if len(bq.EndDate) > 0 {
		query = query.Where("slam_xflow.create_time <= ?", bq.EndDate)
	}
	if bq.XflowId > 0 {
		query = query.Where("slam_xflow.id = ?", bq.XflowId)
	}
	query.Where("slam_xflow.status", global.XFLOW_STATUS_FINISHED).
		Where("slam_xflow.\"type\" in (?)", []string{
			global.XFLOW_TYPE_STOCK, global.XFLOW_TYPE_STOCK_CENTER, global.XFLOW_TYPE_STOCK_BIZ,
			global.XFLOW_TYPE_STOCK_TAG, global.XFLOW_TYPE_STOCK_UNTAG,
		}).Find(&infos)

	if query.Error != nil {
		return nil, query.Error
	} else {
		return infos, nil
	}
}

/**
 * @author: lwq <EMAIL>
 * @description: getIntegrationBugList 获取集成单bug列表
 * @param {*StockBugCveList} list
 * @param {int} igId 集成单id
 * @return {[]*IntegrationBug }
 */
func GetIntegrationBugList(list *StockBugCveList, bq *BugCveListReq) []*IntegrationBug {
	var bugs []*IntegrationBug
	for _, bug := range list.BugInfo {
		for _, r := range bug.SrpmRelation {
			pid, _ := strconv.Atoi(r.ProductID)
			bugs = append(bugs, &IntegrationBug{
				IntegrationId: bq.IgId,
				XflowId:       list.XflowId,
				SubmiterId:    list.UserId,
				SubmiterName:  list.UserName,
				CreatedAt:     list.CreateTime,
				SrcName:       r.PackageSrpmName,
				ProductId:     pid,
				ArchInfo:      r.Framework,
				TaskType:      list.Type,
				BugType:       bug.BugType,
				Level:         bug.Emergency,
				ZentaoNo:      bug.Zid,
				Describtion:   utils.StripHTML(bug.BugDetail),
				ReplayMethod:  utils.StripHTML(bug.ReplayMethod),
				RepairMethod:  utils.StripHTML(bug.RepairMethod),
				TestInfo:      utils.StripHTML(list.TestInfo),
				Source:        bq.Source,
			})
		}
	}
	return bugs
}

/**
 * @author: lwq <EMAIL>
 * @description: getIntegrationCveList 获取集成单cve列表
 * @param {*StockBugCveList} list
 * @param {int} igId 集成单id
 * @return {[]*IntegrationCve}
 */
func GetIntegrationCveList(list *StockBugCveList, bq *BugCveListReq) []*IntegrationCve {
	var cves []*IntegrationCve
	for _, cve := range list.CveInfo {
		pid, _ := strconv.Atoi(cve.ProductID)
		cves = append(cves, &IntegrationCve{
			IntegrationId: bq.IgId,
			XflowId:       list.XflowId,
			SubmiterId:    list.UserId,
			SubmiterName:  list.UserName,
			CreatedAt:     list.CreateTime,
			SrcName:       cve.PackageName,
			ProductId:     pid,
			ArchInfo:      cve.Arch,
			TaskType:      list.Type,
			Level:         "", // 暂时不用风险等级
			CveId:         cve.CveID,
			FixInfo:       cve.OpFixInfo,
			Source:        bq.Source,
		})
	}
	return cves
}
