package model

import (
	"regexp"
	"testing"
	"time"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/stretchr/testify/assert"
)

func TestCreateIntegrationCheck(t *testing.T) {
	assert := assert.New(t)

	// 设置测试数据
	testTime := time.Now()
	testCheck := []*IntegrationCheck{{
		CheckerID:     100,
		CheckerName:   "审核人",
		IntegrationId: 1,
		CheckResult:   IntegrationCheckPass,
		CheckAdvice:   "测试审核意见",
		CheckType:     IntegrationCheckTypeBug,
		Identifier:    1001,
		IgStatus:      0, // 添加缺失的字段
		CreatedAt:     testTime,
		UpdatedAt:     testTime,
	}}

	// 初始化测试数据库
	gormDB, tmock, err := initSqlmock()
	assert.NoError(err)

	// 设置SQL预期 - 修正SQL语句和参数以匹配实际执行的查询
	tmock.ExpectBegin()
	tmock.ExpectQuery(regexp.QuoteMeta(`INSERT INTO "slam_integration_check" ("identifier","integration_id","ig_status","check_type","checker_id","checker_name","checker_email","check_result","check_advice","create_time","update_time") VALUES ($1,$2,$3,$4,$5,$6,$7,$8,$9,$10,$11)`)).
		WithArgs(1001, 1, 0, IntegrationCheckTypeBug, 100, "审核人", "", IntegrationCheckPass, "测试审核意见", testTime, testTime).
		WillReturnRows(sqlmock.NewRows([]string{"id"}).AddRow(1))
	tmock.ExpectCommit()

	// 执行测试
	model := &integrationCheckModel{db: gormDB}
	err = model.CreateIntegrationCheck(testCheck)

	// 验证结果
	assert.NoError(err)
	assert.NoError(tmock.ExpectationsWereMet())
}

func TestGetIntegrationCheckList(t *testing.T) {
	assert := assert.New(t)

	// 设置测试数据
	testTime := time.Now()

	// 初始化测试数据库
	gormDB, tmock, err := initSqlmock()
	assert.NoError(err)

	// 设置SQL预期
	tmock.ExpectQuery(regexp.QuoteMeta(`SELECT * FROM "slam_integration_check" WHERE identifier = $1 AND check_type = $2`)).
		WithArgs(1, IntegrationCheckTypeBug).
		WillReturnRows(
			sqlmock.NewRows([]string{
				"id", "checker_id", "checker_name", "integration_id", "check_result", "check_advice",
				"check_type", "identifier", "create_time", "update_time",
			}).AddRow(
				1, 100, "测试审核人", 1, IntegrationCheckPass, "测试审核意见1",
				IntegrationCheckTypeBug, 1001, testTime, testTime,
			))

	// 执行测试
	model := &integrationCheckModel{db: gormDB}
	checks, err := model.GetCheckList(1, IntegrationCheckTypeBug)
	// 验证结果
	assert.NoError(err)
	assert.Equal(1, len(checks))
	assert.NoError(tmock.ExpectationsWereMet())
}

func TestGetIntegrationCheckListByIgId(t *testing.T) {
	assert := assert.New(t)

	// 设置测试数据
	testTime := time.Now()

	// 初始化测试数据库
	gormDB, tmock, err := initSqlmock()
	assert.NoError(err)

	// 设置SQL预期
	tmock.ExpectQuery(regexp.QuoteMeta(`SELECT * FROM "slam_integration_check" WHERE integration_id = $1 AND check_type = $2`)).
		WithArgs(1, IntegrationCheckTypeBug).
		WillReturnRows(
			sqlmock.NewRows([]string{
				"id", "checker_id", "checker_name", "integration_id", "check_result", "check_advice",
				"check_type", "identifier", "create_time", "update_time",
			}).AddRow(
				1, 100, "测试审核人", 1, IntegrationCheckPass, "测试审核意见1",
				IntegrationCheckTypeBug, 1001, testTime, testTime,
			))

	// 执行测试
	model := &integrationCheckModel{db: gormDB}
	checks, err := model.GetCheckListByIgId(1, IntegrationCheckTypeBug)
	// 验证结果
	assert.NoError(err)
	assert.Equal(1, len(checks))
	assert.NoError(tmock.ExpectationsWereMet())
}
