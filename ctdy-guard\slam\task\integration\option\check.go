/*
 * @Author: lwq <EMAIL>
 * @Date: 2025-07-08
 * @LastEditors: lwq <EMAIL>
 * @LastEditTime: 2025-07-27
 * @FilePath: /ctdy-guard/slam/task/integration/option/check.go
 * @Description: 集成操作
 * @Copyright (c) 2025 by 2022.kylinos.cn, All Rights Reserved.
 */
package option

import (
	"errors"
	"slam/model"
	"slam/slog"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type CheckReq struct {
	Id          int
	IgId        int
	CheckResult int
	CheckAdvice string
	CheckType   int
}

// CheckOption 审核操作
type CheckOption struct {
	BaseOption
	igCheckModel model.IntegrationCheckRepo
	igCountModel model.IntegrationCountRepo
	bugModel     model.IntegrationRowsRepo
	cveModel     model.IntegrationRowsRepo
}

func NewCheckOption() *CheckOption {
	return &CheckOption{
		BaseOption: BaseOption{
			ID:   "check",
			Name: "审核",
		},
		igCheckModel: model.NewIntegrationCheckModel(),
		igCountModel: model.NewIntegrationCountModel(),
		bugModel:     model.NewIntegrationBugModel(),
		cveModel:     model.NewIntegrationCveModel(),
	}
}

func (o *CheckOption) Execute(c *gin.Context, opUser *OpUser, params any) (any, error) {
	req := params.(*CheckReq)

	umap := map[string]any{}
	bumap := map[string]any{"check_res": req.CheckResult}
	var m model.IntegrationRowsRepo
	switch req.CheckType {
	case model.IntegrationCheckTypeBug:
		m = o.bugModel
		umap["review_bug_count"] = gorm.Expr("review_bug_count + ?", 1)
	case model.IntegrationCheckTypeCve:
		m = o.cveModel
		umap["review_cve_count"] = gorm.Expr("review_cve_count + ?", 1)
	}

	if req.CheckResult == model.IntegrationCheckUnPass {
		umap["unpublish_count"] = gorm.Expr("unpublish_count + ?", 1)
		bumap["is_publish"] = req.CheckResult
	}
	// 更新审核状态及发布状态
	rowsAffected, _ := m.UpdateWithMap(req.Id, bumap)
	if rowsAffected <= 0 {
		slog.ErrorWithContext(c, "更新状态失败，受影响行数为0")
		return nil, errors.New("审核失败，请重试！")
	}

	// 写入审核数据库审核记录表
	err := o.addCheckInfo(req, opUser)
	if err != nil {
		slog.ErrorWithContext(c, err)
		return nil, err
	}

	// 更新统计信息
	err = o.igCountModel.UpdateCountInfo(req.IgId, opUser.OpIgInfo.Status, umap)
	if err != nil {
		return nil, err
	}

	return nil, nil
}

/**
 * @author: lwq <EMAIL>
 * @description: 添加审核信息
 * @param {*CheckReq} req
 * @param {*model.User} user
 * @return {*}
 */
func (o *CheckOption) addCheckInfo(req *CheckReq, opUser *OpUser) error {
	igCheckInfos := []*model.IntegrationCheck{{
		IntegrationId: req.IgId,
		IgStatus:      opUser.OpIgInfo.Status,
		Identifier:    req.Id,
		CheckerID:     opUser.UserId,
		CheckerName:   opUser.UserName,
		CheckerEmail:  opUser.Email,
		CheckResult:   req.CheckResult,
		CheckAdvice:   req.CheckAdvice,
		CheckType:     req.CheckType,
	}}

	return o.igCheckModel.CreateIntegrationCheck(igCheckInfos)
}
