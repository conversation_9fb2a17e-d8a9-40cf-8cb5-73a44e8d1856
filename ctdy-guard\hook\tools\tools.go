package tools

import (
	"fmt"
	etree "github.com/beevik/etree"
	"math"
	"math/rand"
	"slam-hook/global"
	"slam-hook/logs"
	"strings"
	"time"
)

func CheckUpdateHookResultErr(method string, err error) {
	if err != nil {
		logs.Log.<PERSON>rf(global.UpdateResultFailed, method, err.Error())
	}
}

func CreateRandStr(strLen int) string {
	localRand := rand.New(rand.NewSource(time.Now().UnixNano()))
	const charset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"

	b := make([]byte, strLen)
	for i := range b {
		b[i] = charset[localRand.Intn(len(charset))]
	}
	return string(b)
}

func ParseXml(xmlPath string) (map[string]string, error) {
	result := map[string]string{}
	doc := etree.NewDocument()
	if err := doc.ReadFromFile(xmlPath); err != nil {
		panic(err)
	}
	root := doc.SelectElement("ReportDefinition")
	var groupingSections []*etree.Element
	for _, element := range root.SelectElements("ReportSection") {
		title := element.SelectElement("Title")
		if title.Text() == "Report Overview" {
			for _, subSection := range element.SelectElements("SubSection") {
				if subSection.FindElement("IssueListing") != nil {
					issueListing := subSection.SelectElement("IssueListing")
					chart := issueListing.SelectElement("Chart")
					groupingSections = chart.SelectElements("GroupingSection")
					break
				}
			}
			break
		}
	}
	for _, group := range groupingSections {
		attr := group.Attr
		probleNum := attr[0].Value
		problemLevel := group.SelectElement("groupTitle").Text()
		result[problemLevel] = probleNum
	}
	return result, nil
}

func ReplaceKojiAddr(taskInfoUrl string) string {
	httpUrl := "http://10.44.48.204"
	if taskInfoUrl == httpUrl {
		taskInfoUrl = strings.Replace(taskInfoUrl, httpUrl, "https://buildsystem.kylinos.cn:8242", -1)
	} else if strings.Contains(taskInfoUrl, httpUrl) {
		taskInfoUrl = strings.Replace(taskInfoUrl, httpUrl, "https://buildsystem.kylinos.cn", -1)
	}
	return taskInfoUrl
}

func CalculateAverage(data []float64) (float64, error) {
	if len(data) == 0 {
		return 0, fmt.Errorf("切片为空，无法计算平均数")
	}

	sum := 0.0
	for _, num := range data {
		sum += num
	}
	average := sum / float64(len(data))

	// 检查 NaN 或 Inf（极端情况）
	if math.IsNaN(average) || math.IsInf(average, 0) {
		return 0, fmt.Errorf("计算结果无效（NaN 或 Inf）")
	}
	return average, nil
}
