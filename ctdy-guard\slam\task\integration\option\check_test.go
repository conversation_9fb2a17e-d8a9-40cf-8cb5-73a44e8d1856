package option

import (
	"slam/model"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"gorm.io/gorm"
)

func TestCheckOptionExecute(t *testing.T) {
	NewCheckOption()
	NewReCheckOption()
	NewReviewOption()
	NewTestResultOption()
	gin.SetMode(gin.TestMode)

	t.Run("successful", func(t *testing.T) {
		// Mocks
		mockCheckModel := new(MockIntegrationCheckRepo)
		mockCountModel := new(MockIntegrationCountRepo)
		mockBugModel := new(MockIntegrationRowsRepo)
		mockCveModel := new(MockIntegrationRowsRepo)

		// Test data
		req := &CheckReq{
			Id:          1,
			IgId:        10,
			CheckResult: model.IntegrationCheckPass,
			CheckAdvice: "Looks good",
			CheckType:   model.IntegrationCheckTypeBug,
		}

		opUser := &OpUser{
			UserInfo: UserInfo{
				UserId:   1,
				UserName: "testuser",
				Email:    "<EMAIL>",
			},
			OpIgInfo: &model.Integration{
				Status: 1,
			},
		}

		// 设置mock期望
		mockBugModel.On("UpdateWithMap", 1, map[string]any{"check_res": model.IntegrationCheckPass}).Return(int(1), nil)
		mockCheckModel.On("CreateIntegrationCheck", mock.AnythingOfType("[]*model.IntegrationCheck")).Return(nil)
		mockCountModel.On("UpdateCountInfo", 10, 1, map[string]any{
			"review_bug_count": gorm.Expr("review_bug_count + ?", 1),
		}).Return(nil)

		// 创建CheckOption实例
		checkOption := &CheckOption{
			BaseOption: BaseOption{
				ID:   "check",
				Name: "审核",
			},
			igCheckModel: mockCheckModel,
			igCountModel: mockCountModel,
			bugModel:     mockBugModel,
			cveModel:     mockCveModel,
		}

		// 执行
		ctx, _ := gin.CreateTestContext(nil)
		result, err := checkOption.Execute(ctx, opUser, req)

		// 断言
		assert.NoError(t, err)
		assert.Nil(t, result)
		mockBugModel.AssertExpectations(t)
		mockCheckModel.AssertExpectations(t)
		mockCountModel.AssertExpectations(t)
	})

}
