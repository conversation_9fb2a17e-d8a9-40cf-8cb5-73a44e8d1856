/*
 * @Author: lwq <EMAIL>
 * @Date: 2025-07-08
 * @LastEditors: lwq <EMAIL>
 * @LastEditTime: 2025-07-31
 * @FilePath: /ctdy-guard/slam/task/integration/node/nodes.go
 * @Description: 定义所有节点，节点的ID为全局常量，对应集成单的状态
 * @Description: 定义各个节点的可执行操作
 * @Copyright (c) 2025 by 2022.kylinos.cn, All Rights Reserved.
 */
package node

import (
	"slam/global"
	"slam/task/integration/option"
)

type NodeComparing struct {
	BaseNode
}

// NewNodeToCreate 校对中节点, 允许创建、添加、删除、补货操作
func NewNodeComparing() *NodeComparing {
	return &NodeComparing{
		BaseNode: BaseNode{
			ID:   global.INTEGRATION_STATUS_COMPARE,
			Name: "校对中",
			AllowedOptions: []int{
				option.CreateOptionID,
				option.UpdateOptionID,
				option.AddOptionId,
				option.RemoveOptionId,
				option.ReplenishOptionID,
				option.RotateOptionID,
			},
		},
	}
}

type NodeCreating struct {
	BaseNode
}

// NewNodeCreating 创建中节点
func NewNodeCreating() *NodeCreating {
	return &NodeCreating{
		BaseNode: BaseNode{
			ID:   global.INTEGRATION_STATUS_CREATING,
			Name: "创建中",
			AllowedOptions: []int{
				option.UpdateOptionID,
				option.ReviewOptionID,
				option.DownloadOptionID,
				option.RotateOptionID,
			},
		},
	}
}

type NodeReviewing struct {
	BaseNode
}

// NewNodeReviewing 创建审核节点
func NewNodeChecking() *NodeReviewing {
	return &NodeReviewing{
		BaseNode: BaseNode{
			ID:   global.INTEGRATION_STATUS_CHECKING,
			Name: "审核中",
			AllowedOptions: []int{
				option.UpdateOptionID,
				option.CheckOptionID,
				option.DownloadOptionID,
				option.RotateOptionID,
			},
		},
	}
}

type NodeTesting struct {
	BaseNode
}

// NewNodeTesting 创建测试节点
func NewNodeTesting() *NodeTesting {
	return &NodeTesting{
		BaseNode: BaseNode{
			ID:   global.INTEGRATION_STATUS_TESTING,
			Name: "测试中",
			AllowedOptions: []int{
				option.UpdateOptionID,
				option.DownloadOptionID,
				option.TestResultOptionID,
				option.RotateOptionID,
			},
		},
	}
}

type NodeRechecking struct {
	BaseNode
}

// NewNodeRechecking 复核节点
func NewNodeRechecking() *NodeRechecking {
	return &NodeRechecking{
		BaseNode: BaseNode{
			ID:   global.INTEGRATION_STATUS_RECHECKING,
			Name: "复核中",
			AllowedOptions: []int{
				option.UpdateOptionID,
				option.ReCheckOptionID,
				option.DownloadOptionID,
				option.RotateOptionID,
				option.ConfirmOptionID,
			},
		},
	}
}

type NodeReleasing struct {
	BaseNode
}

// NewNodeReleasing 创建发布节点
func NewNodeReleasing() *NodeReleasing {
	return &NodeReleasing{
		BaseNode: BaseNode{
			ID:   global.INTEGRATION_STATUS_RELEASING,
			Name: "发布中",
			AllowedOptions: []int{
				option.UpdateOptionID,
				option.DownloadOptionID,
				option.RotateOptionID,
				option.FlagOptionID,
			},
		},
	}
}

type NodePublished struct {
	BaseNode
}

// NewNodePublished 创建发布节点
func NewNodePublished() *NodePublished {
	return &NodePublished{
		BaseNode: BaseNode{
			ID:   global.INTEGRATION_STATUS_PUBLISHED,
			Name: "已发布",
			AllowedOptions: []int{
				option.DownloadOptionID,
			},
		},
	}
}
