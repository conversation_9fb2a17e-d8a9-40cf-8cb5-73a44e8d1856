package koji

import (
	"time"
)

type ParamListBuilds struct {
	CreatedAfter  *time.Time `json:"created_after"`  // 选填参数，不需要就传 nil
	CreatedBefore *time.Time `json:"created_before"` // 选填参数，不需要就传 nil
}

func (p *ParamListBuilds) GetParams() []Value {
	return []Value{ // 以下是函数默认值，根据需求修改
		NewValueNil(),                      // packageID
		NewValueNil(),                      // userID
		NewValueNil(),                      // taskID
		NewValueNil(),                      // prefix
		NewValueNil(),                      // state
		NewValueNil(),                      // volumeID
		NewValueNil(),                      // source
		NewValueTimeOrNil(p.CreatedBefore), // createdBefore
		NewValueTimeOrNil(p.CreatedAfter),  // createdAfter
		NewValueNil(),                      // completeBefore
		NewValueNil(),                      // completeAfter
		NewValueNil(),                      // type
		NewValueNil(),                      // typeInfo
		NewValueNil(),                      // queryOpts
		NewValueNil(),                      // pattern
		NewValueNil(),                      // cgID
	}
}

type ResListBuilds struct {
	BuildId         int       `json:"build_id"`
	CompletionTime  time.Time `json:"completion_time"`
	CompletionTs    float64   `json:"completion_ts"`
	CreationEventId int       `json:"creation_event_id"`
	CreationTime    time.Time `json:"creation_time"`
	CreationTs      float64   `json:"creation_ts"`
	Epoch           string    `json:"epoch"`
	Extra           string    `json:"extra"`
	Name            string    `json:"name"`
	Nvr             string    `json:"nvr"`
	OwnerId         int       `json:"owner_id"`
	OwnerName       string    `json:"owner_name"`
	PackageId       int       `json:"package_id"`
	PackageName     string    `json:"package_name"`
	Release         string    `json:"release"`
	Source          string    `json:"source"`
	StartTime       time.Time `json:"start_time"`
	StartTs         float64   `json:"start_ts"`
	State           int       `json:"state"`
	TaskId          int       `json:"task_id"`
	Version         string    `json:"version"`
	VolumeId        int       `json:"volume_id"`
	VolumeName      string    `json:"volume_name"`
}

type ResponseListBuildsSingle struct {
	Members []Member `xml:"struct>member"`
}

func (r *ResponseListBuildsSingle) GetResListBuilds() ResListBuilds {
	res := ResListBuilds{}
	for _, member := range r.Members {
		switch member.Name {
		case "build_id":
			res.BuildId = member.GetValueInt()
		case "completion_time":
			res.CompletionTime = member.GetValueTime()
		case "completion_ts":
			res.CompletionTs = member.GetValueFloat()
		case "creation_event_id":
			res.CreationEventId = member.GetValueInt()
		case "creation_time":
			res.CreationTime = member.GetValueTime()
		case "creation_ts":
			res.CreationTs = member.GetValueFloat()
		case "epoch":
			res.Epoch = member.GetValueString()
		case "extra":
			res.Extra = member.GetValueString()
		case "name":
			res.Name = member.GetValueString()
		case "nvr":
			res.Nvr = member.GetValueString()
		case "owner_id":
			res.OwnerId = member.GetValueInt()
		case "owner_name":
			res.OwnerName = member.GetValueString()
		case "package_id":
			res.PackageId = member.GetValueInt()
		case "package_name":
			res.PackageName = member.GetValueString()
		case "release":
			res.Release = member.GetValueString()
		case "source":
			res.Source = member.GetValueString()
		case "start_time":
			res.StartTime = member.GetValueTime()
		case "start_ts":
			res.StartTs = member.GetValueFloat()
		case "state":
			res.State = member.GetValueInt()
		case "task_id":
			res.TaskId = member.GetValueInt()
		case "version":
			res.Version = member.GetValueString()
		case "volume_id":
			res.VolumeId = member.GetValueInt()
		case "volume_name":
			res.VolumeName = member.GetValueString()
		}
	}
	return res
}

type RequestListBuilds struct {
	RequestBase
	Params ParamsExample `xml:"params"`
}

type ResponseListBuilds struct {
	ResponseBase
	Data []ResponseListBuildsSingle `xml:"params>param>value>array>data>value"`
}

func (resp *ResponseListBuilds) GetRes() []ResListBuilds {
	var res []ResListBuilds
	for _, data := range resp.Data {
		res = append(res, data.GetResListBuilds())
	}
	return res
}

func (s *Client) ListBuilds(param ParamListBuilds) ([]ResListBuilds, error) {
	request := RequestListBuilds{
		RequestBase: RequestBase{Method: "listBuilds"},
		Params:      ParamsExample{Param: param.GetParams()},
	}
	response := ResponseListBuilds{}
	err := s.doPost(request, &response)
	return response.GetRes(), err
}
