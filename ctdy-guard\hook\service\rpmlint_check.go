package service

import (
	"bufio"
	"errors"
	"fmt"
	"os"
	"os/exec"
	"path/filepath"
	"regexp"
	"slam-hook/global"
	"slam-hook/logs"
	"slam-hook/model"
	"slam-hook/tools"
	"strings"
)

// HookRpmlintCheck rpmlint 工具检测rpm包
func (hp *HookParam) HookRpmlintCheck() {
	logs.Log.Infof("start %d rpmlint check", hp.XflowId)
	res := make(map[string]interface{})
	defer func() {
		if r := recover(); r != nil {
			logs.Log.Error("HookRpmlintCheck panic，reason：", r)
			res["hook_result"] = global.HOOK_TASK_RESULT_ERROR
			res["status"] = global.HOOK_TASK_STATUS_FAILED
			err := model.UpdateHookResultById(hp.Id, res)
			tools.CheckUpdateHookResultErr("HookRpmlintCheck", err)
			return
		}
	}()
	if strings.Contains(hp.SrpmName, ".deb") {
		res["hook_result"] = global.HOOK_TASK_RESULT_PASS
		res["status"] = global.HOOK_TASK_STATUS_CLOSED
		res["hook_details"] = "deb包不进行rpmlint检测"
		err := model.UpdateHookResultById(hp.Id, res)
		tools.CheckUpdateHookResultErr("HookRpmlintCheck", err)
		return
	}

	filePath := hp.FilePath
	cleanCmdStr := fmt.Sprintf("rpmlint %s", filePath)
	cleanCmd := exec.Command("bash", "-c", cleanCmdStr)
	cleanCmd.Dir = filepath.Dir(filePath)
	output, err := cleanCmd.CombinedOutput()

	var exitErr *exec.ExitError
	if errors.As(err, &exitErr) {
		if exitErr.ExitCode() != 64 {
			logs.Log.Error("执行rpmlint失败, ", err.Error())
			res["hook_result"] = global.HOOK_TASK_RESULT_ERROR
			res["status"] = global.HOOK_TASK_STATUS_FAILED
			res["hook_details"] = "rpmlint执行失败"
			err = model.UpdateHookResultById(hp.Id, res)
			tools.CheckUpdateHookResultErr("HookRpmlintCheck", err)
			return
		}
	}

	repoPath := filepath.Join(filepath.Dir(filePath), hp.SrpmName[0:len(hp.SrpmName)-4]+".txt")
	err = os.WriteFile(repoPath, output, 0644)
	if err != nil {
		logs.Log.Error("rpmlint日志保存失败, ", err.Error())
		res["hook_result"] = global.HOOK_TASK_RESULT_ERROR
		res["status"] = global.HOOK_TASK_STATUS_FAILED
		res["hook_details"] = "rpmlint日志保存失败"
		err = model.UpdateHookResultById(hp.Id, res)
		tools.CheckUpdateHookResultErr("HookRpmlintCheck", err)
		return
	}
	logInfo, resMap := ParseRpmlintLog(repoPath)
	if resMap["errors"] > "0" {
		res["hook_result"] = global.HOOK_TASK_RESULT_FAIL
		res["status"] = global.HOOK_TASK_STATUS_FAILED
		res["hook_details"] = fmt.Sprintf("软件包存在错误问题：%s 个，警告问题：%s 个, 报错详情:%s", resMap["errors"], resMap["warnings"], logInfo)
	} else {
		res["hook_result"] = global.HOOK_TASK_RESULT_PASS
		res["status"] = global.HOOK_TASK_STATUS_CLOSED
		res["hook_details"] = fmt.Sprintf("软件包存在错误问题：%s 个，警告问题：%s 个, 报错详情:%s", resMap["errors"], resMap["warnings"], logInfo)
	}
	err = model.UpdateHookResultById(hp.Id, res)
	tools.CheckUpdateHookResultErr("HookRpmlintCheck", err)
	return
}

// ParseRpmlintLog 解析rpmlint 日志
func ParseRpmlintLog(logPath string) (string, map[string]string) {
	file, err := os.Open(logPath)
	if err != nil {
		logs.Log.Error(err)
	}
	defer file.Close()
	var resultList []string
	var isResult bool
	scanner := bufio.NewScanner(file)
	for scanner.Scan() {
		line := scanner.Text()
		fmt.Println(line)
		if !isResult {
			pattern := `^checks: \d+, packages: \d+$` // ^ 和 $ 表示行首行尾
			re := regexp.MustCompile(pattern)
			if re.MatchString(line) {
				isResult = true
			}
		} else {
			resultList = append(resultList, line)
		}
	}
	if err = scanner.Err(); err != nil {
		logs.Log.Error("读取错误:", err)
	}
	resultStr := resultList[len(resultList)-1]
	resList := strings.Split(resultStr, ";")
	// 获取结果数据
	re := regexp.MustCompile(`(\d+)\s+(errors|warnings|filtered|badness)`)
	matches := re.FindAllStringSubmatch(resList[1], -1)
	resultMap := map[string]string{}
	// 遍历匹配结果
	for _, match := range matches {
		count := match[1]
		category := match[2] // 类型（errors/warnings/filtered/badness）
		resultMap[category] = count
		fmt.Printf("%s: %s\n", category, count)
	}
	if len(resultList) > 3 {
		resultList = resultList[1 : len(resultList)-1]
	}
	return strings.Join(resultList, "\n"), resultMap
}
