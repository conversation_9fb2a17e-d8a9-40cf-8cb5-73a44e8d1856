/*
 * @Author: lwq <EMAIL>
 * @Date: 2025-07-08
 * @LastEditors: lwq <EMAIL>
 * @LastEditTime: 2025-07-16
 * @FilePath: /ctdy-guard/slam/task/integration/option/review.go
 * @Description: 集成操作
 * @Copyright (c) 2025 by 2022.kylinos.cn, All Rights Reserved.
 */
package option

import "slam/model"

// ReviewOption 初审操作 直接继承审核的方法
type ReviewOption struct {
	CheckOption
}

func NewReviewOption() *ReviewOption {
	return &ReviewOption{
		CheckOption: CheckOption{
			BaseOption: BaseOption{
				ID:   "review",
				Name: "初审",
			},
			igCheckModel: model.NewIntegrationCheckModel(),
			igCountModel: model.NewIntegrationCountModel(),
			bugModel:     model.NewIntegrationBugModel(),
			cveModel:     model.NewIntegrationCveModel(),
		},
	}
}
