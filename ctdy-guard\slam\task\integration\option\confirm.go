/*
 * @Author: lwq <EMAIL>
 * @Date: 2025-07-08
 * @LastEditors: lwq <EMAIL>
 * @LastEditTime: 2025-07-27
 * @FilePath: /ctdy-guard/slam/task/integration/option/confirm.go
 * @Description: 集成操作
 * @Copyright (c) 2025 by 2022.kylinos.cn, All Rights Reserved.
 */
package option

import (
	"slam/model"
	"slam/slog"

	"github.com/gin-gonic/gin"
)

// ConfirmOption 一键确认软件包操作
type ConfirmOption struct {
	BaseOption
	igModel      model.IntegrationRepo
	igCountModel model.IntegrationCountRepo
}

func NewConfirmOption() *ConfirmOption {
	return &ConfirmOption{
		BaseOption: BaseOption{
			ID:   "confirm",
			Name: "确认集成单",
		},
		igModel:      model.NewIntegrationModel(),
		igCountModel: model.NewIntegrationCountModel(),
	}
}

func (o *ConfirmOption) Execute(c *gin.Context, opUser *OpUser, params any) (any, error) {
	// 不用校验是否完成审核
	igInfo := opUser.OpIgInfo
	// 获取集成单统计数据
	igCount, err := o.igCountModel.GetIntegrationCount(igInfo.Id, igInfo.Status)
	if err != nil {
		slog.ErrorWithContext(c, "查询统计数据报错")
		return false, nil
	}

	// 更新当前状态的统计数据，标记所有审核通过
	igCount.ReviewBugCount = igCount.TotalBugCount
	igCount.ReviewCveCount = igCount.TotalCveCount
	err = o.igCountModel.UpdateCountInfo(igInfo.Id, igInfo.Status, map[string]any{
		"review_bug_count": igCount.TotalBugCount,
		"review_cve_count": igCount.TotalCveCount,
	})
	if err != nil {
		slog.ErrorWithContext(c, "更新集成单统计数据失败", err)
		return nil, err
	}
	return nil, nil
}
