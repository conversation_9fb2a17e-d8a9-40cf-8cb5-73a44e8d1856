package role

import (
	"slam/task/integration/option"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestBaseRoleGetID(t *testing.T) {
	role := &BaseRole{
		ID:   1,
		Name: "",
	}

	assert.Equal(t, 1, role.GetID())
}

func TestBaseRoleGetName(t *testing.T) {
	role := &BaseRole{
		ID:   1,
		Name: "Role",
	}

	assert.Equal(t, "Role", role.GetName())
}

func TestBaseRoleCheckAccess(t *testing.T) {
	role := &BaseRole{
		ID:   1,
		Name: "Role1",
		AllowedOptions: []int{
			option.CreateOptionID,
			option.UpdateOptionID,
		},
	}

	// 测试允许的操作
	assert.True(t, role.CheckAccess(option.CreateOptionID))
	assert.True(t, role.CheckAccess(option.UpdateOptionID))

	// 测试不允许的操作
	assert.False(t, role.CheckAccess(option.DownloadOptionID))
	assert.False(t, role.CheckAccess(option.ReviewOptionID))
}

func TestGetRoles(t *testing.T) {
	roles := GetRoles()

	// 检查是否返回了所有角色
	assert.Len(t, roles, 4)

	// 检查各个角色是否存在且类型正确
	assert.Implements(t, (*RoleInterface)(nil), roles[ArchitectRoleID])
	assert.Implements(t, (*RoleInterface)(nil), roles[TechManagerRoleID])
	assert.Implements(t, (*RoleInterface)(nil), roles[ProjectManagerRoleID])
	assert.Implements(t, (*RoleInterface)(nil), roles[RepoManagerRoleID])

	// 检查角色ID是否正确
	assert.Equal(t, ArchitectRoleID, roles[ArchitectRoleID].GetID())
	assert.Equal(t, TechManagerRoleID, roles[TechManagerRoleID].GetID())
	assert.Equal(t, ProjectManagerRoleID, roles[ProjectManagerRoleID].GetID())
	assert.Equal(t, RepoManagerRoleID, roles[RepoManagerRoleID].GetID())

	// 检查角色名称是否正确
	assert.Equal(t, "架构负责人", roles[ArchitectRoleID].GetName())
	assert.Equal(t, "技术经理", roles[TechManagerRoleID].GetName())
	assert.Equal(t, "项目经理", roles[ProjectManagerRoleID].GetName())
	assert.Equal(t, "仓库管理员", roles[RepoManagerRoleID].GetName())
}

func TestArchitectRole(t *testing.T) {
	role := NewArchitectRole()

	assert.Equal(t, ArchitectRoleID, role.GetID())
	assert.Equal(t, "架构负责人", role.GetName())

	// 测试架构负责人权限
	assert.True(t, role.CheckAccess(option.CreateOptionID))
	assert.True(t, role.CheckAccess(option.ReviewOptionID))
	assert.True(t, role.CheckAccess(option.UpdateOptionID))
	assert.True(t, role.CheckAccess(option.DownloadOptionID))

	// 测试非架构负责人权限
	assert.False(t, role.CheckAccess(option.CheckOptionID))
	assert.False(t, role.CheckAccess(option.ReCheckOptionID))
	assert.False(t, role.CheckAccess(option.ConfirmOptionID))
}

func TestTechManagerRole(t *testing.T) {
	role := NewTechManagerRole()

	assert.Equal(t, TechManagerRoleID, role.GetID())
	assert.Equal(t, "技术经理", role.GetName())

	// 测试技术经理权限
	assert.True(t, role.CheckAccess(option.CheckOptionID))
	assert.True(t, role.CheckAccess(option.DownloadOptionID))
	assert.True(t, role.CheckAccess(option.UpdateOptionID))

	// 测试非技术经理权限
	assert.False(t, role.CheckAccess(option.CreateOptionID))
	assert.False(t, role.CheckAccess(option.ReviewOptionID))
	assert.False(t, role.CheckAccess(option.ReCheckOptionID))
	assert.False(t, role.CheckAccess(option.ConfirmOptionID))
}

func TestProjectManagerRole(t *testing.T) {
	role := NewProjectManagerRole()

	assert.Equal(t, ProjectManagerRoleID, role.GetID())
	assert.Equal(t, "项目经理", role.GetName())

	// 测试项目经理权限
	assert.True(t, role.CheckAccess(option.ReCheckOptionID))
	assert.True(t, role.CheckAccess(option.DownloadOptionID))
	assert.True(t, role.CheckAccess(option.UpdateOptionID))
	assert.True(t, role.CheckAccess(option.ConfirmOptionID))

	// 测试非项目经理权限
	assert.False(t, role.CheckAccess(option.CreateOptionID))
	assert.False(t, role.CheckAccess(option.ReviewOptionID))
	assert.False(t, role.CheckAccess(option.CheckOptionID))
}

func TestRepoManagerRole(t *testing.T) {
	role := NewRepoManagerRole()

	assert.Equal(t, RepoManagerRoleID, role.GetID())
	assert.Equal(t, "仓库管理员", role.GetName())

	// 测试仓库管理员权限 - 应该有所有权限
	assert.True(t, role.CheckAccess(option.CreateOptionID))
	assert.True(t, role.CheckAccess(option.UpdateOptionID))
	assert.True(t, role.CheckAccess(option.ReplenishOptionID))
	assert.True(t, role.CheckAccess(option.AddOptionId))
	assert.True(t, role.CheckAccess(option.RemoveOptionId))
	assert.True(t, role.CheckAccess(option.TestResultOptionID))
	assert.True(t, role.CheckAccess(option.RotateOptionID))
	assert.True(t, role.CheckAccess(option.DownloadOptionID))
	assert.True(t, role.CheckAccess(option.LockOptionID))
	assert.True(t, role.CheckAccess(option.UnlockOptionID))
	assert.True(t, role.CheckAccess(option.FlagOptionID))

}
