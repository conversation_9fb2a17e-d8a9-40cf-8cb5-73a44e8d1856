package service

import (
	"encoding/json"
	"fmt"
	"log"
	"slam-hook/config"
	"slam-hook/global"
	"slam-hook/logs"
	"slam-hook/model"
	"slam-hook/service/utils_rpm_test_check/client"
	"slam-hook/service/utils_rpm_test_check/params"
	"slam-hook/tools"
	"strings"
	"time"
)

func HookRpmTestCheckCheckRes(taskInfoList []params.TaskInfo, err error) (string, string) {
	if err != nil {
		if strings.Contains(err.Error(), "未配置自动化测试") {
			return "0", "未配置自动化测试"
		}
		return "1", fmt.Sprintf("自动化任务创建失败，原因：%v", err.Error())
	}
	taskInfoListBytes, _ := json.Marshal(taskInfoList)
	taskInfoListStr := string(taskInfoListBytes)
	if params.TaskInfoList(taskInfoList).IsAllSuccess() {
		return "0", taskInfoListStr
	}
	return "1", taskInfoListStr
}

func (hp *HookParam) GetKojiAddrTaskInfo() (string, error) {
	if hp.KojiAddr != "" {
		return hp.KojiAddr, nil
	}
	task, _ := model.GetStockTagBuildTask(hp.XflowId, hp.SrpmName, hp.TagKoji)
	if task != "" {
		return task, nil
	}
	return GetTagBuildTaskId(hp.TagKoji, hp.SrpmName, hp.XflowId)
}

// HookRpmTestCheck  入库软件包触发测试部自动化测试
func (hp *HookParam) HookRpmTestCheck() {
	defer func() {
		if r := recover(); r != nil {
			logs.Log.Error("HookRpmTestCheck panic，reason：", r)
		}
	}()
	var resultCode, resultMessage string
	if hp.TagAction == "add" {
		c := client.NewClient(client.WithUrlBase(config.InnerTestUrl))
		kojiAddr, _ := hp.GetKojiAddrTaskInfo()
		taskInfoList, err := c.KojiTaskCreateAndWait(
			params.ParamKojiTaskCreate{LinkKojiTask: kojiAddr, Tag: hp.Tag},
			func() {
				logs.Log.Debug(fmt.Sprintf("获取 %d 流程软件包 %s 的入库自动化测试结果中...", hp.XflowId, hp.SrpmName))
				log.Println(fmt.Sprintf("获取 %d 流程软件包 %s 的入库自动化测试结果中...", hp.XflowId, hp.SrpmName))
				time.Sleep(60 * time.Second)
			},
		)
		resultCode, resultMessage = HookRpmTestCheckCheckRes(taskInfoList, err)
	} else {
		resultCode, resultMessage = "0", "非软件包[入库]行为，不进行自动化测试"
	}

	res := map[string]interface{}{"hook_details": resultMessage, "hook_msg": "将编译出来的二进制包构建成仓库，并调用测试部接口进行测试"}
	switch resultCode {
	case "1":
		res["hook_result"] = global.HOOK_TASK_RESULT_FAIL
		res["status"] = global.HOOK_TASK_STATUS_CLOSED
	default:
		res["hook_result"] = global.HOOK_TASK_RESULT_PASS
		res["status"] = global.HOOK_TASK_STATUS_CLOSED
	}
	tools.CheckUpdateHookResultErr("HookRpmTestCheck", model.UpdateHookResultById(hp.Id, res))
	return
}
