package option

import (
	"slam/model"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"gorm.io/gorm"
)

func TestFlagOptionExecute(t *testing.T) {
	NewFlagOption()
	gin.SetMode(gin.TestMode)

	t.Run("successful flag bug as unpublish", func(t *testing.T) {
		// Mocks
		mockCheckModel := new(MockIntegrationCheckRepo)
		mockCountModel := new(MockIntegrationCountRepo)
		mockBugModel := new(MockIntegrationRowsRepo)
		mockCveModel := new(MockIntegrationRowsRepo)

		// Test data
		req := &CheckReq{
			Id:          1,
			IgId:        10,
			CheckResult: model.IntegrationCheckUnPass, // 标记为不发布
			CheckAdvice: "Not ready for publish",
			CheckType:   model.IntegrationCheckTypeBug,
		}

		opUser := &OpUser{
			UserInfo: UserInfo{
				UserId:   1,
				UserName: "testuser",
				Email:    "<EMAIL>",
			},
			OpIgInfo: &model.Integration{
				Status: 1,
			},
		}

		// 设置mock期望
		mockBugModel.On("UpdateWithMap", 1, map[string]any{"is_publish": model.IntegrationCheckUnPass}).Return(int(1), nil)
		mockCheckModel.On("CreateIntegrationCheck", mock.AnythingOfType("[]*model.IntegrationCheck")).Return(nil)
		mockCountModel.On("UpdateCountInfo", 10, 1, map[string]any{
			"unpublish_count": gorm.Expr("unpublish_count + ?", 1),
		}).Return(nil)

		// 创建FlagOption实例
		flagOption := &FlagOption{
			CheckOption: CheckOption{
				BaseOption: BaseOption{
					ID:   "flag",
					Name: "标记软件包",
				},
				igCheckModel: mockCheckModel,
				igCountModel: mockCountModel,
				bugModel:     mockBugModel,
				cveModel:     mockCveModel,
			},
		}

		// 执行
		ctx, _ := gin.CreateTestContext(nil)
		result, err := flagOption.Execute(ctx, opUser, req)

		// 断言
		assert.NoError(t, err)
		assert.Nil(t, result)
		mockBugModel.AssertExpectations(t)
		mockCheckModel.AssertExpectations(t)
		mockCountModel.AssertExpectations(t)
	})

	t.Run("successful flag cve as publish", func(t *testing.T) {
		// Mocks
		mockCheckModel := new(MockIntegrationCheckRepo)
		mockCountModel := new(MockIntegrationCountRepo)
		mockBugModel := new(MockIntegrationRowsRepo)
		mockCveModel := new(MockIntegrationRowsRepo)

		// Test data
		req := &CheckReq{
			Id:          2,
			IgId:        10,
			CheckResult: model.IntegrationCheckPass, // 标记为发布
			CheckAdvice: "Ready for publish",
			CheckType:   model.IntegrationCheckTypeCve,
		}

		opUser := &OpUser{
			UserInfo: UserInfo{
				UserId:   1,
				UserName: "testuser",
				Email:    "<EMAIL>",
			},
			OpIgInfo: &model.Integration{
				Status: 1,
			},
		}

		// 设置mock期望
		mockCveModel.On("UpdateWithMap", 2, map[string]any{"is_publish": model.IntegrationCheckPass}).Return(int(1), nil)
		mockCheckModel.On("CreateIntegrationCheck", mock.AnythingOfType("[]*model.IntegrationCheck")).Return(nil)
		mockCountModel.On("UpdateCountInfo", 10, 1, map[string]any{
			"unpublish_count": gorm.Expr("unpublish_count - ?", 1),
		}).Return(nil)

		// 创建FlagOption实例
		flagOption := &FlagOption{
			CheckOption: CheckOption{
				BaseOption: BaseOption{
					ID:   "flag",
					Name: "标记软件包",
				},
				igCheckModel: mockCheckModel,
				igCountModel: mockCountModel,
				bugModel:     mockBugModel,
				cveModel:     mockCveModel,
			},
		}

		// 执行
		ctx, _ := gin.CreateTestContext(nil)
		result, err := flagOption.Execute(ctx, opUser, req)

		// 断言
		assert.NoError(t, err)
		assert.Nil(t, result)
		mockCveModel.AssertExpectations(t)
		mockCheckModel.AssertExpectations(t)
		mockCountModel.AssertExpectations(t)
	})
}