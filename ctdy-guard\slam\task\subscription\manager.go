/*
 * @Author: lwq <EMAIL>
 * @Date: 2025-04-21
 * @LastEditors: lwq <EMAIL>
<<<<<<< HEAD
 * @LastEditTime: 2025-04-24
=======
 * @LastEditTime: 2025-04-29
>>>>>>> origin/slam
 * @FilePath: /ctdy-guard/slam/task/subscription/manager.go
 * @Description: 订阅相关,负责通知的发送和管理
 * @Copyright (c) 2025 by 2022.kylinos.cn, All Rights Reserved.
 */
package subscription

import (
	"errors"
	"slam/model"
	. "slam/task/subscription/kernel"
)

// Manager 订阅管理器
type Manager struct {
	channels      map[ChannelType]Channel           // 注册的通知渠道
	rules         []*model.SubscribeRule            // 本次需要执行的规则
	noticeTypes   map[NotificationType]Notification // 通知类型
	subRuleModel  model.SubscribeRuleRepo           // 订阅规则模型
	templateModel model.TemplateRepo
}

// NewManager 创建订阅管理器
func NewManager() *Manager {
	return &Manager{
		channels:      map[ChannelType]Channel{},
		rules:         []*model.SubscribeRule{},
		noticeTypes:   map[NotificationType]Notification{},
		subRuleModel:  model.NewSubscribeRule(),
		templateModel: model.NewTemplate(),
	}
}

/**
 * @author: lwq <EMAIL>
 * @description: RegisterChannel 注册通知渠道
 * @param {Channel} channel
 * @return {nil}
 */
func (m *Manager) RegisterChannel(channel Channel) {
	m.channels[channel.GetType()] = channel
}

/**
 * @author: lwq <EMAIL>
 * @description: RegisterNoticeType 注册通知类型
 * @param {Notification} noticeType
 * @return {nil}
 */
func (m *Manager) RegisterNoticeType(noticeType Notification) {
	m.noticeTypes[noticeType.GetType()] = noticeType
}

/**
 * @author: lwq <EMAIL>
 * @description: InitManager 初始化管理器
 *   初始化通知类型和通知渠道
 *   从数据库中获取所有需要发送消息的规则
 * @return {nil}
 */
func (m *Manager) InitManager() {
	// 注册通知类型和通知渠道
	m.noticeTypes = map[NotificationType]Notification{
		NoticeTypeAutoStock: NewAutoStockNotice(),
	}
	m.channels = map[ChannelType]Channel{
		ChannelLanXin: NewLanXinChannel(),
	}
	// 从数据库中获取所有需要发送消息的规则
	rules, err := m.subRuleModel.GetRulesByCurrentTime()
	if err != nil {
		return
	}

	m.rules = rules
}

/**
 * @author: lwq <EMAIL>
 * @description: Notify 发送通知 需要指定用户名
 * @return {error}
 */
func (m *Manager) Notify() error {
	// 遍历所有规则, 是否需要通知 （需要优化规则查询逻辑，不要全量查询）
	for _, rule := range m.rules {
		//获取 NoticeType 的实例，并调用其 GetTemplate 方法获取模板内容
		notice, ok := m.noticeTypes[NotificationType(rule.NotifyType)]
		if !ok {
			return errors.New("未定义的通知类型")
		}
		template := m.GetTemplate(rule.TemplateId)
		if template == nil {
			return errors.New("未定义的通知模板")
		}
		template = notice.SetTemplateData(template)
		// 获取 ChannelType 的实例，并调用其 Rander 和 SendToUser 方法发送通知
		channel, ok := m.channels[ChannelType(rule.ChannelType)]
		if !ok {
			return errors.New("未定义的通知渠道")
		}

		channel.SendToUser(template, rule.UserID)
	}

	return nil
}

/**
 * @author: lwq <EMAIL>
 * @description: GetTemplate 获取模板
 *   目前只支持获取数据库中的模板
 * @param {int} templateId
 * @return {*Template}
 */
func (m *Manager) GetTemplate(templateId int) *Template {
	// 需要用通道和通知类型获取模板
	template, _ := m.templateModel.GetTemplateById(templateId)
	if template != nil {
		return &Template{
			TemplateType:     TemplateType(template.TemplateType),
			NotificationType: NotificationType(template.NoticeType),
			Content:          template.Content,
			Data:             map[string]interface{}{},
		}
	} else {
		return nil
	}
}
