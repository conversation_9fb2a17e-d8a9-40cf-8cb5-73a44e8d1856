/*
 * @Author: lwq <EMAIL>
 * @Date: 2025-07-08
 * @LastEditors: lwq <EMAIL>
 * @LastEditTime: 2025-07-29
 * @FilePath: /ctdy-guard/slam/task/integration/option/replenish.go
 * @Description: 集成操作
 * @Copyright (c) 2025 by 2022.kylinos.cn, All Rights Reserved.
 */
package option

import (
	"encoding/json"
	"errors"
	"slam/global"
	"slam/model"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type ReplenishReq struct {
	IgId    int
	SrcName string
	BugInfo string
	CveInfo string
}

// ReplenishOption 补充集成单操作
type ReplenishOption struct {
	BaseOption
	irModel      model.IntegrationReplenishRepo
	igCountModel model.IntegrationCountRepo
	igBugModel   model.IntegrationRowsRepo
	igCveModel   model.IntegrationRowsRepo
}

func NewReplenishOption() *ReplenishOption {
	return &ReplenishOption{
		BaseOption: BaseOption{
			ID:   "replenish",
			Name: "补录软件包",
		},
		irModel:      model.NewIntegrationReplenishModel(),
		igCountModel: model.NewIntegrationCountModel(),
		igBugModel:   model.NewIntegrationBugModel(),
		igCveModel:   model.NewIntegrationCveModel(),
	}
}

func (o *ReplenishOption) Execute(c *gin.Context, opUser *OpUser, params any) (any, error) {
	// 解析bug或者cve信息，插入到数据库
	req := params.(*ReplenishReq)

	replenish := []*model.IntegrationReplenish{{
		SrcName: req.SrcName,
		BugInfo: req.BugInfo,
		CveInfo: req.CveInfo,
	}}
	err := o.irModel.BatchInsertReplenish(replenish)
	if err != nil {
		return "", err
	}
	bugs, cves, err := o.addBugsOrCves(opUser, req)
	if err != nil {
		return "", err
	}
	// 更新统计数据
	umap := map[string]any{
		"total_count":     gorm.Expr("total_count + ?", bugs+cves),
		"total_bug_count": gorm.Expr("total_bug_count + ?", bugs),
		"total_cve_count": gorm.Expr("total_cve_count + ?", cves),
	}
	err = o.igCountModel.UpdateCountInfo(req.IgId, opUser.OpIgInfo.Status, umap)
	if err != nil {
		return "", err
	}

	return "", nil
}

/**
 * @author: lwq <EMAIL>
 * @description: 补录的数据添加到数据库中
 * @param {*OpUser} user
 * @param {*ReplenishReq} req
 * @return {int, int, error}
 */
func (o *ReplenishOption) addBugsOrCves(user *OpUser, req *ReplenishReq) (int, int, error) {
	bugs := []model.BugInfo{}
	cves := []model.CveInfo{}
	//构建参数，处理bug和cve入参
	list := &model.StockBugCveList{
		XflowId:  req.IgId,
		Type:     o.ID,
		CveInfo:  cves,
		BugInfo:  bugs,
		UserId:   user.UserId,
		UserName: user.UserName,
	}
	bq := &model.BugCveListReq{
		IgId:   req.IgId,
		Source: global.INTEGRATION_SOURCE_REPLENISH,
	}
	igBugs := []*model.IntegrationBug{}
	igCves := []*model.IntegrationCve{}
	var err error

	// 解析 json 字符串
	if len(req.BugInfo) > 0 {
		err = json.Unmarshal([]byte(req.BugInfo), &bugs)
		if err != nil {
			return 0, 0, errors.New("参数解析错误")
		}
		list.BugInfo = bugs
		igBugs = model.GetIntegrationBugList(list, bq)
		err = o.igBugModel.BatchInsertIntegration(igBugs)
		if err != nil {
			return 0, 0, err
		}
	}

	if len(req.CveInfo) > 0 {
		err = json.Unmarshal([]byte(req.CveInfo), &cves)
		if err != nil {
			return len(igBugs), 0, errors.New("参数解析错误")
		}
		list.CveInfo = cves
		igCves = model.GetIntegrationCveList(list, bq)
		err = o.igCveModel.BatchInsertIntegration(igCves)
		if err != nil {
			return len(igBugs), 0, err
		}
	}

	return len(igBugs), len(igCves), nil
}
